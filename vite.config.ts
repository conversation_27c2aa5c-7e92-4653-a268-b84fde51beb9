import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { VitePWA } from "vite-plugin-pwa";

// https://vitejs.dev/config/
export default defineConfig({
  server: {
    host: "127.0.0.1",
    port: 5173,
  },
  plugins: [
    react(),
    VitePWA({
      /** use your own file instead of workbox-generated one */
      strategies: "injectManifest",
      srcDir: "src",
      filename: "sw.ts",

      /** still precache everything Vite outputs except the MP4s */
      injectManifest: {
        globPatterns: ["**/*.{js,css,html,ico,png,svg}"],
      },

      /** dev-time worker so you can test offline in `vite dev` */
      devOptions: { enabled: true, type: "module" },

      /** nicer update UX (optional) */
      registerType: "prompt",
    }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        entryFileNames: "assets/[name]-[hash].js",
        chunkFileNames: "assets/[name]-[hash].js",
        assetFileNames: "assets/[name]-[hash].[ext]",
      },
    },
    emptyOutDir: true,
    manifest: true,
  },
  optimizeDeps: {
    include: ["react", "react-dom"],
  },
});
