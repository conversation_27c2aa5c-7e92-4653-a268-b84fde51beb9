// Random SHA-256, because the user is unlikely to name his video like this.

import { PresetConfig } from "./remotion/types";

const VIDEO_ACTIONS_MAP = {
  NEW_EDIT: "new-edit",
  VIDEO_EDITS: "video-edits",
  VIDEO_DETAILS: "video-details",
};

const EDIT_ACTIONS_MAP = {
  EDIT_DETAILS: "edit-details",
  DELETE_EDIT: "delete-edit",
  EDIT: "edit",
};

const VIDEO_TITLE_MAX_LENGTH = 15;

const FONTS = [
  "Inter",
  "Montserrat",
  "Poppins",
  "Lato",
  "Roboto",
  "N<PERSON><PERSON>",
  "Source Sans Pro",
  "<PERSON>",
  "Lexend Deca",
  "Fira Sans",
];

const DEFAULT_SUBTITLES_WORDS_PER_LINE = 3;

const SUBTITLE_TEMPLATES: PresetConfig[] = [
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Default",
    size: 70,
    font: { family: "Inter", weight: "bold" },
    color: "#FFFFFF",
    stroke: {
      color: "#000000",
      weight: 2,
      opacity: 100,
    },
    shadow: {
      color: "#000000",
      opacity: 80,
      offset: {
        x: 0,
        y: 0,
      },
    },
    highlight_color: null,
    current_word_color: "#FFE100",
    amplified_word_color: "#9BFA29",
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Soft Glow",
    size: 70,
    font: { family: "Montserrat", weight: "bold" },
    color: "#FFFFFF",
    stroke: null,
    shadow: {
      color: "#000000",
      opacity: 50,
      offset: {
        x: 0,
        y: 3,
      },
    },
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Sharp Contrast",
    size: 70,
    font: { family: "Poppins", weight: "bold" },
    color: "#000000",
    stroke: {
      color: "#FFFFFF",
      weight: 4,
      opacity: 50,
    },
    shadow: null,
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Bold Wave",
    size: 70,
    font: { family: "Lato", weight: "bold" },
    color: "#FFFFFF",
    stroke: {
      color: "#000000",
      weight: 6,
      opacity: 100,
    },
    shadow: null,
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: "#83D2FF",
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Golden Edge",
    size: 70,
    font: { family: "Fira Sans", weight: "bold" },
    color: "#FFE100",
    stroke: {
      color: "#000000",
      weight: 3,
      opacity: 100,
    },
    shadow: null,
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Blush",
    size: 70,
    font: { family: "Roboto", weight: "bold" },
    color: "#FFFFFF",
    stroke: null,
    shadow: null,
    highlight_color: null,
    current_word_color: "#FFBAE7",
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Sunflare",
    size: 70,
    font: { family: "Nunito", weight: "bold" },
    color: "#FFB31B",
    stroke: {
      color: "#000000",
      weight: 4,
      opacity: 100,
    },
    shadow: null,
    highlight_color: "#FFFFFF",
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Soft Drop",
    size: 70,
    font: { family: "Source Sans Pro", weight: "bold" },
    color: "#FFFFFF",
    stroke: null,
    shadow: {
      color: "#000000",
      opacity: 40,
      blur: 6,
      offset: {
        x: 0,
        y: 0,
      },
    },
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Neon Punch",
    size: 70,
    font: { family: "Oswald", weight: "bold" },
    color: "#AEFA54",
    stroke: {
      color: "#000000",
      weight: 3,
      opacity: 100,
    },
    shadow: null,
    highlight_color: null,
    current_word_color: null,
    amplified_word_color: null,
  },
  {
    words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
    preset_name: "Flash Pop",
    size: 70,
    font: { family: "Lexend Deca", weight: "bold" },
    color: "#FFFFFF",
    stroke: null,
    shadow: null,
    highlight_color: null,
    current_word_color: "#FFE100",
    amplified_word_color: "#FF51B1",
  },
];

const SUBTITLE_STROKE_WIDTH = 3;

const MAX_VIDEO_SIZE = 10368709120;

export {
  VIDEO_ACTIONS_MAP,
  EDIT_ACTIONS_MAP,
  VIDEO_TITLE_MAX_LENGTH,
  FONTS,
  DEFAULT_SUBTITLES_WORDS_PER_LINE,
  SUBTITLE_TEMPLATES,
  SUBTITLE_STROKE_WIDTH,
  MAX_VIDEO_SIZE,
};
