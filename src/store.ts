import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import modalSlice from "./features/modal/modalSlice";
import videosSlice from "./features/videos/videosSlice";
import clipsSlice from "./features/clips/clipsSlice";
import authSlice from "./features/auth/authSlice";
import sseMiddleware from "./features/middleware/sseMiddleware";
import presetsSlice from "./features/presets/presetsSlice";
import resetStateMiddleware from "./features/middleware/redux-reset";

export const store = configureStore({
  reducer: {
    modal: modalSlice,
    videos: videosSlice,
    clips: clipsSlice,
    auth: authSlice,
    presets: presetsSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(sseMiddleware, resetStateMiddleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

setupListeners(store.dispatch);
