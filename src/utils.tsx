import { showNotification } from "@mantine/notifications";
import { VIDEO_TITLE_MAX_LENGTH } from "./constants";
import { ISubtitle, IVideoSegment } from "./remotion/types";
import { ErrorResponse, TimelineZoomLevel } from "./types";
import VFIconComponent from "./components/icon/vf-icon";

const handleErrors = (err: any, errMsg?: string) => {
  showNotification({
    icon: <VFIconComponent type="error" backgroundColor="#FFFFFF" />,
    radius: "md",
    message: err?.data?.detail
      ? Array.isArray(err.data.detail)
        ? err.data.detail.map((error: any) => error.msg).join(", ")
        : err.data.detail
      : errMsg
      ? errMsg
      : "An error occurred",
  });
};

/**
 * Formats time in seconds to "m:ss" format.
 * @param seconds - The time value in seconds
 * @returns Formatted time string in "m:ss" format
 */
function formatClipDuration(seconds: number): string {
  // Calculate minutes and remaining seconds
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  // Format seconds with leading zero if needed
  const formattedSeconds =
    remainingSeconds < 10 ? `0${remainingSeconds}` : `${remainingSeconds}`;

  // Return the formatted time string
  return `${minutes}:${formattedSeconds}`;
}

const formatTime = (timeInSeconds: number, pretty?: boolean): string => {
  const pad = (num: number, size: number) => num.toString().padStart(size, "0");

  const hours = Math.floor(timeInSeconds / 3600);
  const minutes = Math.floor((timeInSeconds % 3600) / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  const milliseconds = Math.floor((timeInSeconds % 1) * 1000);

  // Format as HH:MM:SS.mmm
  return pretty
    ? `${pad(hours, 2)}h:${pad(minutes, 2)}m:${pad(seconds, 2)}s`
    : `${pad(hours, 2)}:${pad(minutes, 2)}:${pad(seconds, 2)}.${pad(
        milliseconds,
        3
      )}`;
};

// function to convert HH:MM:SS.sss to seconds
const parseTime = (time: string): number => {
  const parts = time.split(":");

  return (
    parseInt(parts[0], 10) * 3600 +
    parseInt(parts[1], 10) * 60 +
    parseFloat(parts[2])
  );
};

const formatFileSize = (bytes: number): string => {
  const MB = 1024 * 1024;
  const sizeInMB = bytes / MB;

  if (sizeInMB < 1) {
    return `${sizeInMB.toFixed(2)} MB`;
  }

  if (sizeInMB >= 1000) {
    const GB = sizeInMB / 1024;
    return `${GB.toFixed(1)} GB`;
  }

  // For sizes between 1MB and 999MB
  if (sizeInMB % 1 === 0) {
    return `${sizeInMB.toFixed(0)} MB`;
  }

  return `${sizeInMB.toFixed(1)} MB`;
};

const formatEntityTitle = (title: string, length?: number): string => {
  const maxLength: number = length || VIDEO_TITLE_MAX_LENGTH;

  return title.length > maxLength ? `${title.slice(0, maxLength)}...` : title;
};

const getPreviousPath = (pathname: string): string => {
  const paths = pathname.split("/");
  paths.pop();
  return paths.join("/");
};

const getFrameDimensions = (
  ratio: string,
  videoPlayerDimensions: { width: number; height: number }
): { frameWidth: number; frameHeight: number } => {
  if (ratio === "original") {
    return {
      frameWidth: videoPlayerDimensions.width,
      frameHeight: videoPlayerDimensions.height,
    };
  }

  let frameWidth, frameHeight;
  const containerWidth = videoPlayerDimensions.width;
  const containerHeight = videoPlayerDimensions.height;

  // Calculate both dimensions based on container constraints and pick the one that fits
  switch (ratio) {
    case "16:9":
      // Calculate dimensions both ways
      const w1 = containerWidth;
      const h1 = w1 * (9 / 16);

      const h2 = containerHeight;
      const w2 = h2 * (16 / 9);

      // Choose the dimensions that fit within the container
      if (h1 <= containerHeight) {
        frameWidth = w1;
        frameHeight = h1;
      } else {
        frameWidth = w2;
        frameHeight = h2;
      }
      break;

    case "9:16":
      // Calculate dimensions both ways
      const w9 = containerWidth;
      const h9 = w9 * (16 / 9);

      const h16 = containerHeight;
      const w16 = h16 * (9 / 16);

      // Choose the dimensions that fit within the container
      if (h9 <= containerHeight) {
        frameWidth = w9;
        frameHeight = h9;
      } else {
        frameWidth = w16;
        frameHeight = h16;
      }
      break;

    case "1:1":
      // Use the smaller dimension for square
      const size = Math.min(containerWidth, containerHeight);
      frameWidth = size;
      frameHeight = size;
      break;

    default:
      throw new Error(`Unsupported aspect ratio: ${ratio}`);
  }

  return { frameWidth, frameHeight };
};

const isVideoPortrait = (videoDimensions: {
  width: number;
  height: number;
}): boolean => {
  return videoDimensions.height > videoDimensions.width;
};

const binarySearch = (
  subtitles: ISubtitle[],
  target: number,
  compare: (sub_time: number, b: number) => boolean
): number => {
  let low = 0;
  let high = subtitles.length - 1;

  while (low <= high) {
    const mid = Math.floor((low + high) / 2);
    if (compare(subtitles[mid].start, target)) {
      high = mid - 1;
    } else {
      low = mid + 1;
    }
  }

  return low;
};

const filterSubtitles = (
  subtitles: ISubtitle[],
  start: number,
  end: number
): ISubtitle[] => {
  const startIndex = binarySearch(subtitles, start, (a, b) => a > b);
  const endIndex = binarySearch(subtitles, end, (a, b) => a >= b);

  // Ensure the indices are within bounds and handle edge cases
  const filteredSubtitles = subtitles.slice(
    Math.max(0, startIndex - 1),
    Math.min(subtitles.length, endIndex + 1)
  );

  return filteredSubtitles;
};

const throttle = (fn: Function, wait: number) => {
  let lastTime = 0;
  return (...args: unknown[]) => {
    const now = new Date().getTime();

    if (now - lastTime >= wait) {
      fn.apply(this, args);
      lastTime = now;
    }
  };
};

const generateTimelineZoomLevels = (
  videoDuration: number,
  totalTimelineLength: number
): TimelineZoomLevel[] => {
  const minIntervalsCount = 4;
  const maxIntervalsCount = 8;
  const minZoomLevels = 5;
  const maxZoomLevels = 10;
  const commonIntervals = [5, 10, 30, 60, 300, 600, 1800, 3600];

  function findNearestInterval(target: number): number {
    return commonIntervals.reduce((prev, curr) =>
      Math.abs(curr - target) < Math.abs(prev - target) ? curr : prev
    );
  }

  // Calculate default zoom level
  let defaultInterval = findNearestInterval(
    Math.max(5, Math.floor(videoDuration / maxIntervalsCount))
  );
  let defaultSpaceBetween = Math.floor(
    totalTimelineLength / Math.ceil(videoDuration / defaultInterval)
  );

  // Adjust defaultInterval and defaultSpaceBetween to fit within totalTimelineLength
  while (
    defaultSpaceBetween * Math.ceil(videoDuration / defaultInterval) >
    totalTimelineLength
  ) {
    const nextInterval = findNearestInterval(defaultInterval + 1);
    if (nextInterval === defaultInterval) break; // No larger interval available
    defaultInterval = nextInterval;
    defaultSpaceBetween = Math.floor(
      totalTimelineLength / Math.ceil(videoDuration / defaultInterval)
    );
  }

  const zoomLevels: TimelineZoomLevel[] = [
    {
      index: 0,
      interval: defaultInterval,
      spaceBetweenIntervals: defaultSpaceBetween,
      default: true,
    },
  ];

  // Find suitable intervals
  const suitableIntervals = Array.from(
    new Set(commonIntervals.filter((interval) => interval <= defaultInterval))
  );
  const smallestInterval = Math.max(5, suitableIntervals[0]);

  // Determine number of zoom levels
  const zoomLevelCount = Math.min(
    maxZoomLevels,
    Math.max(minZoomLevels, suitableIntervals.length)
  );

  // Generate intermediate zoom levels
  const step = (suitableIntervals.length - 1) / (zoomLevelCount - 1);
  for (let i = 1; i < zoomLevelCount; i++) {
    const index = Math.floor(i * step);
    const interval = suitableIntervals[index];
    const spaceBetween = calculateSpaceBetween(
      interval,
      defaultInterval,
      defaultSpaceBetween,
      smallestInterval
    );

    zoomLevels.push({
      index: i,
      interval: interval,
      spaceBetweenIntervals: spaceBetween,
      default: false,
    });
  }

  // Adjust spaceBetweenIntervals to respect minIntervalsCount and maxIntervalsCount
  zoomLevels.forEach((level) => {
    const idealIntervalCount =
      totalTimelineLength / level.spaceBetweenIntervals;
    if (idealIntervalCount < minIntervalsCount) {
      level.spaceBetweenIntervals = Math.floor(
        totalTimelineLength / minIntervalsCount
      );
    } else if (idealIntervalCount > maxIntervalsCount) {
      level.spaceBetweenIntervals = Math.ceil(
        totalTimelineLength / maxIntervalsCount
      );
    }
  });

  // Sort zoom levels and remove duplicates
  zoomLevels.sort((a, b) => b.interval - a.interval);
  const uniqueZoomLevels = zoomLevels.filter(
    (level, index, self) =>
      index === self.findIndex((t) => t.interval === level.interval)
  );

  // Reassign indices after sorting and removing duplicates
  uniqueZoomLevels.forEach((level, idx) => (level.index = idx));

  return uniqueZoomLevels;
};

const calculateSpaceBetween = (
  interval: number,
  defaultInterval: number,
  defaultSpaceBetween: number,
  smallestInterval: number
): number => {
  const maxRatio = defaultInterval / smallestInterval;
  const currentRatio = defaultInterval / interval;
  const normalizedRatio = (currentRatio - 1) / (maxRatio - 1);

  // Use a power function to create a more dramatic curve
  const factor = Math.pow(normalizedRatio, 1.5) * 3 + 1;

  return Math.floor(defaultSpaceBetween * factor);
};

type MemoizedState = {
  lastIndex: number;
  subtitles: ISubtitle[];
};

// Helper function to check if a time is within a subtitle's range
const isTimeInSubtitle = (time: number, subtitle: ISubtitle): boolean => {
  return subtitle && time >= subtitle.start && time < subtitle.end;
};

// Binary search function
const binarySearchSubtitle = (
  subtitles: ISubtitle[],
  currentTime: number
): number => {
  if (!subtitles || !subtitles.length) return -1;

  let left = 0;
  let right = subtitles.length - 1;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const subtitle = subtitles[mid];

    if (isTimeInSubtitle(currentTime, subtitle)) {
      return mid;
    }

    if (currentTime < subtitle.start) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }

  return -1; // Not found
};

// Main search function with memoization
const findSubtitle = (() => {
  let memoizedState: MemoizedState | null = null;

  return (
    subtitles: ISubtitle[] | null | undefined,
    currentTime: number
  ): ISubtitle | null => {
    // Return null if subtitles array is null or undefined
    if (!subtitles || !subtitles.length) {
      return null;
    }

    // Initialize or update memoized state if subtitles array has changed
    if (!memoizedState || memoizedState.subtitles !== subtitles) {
      memoizedState = { lastIndex: 0, subtitles };
    }

    const { lastIndex } = memoizedState;

    // Check if lastIndex is valid and the last found subtitle is still valid
    if (
      lastIndex >= 0 &&
      lastIndex < subtitles.length &&
      isTimeInSubtitle(currentTime, subtitles[lastIndex])
    ) {
      return subtitles[lastIndex];
    }

    // Perform binary search
    const foundIndex = binarySearchSubtitle(subtitles, currentTime);

    if (foundIndex !== -1) {
      memoizedState.lastIndex = foundIndex;
      return subtitles[foundIndex];
    }

    return null; // No subtitle found for the current time
  };
})();

const getClipDurationInSeconds = (segments: IVideoSegment[]): number => {
  if (!segments || segments.length === 0) return 0;

  return segments.reduce((acc: number, segment: IVideoSegment) => {
    return acc + (segment.end - segment.start);
  }, 0);
};

const parseErrorMessage = (
  error: ErrorResponse,
  defaultMessage?: string
): string => {
  if (typeof error.detail === "string") {
    return error.detail;
  } else if (Array.isArray(error.detail) && error.detail.length) {
    return error.detail[0].msg;
  } else {
    return defaultMessage || "An unexpected error occurred";
  }
};

const getSubtitleDefaultSize = (videoHeight: number): number =>
  Math.ceil(videoHeight / 14);

declare global {
  interface Window {
    dataLayer?: any[];
  }
}

const pushToDataLayer = (eventName: string, eventParams = {}) => {
  if (window.dataLayer) {
    window.dataLayer.push({
      event: eventName,
      ...eventParams,
    });
  }
};

/**
 * Adjusts subtitle timings to match new video segments.
 *
 * @param subtitles - Original subtitles
 * @param segments - New video segments
 * @returns New array of subtitles with adjusted timings
 *
 * Notes:
 * - Keeps subtitles that fit in the new segments
 * - Adjusts subtitle and word timings to match new video
 * - [!]Assumes segments are in order and don't overlap
 *
 * Example:
 * Original subtitle: 61 to 71 seconds
 * Segment: 60 to 70 seconds
 * Result: 1 to 10 seconds in new video
 */
const calibrateSubtitles = (
  subtitles: ISubtitle[],
  segments: IVideoSegment[]
): ISubtitle[] => {
  if (segments.length === 0) return [];

  const calibratedSubtitles: ISubtitle[] = [];
  let newVideoTime = 0;
  let subtitleIndex = 0;

  for (const segment of segments) {
    const segmentDuration = segment.end - segment.start;

    while (
      subtitleIndex < subtitles.length &&
      subtitles[subtitleIndex].start < segment.end
    ) {
      const subtitle = subtitles[subtitleIndex];

      // Only include subtitles that overlap with the segment
      if (subtitle.end <= segment.start) {
        subtitleIndex++;
        continue;
      }

      // Calculate overlap
      const overlapStart = Math.max(subtitle.start, segment.start);
      const overlapEnd = Math.min(subtitle.end, segment.end);
      if (overlapStart >= overlapEnd) {
        subtitleIndex++;
        continue;
      }

      // Only calibrate and include subtitles that overlap with the segment
      const calibratedSubtitle: ISubtitle = {
        start: Math.max(
          newVideoTime,
          newVideoTime + (subtitle.start - segment.start)
        ),
        end: Math.min(
          newVideoTime + segmentDuration,
          newVideoTime + (subtitle.end - segment.start)
        ),
        text: subtitle.text,
        words: subtitle.words.map((word) => ({
          ...word,
          start: Math.max(
            newVideoTime,
            newVideoTime + (word.start - segment.start)
          ),
          end: Math.min(
            newVideoTime + segmentDuration,
            newVideoTime + (word.end - segment.start)
          ),
        })),
      };

      calibratedSubtitles.push(calibratedSubtitle);
      subtitleIndex++;
    }

    newVideoTime += segmentDuration;
  }

  return calibratedSubtitles;
};

const totalDurationInFrames = (
  segments: IVideoSegment[],
  fps: number | undefined
): number => {
  if (!segments || segments.length === 0) return 0;
  if (!fps) return 0;

  return Math.round(
    segments.reduce((acc: number, segment: IVideoSegment) => {
      return acc + (segment.end - segment.start) * fps;
    }, 0)
  );
};

// Helper function to determine aspect ratio from video dimensions
const detectAspectRatio = (width: number, height: number): string => {
  const ratio = width / height;

  if (Math.abs(ratio - 16 / 9) < 0.1) return "16:9";
  if (Math.abs(ratio - 9 / 16) < 0.1) return "9:16";
  if (Math.abs(ratio - 1) < 0.1) return "1:1";

  // Default to the closest standard ratio if none of the above match
  return ratio > 1 ? "16:9" : "9:16";
};

const isObject = (item: any) => {
  return item && typeof item === "object" && !Array.isArray(item);
};

const deepMerge = (target: any, ...sources: any[]): any => {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    // Create a new object instead of modifying the target directly
    const result = { ...target };

    for (const key in source) {
      if (isObject(source[key])) {
        if (!result[key]) result[key] = {};
        result[key] = deepMerge(result[key], source[key]);
      } else {
        result[key] = source[key];
      }
    }

    return deepMerge(result, ...sources);
  }

  // If not both objects, return source (overwrite)
  return source === undefined ? target : source;
};

const isValidCropBox = (box: any) => {
  if (!box) return false;

  const w = box.x2 - box.x1;
  const h = box.y2 - box.y1;

  return w > 0 && h > 0;
};

const hexToRgb = (hex: string): string => {
  // Remove the # if present
  hex = hex.replace(/^#/, "");

  // Parse the hex values
  let r, g, b;
  if (hex.length === 3) {
    // For shorthand like #ABC
    r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
    g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
    b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
  } else {
    // For full hex like #AABBCC
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  }

  // Return the RGB values as a comma-separated string
  return `${r}, ${g}, ${b}`;
};

const getClipEstimatedProcessingTimeMs = (
  segments: { start: number; end: number }[],
  videoMetadata: { fps?: number; duration_seconds?: number }
): number => {
  if (segments.length > 0 && videoMetadata?.fps) {
    const durationSeconds =
      segments.reduce(
        (total, segment) => total + (segment.end - segment.start),
        0
      ) / videoMetadata.fps;
    return Math.ceil((durationSeconds / 3.73 + 30) * 1000);
  }
  if (videoMetadata?.duration_seconds) {
    return Math.ceil((videoMetadata.duration_seconds / 3.73 + 30) * 1000);
  }
  return 5 * 60 * 1000;
};

// Function to download file from URL
const downloadFile = (url: string | undefined, title: string | undefined) => {
  if (!url) return;
  const link = document.createElement("a");
  link.href = url;
  link.download = `${title || "video"}.mp4`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Format date to "Month Day, Year" format (e.g., "May 12, 2025")
const formatDate = (date: Date): string => {
  return date.toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric",
  });
};

export {
  handleErrors,
  formatTime,
  parseTime,
  formatEntityTitle,
  getPreviousPath,
  getFrameDimensions,
  isVideoPortrait,
  filterSubtitles,
  throttle,
  generateTimelineZoomLevels,
  findSubtitle,
  getClipDurationInSeconds,
  parseErrorMessage,
  getSubtitleDefaultSize,
  formatFileSize,
  pushToDataLayer,
  formatClipDuration,
  calibrateSubtitles,
  totalDurationInFrames,
  detectAspectRatio,
  deepMerge,
  isValidCropBox,
  hexToRgb,
  getClipEstimatedProcessingTimeMs,
  downloadFile,
  formatDate,
};
