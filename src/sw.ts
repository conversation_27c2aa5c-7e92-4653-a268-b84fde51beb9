/// <reference lib="WebWorker" />
declare let self: ServiceWorkerGlobalScope;

import { registerRoute } from "workbox-routing";
import { CacheFirst } from "workbox-strategies";
import { ExpirationPlugin } from "workbox-expiration";
import { CacheableResponsePlugin } from "workbox-cacheable-response";
import { precacheAndRoute, cleanupOutdatedCaches } from "workbox-precaching";
import { clientsClaim } from "workbox-core";
import type { WorkboxPlugin } from "workbox-core";

/* ---------- 1. canonical key (strip query-string) ---------- */
const canonicalKey: WorkboxPlugin = {
  // make it async *or* wrap the value in Promise.resolve(...)
  async cacheKeyWillBeUsed({ request }) {
    const u = new URL(request.url);
    u.search = ""; // strip presign params
    return u.toString(); // OK: async fn always returns a Promise
  },
};

/* ---------- 2. store the slice exactly as we got it -------- */
const store206Exactly = {
  async cacheWillUpdate({ response }: { response: Response }) {
    return response?.status === 206 ? response : null; // only 206s
  },
};

// Log font requests for debugging
self.addEventListener("fetch", (event) => {
  const url = new URL(event.request.url);
  if (url.hostname.includes("boostcast-fonts")) {
    // Try to respond with cache or fetch for font files specifically
    if (
      url.pathname.includes("/system/") &&
      (url.pathname.endsWith(".otf") ||
        url.pathname.endsWith(".ttf") ||
        url.pathname.endsWith(".woff") ||
        url.pathname.endsWith(".woff2"))
    ) {
      event.respondWith(
        caches
          .match(new Request(url.toString().split("?")[0]))
          .then((cachedResponse) => {
            if (cachedResponse) {
              return cachedResponse;
            }

            return fetch(event.request).then((response) => {
              // Cache a copy of the response
              const responseToCache = response.clone();
              caches.open("fonts-cache").then((cache) => {
                const cacheKey = url.toString().split("?")[0];

                cache.put(new Request(cacheKey), responseToCache);
              });

              return response;
            });
          })
      );
    }
  }
});

/* ---------- 3. SW install boilerplate --------------------- */
self.skipWaiting();
clientsClaim();
cleanupOutdatedCaches();
precacheAndRoute(self.__WB_MANIFEST);

/* ---------- 4. runtime route for the big MP4s ------------- */
// registerRoute(
//   ({ url }) =>
//     url.origin === "https://source-videos.vidfast.ai" &&
//     url.pathname.endsWith(".mp4"),
//   new CacheFirst({
//     cacheName: "source-video-slices",
//     matchOptions: { ignoreSearch: true }, // find cached slice despite new sig
//     plugins: [
//       canonicalKey, // one entry per _file_, not per signature
//       store206Exactly, // keep only the played slice
//       new CacheableResponsePlugin({ statuses: [206] }),
//       new ExpirationPlugin({
//         maxEntries: 500, // tune to ≈2 GB if each slice ~4 MB
//         maxAgeSeconds: 60 * 60 * 24, // 24 h
//         purgeOnQuotaError: true,
//       }),
//     ],
//   })
// );

/* ---------- 5. runtime route for fonts with specific extensions ------------- */
registerRoute(
  // Match any font file requests from our CDN (more permissive matching)
  ({ request, url }) => {
    // Match any font file from our CDN
    return (
      url.hostname.includes("boostcast-fonts") &&
      (url.pathname.endsWith(".otf") ||
        url.pathname.endsWith(".ttf") ||
        url.pathname.endsWith(".woff") ||
        url.pathname.endsWith(".woff2"))
    );
  },
  new CacheFirst({
    cacheName: "fonts-cache",
    matchOptions: { ignoreSearch: true }, // ignore presigned URL parameters
    plugins: [
      canonicalKey, // one entry per file, not per signature
      new CacheableResponsePlugin({
        statuses: [0, 200], // Include both successful and opaque responses (CORS)
      }),
      new ExpirationPlugin({
        maxEntries: 25, // adjust based on how many fonts you use
        maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
        purgeOnQuotaError: true,
      }),
    ],
  })
);

/* ---------- 6. catch-all route for Digital Ocean Spaces fonts ------------- */
registerRoute(
  // Generic route to catch any requests from the fonts CDN
  ({ url }) =>
    url.hostname.includes("boostcast-fonts.s3.eu-west-1.amazonaws.com"),
  new CacheFirst({
    cacheName: "fonts-cache-generic",
    matchOptions: { ignoreSearch: true },
    plugins: [
      canonicalKey,
      new CacheableResponsePlugin({ statuses: [0, 200] }),
      new ExpirationPlugin({
        maxEntries: 25,
        maxAgeSeconds: 60 * 60 * 24 * 30, // 30 days
        purgeOnQuotaError: true,
      }),
    ],
  })
);
