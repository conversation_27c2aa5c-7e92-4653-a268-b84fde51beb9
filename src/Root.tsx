import { Composition } from "remotion";
import { ClipPreview } from "./remotion/ClipPreview";

export const RemotionRoot = () => {
  const clipConfig = {
    segments: [
      {
        start: 0,
        end: 5.273,
        crop_box: {
          x1: 1017.87,
          x2: 1623.81,
          y1: 0,
          y2: 1077.22,
        },
      },
      {
        start: 56.359,
        end: 75.265,
        crop_box: {
          x1: 967.42,
          x2: 1573.36,
          y1: 0,
          y2: 1077.22,
        },
      },
    ],
    subtitles: null,
    sourceVideo: {
      file_path:
        "https://ams3.digitaloceanspaces.com/source-videos/4e9d2e51-f8fb-4048-aa2a-4181f88862d5/412a4801-39dd-42db-924d-83d7516dcd86.mp4?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=DO00M3WTPL3DJKFK2JDZ%2F20250219%2Fams3%2Fs3%2Faws4_request&X-Amz-Date=20250219T152203Z&X-Amz-Expires=3600&X-Amz-SignedHeaders=host&X-Amz-Signature=fe5dad1818e8c658ca2c08238d026d829e3ce3ed0793cb372f26d5fc9171e39f",
      media_metadata: {
        file_size_bytes: 854226002,
        height: 1080,
        width: 1920,
        duration_seconds: 5779.586032,
        fps: 25,
      },
    },
  };

  const segments = [
    {
      start: 0,
      end: 131.825,
      crop_box: {
        x1: 1017.87,
        x2: 1623.81,
        y1: 0,
        y2: 1077.22,
      },
    },
    {
      start: 1408.9750000000001,
      end: 1881.625,
      crop_box: {
        x1: 967.42,
        x2: 1573.36,
        y1: 0,
        y2: 1077.22,
      },
    },
  ];

  const totalDurationInFrames: number = clipConfig.segments.reduce(
    (total, segment) =>
      total +
      (segment.end - segment.start) * clipConfig.sourceVideo.media_metadata.fps,
    0
  );

  return (
    <>
      <Composition
        id="comp"
        fps={clipConfig.sourceVideo.media_metadata.fps}
        height={226}
        width={402}
        durationInFrames={Math.round(totalDurationInFrames)}
        component={ClipPreview}
        defaultProps={{
          segments: segments, // , clipConfig.segments
          fps: clipConfig.sourceVideo.media_metadata.fps,
          videoUrl: clipConfig.sourceVideo.file_path,
          dimensions: {
            width: 226,
            height: 402,
          },
          sourceVideoMetadata: clipConfig.sourceVideo.media_metadata,
        }}
      />
    </>
  );
};
