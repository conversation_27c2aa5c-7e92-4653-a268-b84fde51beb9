import { createStyles, useMantineTheme, Tooltip } from "@mantine/core";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import VFIconComponent from "../icon/vf-icon";
import { MenuItemProps, VFMenu } from "../ui/Menu";
import { VFLabel, VFLabelType } from "../ui/Label";
import { Player, PlayerRef, RenderPoster } from "@remotion/player";

import { ClipPreview } from "../../remotion/ClipPreview";
import { VFButton } from "../ui/Button";
import Progress from "../ui/Progress";
import { AbsoluteFill } from "remotion";
import { ProcessingStatus } from "../../types";
import {
  useNoRenderProcessingSimulation,
  getProcessingSimulationState,
} from "../../hooks/useDownloadSimulation";
import { selectVideoById } from "../../features/videos/videosSlice";
import {
  deleteClip,
  renderClip,
  updateClip,
  updateClipState,
  selectClipById,
} from "../../features/clips/clipsSlice";
import { RootState, store } from "../../store";
import {
  calibrateSubtitles,
  downloadFile,
  formatClipDuration,
  getClipDurationInSeconds,
  pushToDataLayer,
} from "../../utils";
import { closeModal, openModal } from "../../features/modal/modalSlice";
import { ModalType } from "../../features/modal/types";
import { ModalConfig } from "../../features/modal/types";
import { useNavigate } from "react-router-dom";
import React from "react";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import Skeleton from "../Skeleton";


const useStyles = createStyles((theme) => ({
  card: {
    backgroundColor: "transparent",
    borderRadius: "4px",
    position: "relative",
  },
  videoContainer: {
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
    position: "relative",
    marginBottom: 8,
    backgroundColor: "#000",

    "@media (min-width: 1728px)": {
      width: "226px",
      height: "402px",
    },
    "@media (min-width: 1280px) and (max-width: 1727px)": {
      width: "226px",
      height: "402px",
    },
    "@media (min-width: 744px) and (max-width: 1279px)": {
      width: "294px",
      height: "523px",
    },
    "@media (max-width: 743px)": {
      width: "312px",
      height: "555px",
    },
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  progressBarContainer: {
    position: "absolute",
    bottom: 16,
    left: 16,
    right: 16,
    zIndex: 8,
    cursor: "pointer",
  },
  hoverOverlay: {
    opacity: 1,
    transition: "opacity 0.2s ease",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.3)",

    "&.hidden": {
      opacity: 0,
    },
  },
  playButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    cursor: "pointer",
    zIndex: 7,
  },
  favoriteButton: {
    cursor: "pointer",
    position: "absolute",
    top: 12,
    left: 12,
    zIndex: 2,
  },
  soundButton: {
    cursor: "pointer",
    position: "absolute",
    top: 12,
    right: 12,
    zIndex: 2,
  },
  infoRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  titleRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    color: theme.other["captions-text-color"],
  },
  score: {
    color: theme.other["label-small-virality-text-color"],
  },
  duration: {
    color: theme.other["surface-subtext-color"],
  },
  title: {
    marginRight: 8,
    minWidth: 0,
    flex: 1,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  menuButton: {
    position: "relative",
  },
  loadingState: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.1)",
  },
}));

interface ClipCardProps {
  id: string; // Only the ID is required now
  videoId: string;
}

export type ClipCardAction = "edit" | "download" | "delete" | "favorite";

export const ClipCard = ({ id, videoId }: ClipCardProps) => {
  const { classes, cx } = useStyles();
  const { handleError } = useErrorHandler();

  // Get clip data and video from Redux store
  const clipData = useSelector((state: RootState) =>
    selectClipById(state, videoId, id)
  );

  // Track previous rendering status for change detection
  const prevRenderingStatusRef = useRef<ProcessingStatus | null>(null);
  const simulationInitializedRef = useRef(false);

  // Track previous hasValidDuration and videoId to reset simulation initialization
  const prevHasValidDurationRef = useRef<boolean>(false);
  const prevVideoIdRef = useRef<string | undefined>(videoId);

  // Handle loading state
  if (!clipData) {
    return <div className={classes.loadingState}>Loading clip data...</div>;
  }

  const sourceVideo = useSelector((state: RootState) =>
    selectVideoById(state, clipData.video_id)
  );
  // Handle video loading state
  if (!sourceVideo) {
    return <div className={classes.loadingState}>Loading video data...</div>;
  }
  useEffect(() => {
    if (!sourceVideo) {
      console.log(
        `${sourceVideo}`
      );
    }
  }, [sourceVideo])

  // Destructure clip data for easier access
  const {
    title,
    segments = [],
    overall_score: score = 0,
    is_favorite: isFavorite,
    file_path: filePath,
    rendering_status: renderingStatus,
    subtitles,
  } = clipData;

  const duration = formatClipDuration(getClipDurationInSeconds(segments));

  // Calculate clip duration in seconds for simulation estimation
  const clipDurationSeconds = useMemo(
    () => getClipDurationInSeconds(segments),
    [segments]
  );

  // Estimate processing time: 1s video = 3.13s processing, add 20% buffer
  const estimatedProcessingTimeMs = useMemo(() => {
    if (
      typeof clipDurationSeconds === "number" &&
      !isNaN(clipDurationSeconds) &&
      clipDurationSeconds > 0
    ) {
      // New formula: duration * 3.13 * 1.2 (20% buffer)
      const renderTime = clipDurationSeconds * 3.13;
      const bufferedTime = renderTime * 1.2;
      return Math.ceil(bufferedTime * 1000);
    }
    // fallback to 5 minutes
    return 5 * 60 * 1000;
  }, [clipDurationSeconds]);

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isFavoriteHovered, setIsFavoriteHovered] = useState(false);
  const [isSoundOn, setIsSoundOn] = useState(true);
  const [isVolumeHovered, setIsVolumeHovered] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isBuffering, setIsBuffering] = useState(false);
  const [playbackProgress, setPlaybackProgress] = useState(0);
  const [actionsVisible, setActionsVisible] = useState(false);
  const [currentFrameTime, setCurrentFrameTime] = useState("0:00");
  const [hoverTime, setHoverTime] = useState<string | null>(null);
  const [isProgressHovered, setIsProgressHovered] = useState(false);
  const playerRef = useRef<PlayerRef>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);
  const theme = useMantineTheme();

  // Function to handle download completion - just downloads the file
  function handleDownloadComplete() {
    // Actual file download happens when simulation completes
    // downloadFile(filePath, title);
  }

  // Initialize processing simulation hook (renamed from download simulation)
  const {
    status: processingStatus,
    percentage: processingPercentage,
    eta: processingEta,
    startSimulation,
    resetSimulation,
    initializeFromLocalStorage,
  } = useNoRenderProcessingSimulation(
    id,
    handleDownloadComplete,
    estimatedProcessingTimeMs
  );

  const dispatch = useDispatch();

  const navigate = useNavigate();

  // Guard: Only start simulation if we have valid duration_seconds
  const hasValidDuration =
    typeof sourceVideo?.media_metadata?.duration_seconds === "number" &&
    !isNaN(sourceVideo.media_metadata.duration_seconds) &&
    sourceVideo.media_metadata.duration_seconds > 0;

  // Initialize simulation immediately based on rendering status
  useEffect(() => {
    if (!simulationInitializedRef.current && hasValidDuration) {
      simulationInitializedRef.current = true;

      // If current rendering status is PROCESSING, initialize or resume simulation
      if (renderingStatus === ProcessingStatus.PROCESSING) {
        // First check if there's already a simulation state
        const existingState = getProcessingSimulationState(id);

        if (existingState) {
          // We already have a simulation running, no need to initialize
          console.log("Found existing processing simulation for clip", id);
          return;
        }

        // Try to initialize from localStorage first
        const hasExistingSimulation = initializeFromLocalStorage();

        // If no existing simulation was found but status is PROCESSING,
        // start a fresh simulation
        if (!hasExistingSimulation) {
          console.log("Starting new processing simulation for clip", id);
          startSimulation();
        }
      }
    }
  }, [
    renderingStatus,
    initializeFromLocalStorage,
    startSimulation,
    hasValidDuration,
    id,
  ]);

  // If we are processing but don't have valid duration, show loading state
  if (renderingStatus === ProcessingStatus.PROCESSING && !hasValidDuration) {
    return (
      <div className={classes.loadingState}>Loading video metadata...</div>
    );
  }

  // React to changes in rendering status from Redux (SSE events)
  useEffect(() => {
    const prevStatus = prevRenderingStatusRef.current;
    prevRenderingStatusRef.current = renderingStatus as ProcessingStatus;

    // Skip if this is the first render (we handle it in the other useEffect)
    if (prevStatus === null) return;

    // If status changed to PROCESSING, start simulation
    if (
      renderingStatus === ProcessingStatus.PROCESSING &&
      prevStatus !== ProcessingStatus.PROCESSING
    ) {
      startSimulation();
    }

    // If status changed to SUCCESS, reset simulation and possibly download
    if (
      renderingStatus === ProcessingStatus.SUCCESS &&
      prevStatus === ProcessingStatus.PROCESSING
    ) {
      resetSimulation();
    }
  }, [
    renderingStatus,
    startSimulation,
    resetSimulation,
    filePath,
    sourceVideo.file_path,
  ]);

  const handleActionClick = useCallback(
    async (actionType: ClipCardAction) => {
      switch (actionType) {
        case "delete":
          const modalConfig: ModalConfig = {
            type: ModalType.BASIC,
            title: "Delete clip",
            subtitle:
              "Deleting the clip will permanently remove all associated files and data. This action cannot be undone.",
            actions: [
              {
                label: "Cancel",
                variant: "secondary",
                fullWidth: true,
                buttonDisplay: "onlyText",
                onClick: () => dispatch(closeModal()),
              },
              {
                label: "Delete",
                variant: "destructive",
                fullWidth: true,
                buttonDisplay: "withIcon",
                iconName: "trash-can",
                onClick: async () => {
                  console.warn("Delete action clicked");

                  await (dispatch as any)(deleteClip(id));

                  // ! TODO: Loader, notification ?
                  console.warn(`Clip ${title} was deleted`);

                  dispatch(closeModal());
                },
              },
            ],
          };

          dispatch(openModal(modalConfig));

          break;
        case "download":
          // User is in trial mode, cannot download
          if (userData?.subscription_level === 0) {
            // Configure the modal
            const pricingModalConfig: ModalConfig = {
              type: ModalType.PRICING,
              title: "Upgrade your subscription",
              subtitle:
                "You're using a trial version of Boostcast and downloading your clips is currently unavailable for you. Upgrade to unlock your full experience.",
            };

            dispatch(openModal(pricingModalConfig));
          } else if (renderingStatus === ProcessingStatus.DRAFT) {
            pushToDataLayer("clip_exported", {
              email: store.getState().auth.userData?.email,
            });

            try {
              const renderedClip = await dispatch(
                renderClip({
                  clipId: clipData.id,
                }) as any
              ).unwrap?.();

              await dispatch(
                updateClipState({
                  videoId,
                  data: renderedClip,
                }) as any
              ).unwrap?.();
            } catch (error: any) {
              console.error("Failed to export clip:", error);
              handleError(error.error, error.statusCode);
            }
          } else if (renderingStatus === ProcessingStatus.SUCCESS) {
            pushToDataLayer("clip_exported", {
              email: store.getState().auth.userData?.email,
            });

            downloadFile(clipData.file_path, title);
          }
          break;
        case "edit":
          navigate(`./${id}/edit`);
        default:
          console.error("Unknown action type");
      }

      setIsMenuOpen(false);
    },
    [clipData]
  );

  const getCompositionDimensions = useCallback(() => {
    // Get container width based on viewport
    let width, height;

    if (window.innerWidth >= 1728) {
      width = 226;
    } else if (window.innerWidth >= 1280) {
      width = 226;
    } else if (window.innerWidth >= 744) {
      width = 294;
    } else {
      width = 312;
    }

    // Calculate height based on content aspect ratio
    const videoWidth = sourceVideo?.media_metadata?.width || 0;
    const videoHeight = sourceVideo?.media_metadata?.height || 0;

    if (videoWidth && videoHeight) {
      // If we have a segment with crop box, use that aspect ratio
      const segmentWithCropBox = segments.find((segment) => segment.crop_box);
      if (segmentWithCropBox?.crop_box) {
        const cropBox = segmentWithCropBox.crop_box;
        const cropBoxWidth = cropBox.x2 - cropBox.x1;
        const cropBoxHeight = cropBox.y2 - cropBox.y1;
        height = Math.round(width * (cropBoxHeight / cropBoxWidth));
      } else {
        // Otherwise use the video's natural aspect ratio
        height = Math.round(width * (videoHeight / videoWidth));
      }
    } else {
      // Fallback to fixed heights if no video metadata
      if (window.innerWidth >= 1280) {
        height = 402;
      } else {
        height = 552;
      }
    }

    return { width, height };
  }, [sourceVideo, segments]);

  // Calculate the total duration in frames for the entire clip
  const totalDurationInFrames = useCallback(
    () =>
      segments.reduce(
        (total, segment) =>
          total +
          (segment.end - segment.start) * sourceVideo.media_metadata.fps,
        0
      ),
    [segments, sourceVideo.media_metadata.fps]
  )();

  const formatTimeFromFrame = useCallback(
    (frame: number): string => {
      const fps = sourceVideo.media_metadata.fps;
      const totalSeconds = frame / fps;
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = Math.floor(totalSeconds % 60);

      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    },
    [sourceVideo.media_metadata.fps]
  );

  const calculateHoverTime = useCallback(
    (clientX: number) => {
      if (!progressBarRef.current) return null;

      const rect = progressBarRef.current.getBoundingClientRect();
      const hoverPositionRatio = (clientX - rect.left) / rect.width;

      // Keep the ratio within bounds
      const boundedRatio = Math.max(0, Math.min(1, hoverPositionRatio));

      // Calculate the frame at hover position
      const hoverFrame = Math.floor(boundedRatio * totalDurationInFrames);

      return formatTimeFromFrame(hoverFrame);
    },
    [totalDurationInFrames, formatTimeFromFrame]
  );

  const handleProgressBarHover = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const time = calculateHoverTime(e.clientX);
      setHoverTime(time);
    },
    [calculateHoverTime]
  );

  const handleProgressBarClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (!progressBarRef.current || !playerRef.current) return;

      // Calculate the click position as a ratio of the progress bar width
      const rect = progressBarRef.current.getBoundingClientRect();
      const clickPositionRatio = (e.clientX - rect.left) / rect.width;

      // Keep the ratio within bounds
      const boundedRatio = Math.max(0, Math.min(1, clickPositionRatio));

      // Calculate the target frame based on the total duration
      const targetFrame = Math.floor(boundedRatio * totalDurationInFrames);

      // Seek to the target frame using Remotion's seekTo method
      playerRef.current.seekTo(targetFrame);

      // Update the progress state and current time display
      setPlaybackProgress(boundedRatio * 100);
      setCurrentFrameTime(formatTimeFromFrame(targetFrame));
    },
    [totalDurationInFrames, formatTimeFromFrame]
  );

  useEffect(() => {
    if (!playerRef.current) return;

    const onEnded = () => {
      setIsPlaying(false);
      setPlaybackProgress(0);
      setCurrentFrameTime(formatTimeFromFrame(0));
    };

    const onWaiting = () => {
      setIsBuffering(true);
    };

    const onResume = () => {
      setIsBuffering(false);
    };

    const onPause = () => {
      setIsPlaying(false);
    };

    const onPlay = () => {
      setIsPlaying(true);
    };

    const onTimeUpdate = () => {
      if (!playerRef.current) return;
      const currentFrame = playerRef.current.getCurrentFrame();
      const progress = (currentFrame / totalDurationInFrames) * 100;
      setPlaybackProgress(progress);
      setCurrentFrameTime(formatTimeFromFrame(currentFrame));
    };

    playerRef.current.addEventListener("ended", onEnded);
    playerRef.current.addEventListener("waiting", onWaiting);
    playerRef.current.addEventListener("resume", onResume);
    playerRef.current.addEventListener("pause", onPause);
    playerRef.current.addEventListener("play", onPlay);
    playerRef.current.addEventListener("timeupdate", onTimeUpdate);

    return () => {
      if (!playerRef.current) return;
      playerRef.current.removeEventListener("ended", onEnded);
      playerRef.current.removeEventListener("waiting", onWaiting);
      playerRef.current.removeEventListener("resume", onResume);
      playerRef.current.removeEventListener("pause", onPause);
      playerRef.current.removeEventListener("play", onPlay);
      playerRef.current.removeEventListener("timeupdate", onTimeUpdate);
    };
  }, [playerRef.current, totalDurationInFrames, formatTimeFromFrame]);

  const menuItems: MenuItemProps[] = useMemo(
    () => [
      {
        label: "Edit",
        onClick: () => handleActionClick("edit"),
      },
      {
        label: "Download",
        onClick: () => handleActionClick("download"),
        // Disable download button when already processing
        disabled: renderingStatus === ProcessingStatus.PROCESSING,
      },
      {
        label: "Delete",
        isDestructive: true,
        leftIcon: "trash-can",
        onClick: () => handleActionClick("delete"),
      },
    ],
    [renderingStatus, handleActionClick]
  );

  const isHighVirality: boolean = useMemo(
    () => (score ? score >= 80 : true),
    [score]
  );

  const dimensions = getCompositionDimensions();

  const renderPoster: RenderPoster = useCallback(({ isBuffering }) => {
    if (isBuffering) {
      return <Skeleton
        shimmerAngle="270deg"
        bgColor="#1f1f1f"
        textColor="#fff"
        textSize="32px"
      >Loading...</Skeleton>
      // return <Skeleton
      //   height={dimensions.height}
      //   width={dimensions.width}
      //   radius="md"
      // />
    }

    return null;
  }, []);

  // Handle play preview action
  const handlePlayPreview = useCallback(() => {
    // Call parent action if needed for play events
    // onActionClick('play', clipData);
  }, [clipData]);

  // Handle toggle sound
  const handleToggleSound = useCallback(() => {
    // This is just local state, no need to inform parent
    setIsSoundOn((prev) => !prev);
  }, []);

  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false);

  // Handle toggle favorite
  const handleToggleFavorite = useCallback(async () => {
    if (isFavoriteLoading) return;
    setIsFavoriteLoading(true);
    await (dispatch as any)(
      updateClip({
        clipId: id,
        updatePayload: { is_favorite: !clipData.is_favorite },
      })
    );
    setIsFavoriteLoading(false);
  }, [dispatch, id, clipData.is_favorite, isFavoriteLoading]);

  // Get system presets from Redux store
  const systemPresets = useSelector(
    (state: RootState) => state.presets.presets?.system
  );
  const userPresets = useSelector(
    (state: RootState) => state.presets.presets?.user
  );
  const userData = useSelector((state: RootState) => state.auth.userData);

  const defaultPreset = useMemo(() => {
    // Check if clip has a preset_id (this is prioritized)
    if (clipData?.preset_id) {
      // Check in system presets first
      if (systemPresets) {
        const clipSystemPreset = systemPresets.find(
          (preset) => preset.id === clipData.preset_id
        );
        if (clipSystemPreset) return clipSystemPreset.config;
      }

      // Then check in user presets
      if (userPresets) {
        const clipUserPreset = userPresets.find(
          (preset) => preset.id === clipData.preset_id
        );
        if (clipUserPreset) return clipUserPreset.config;
      }
    }

    // If no clip preset or preset not found, use clip's subtitles config if available
    if (clipData?.subtitles?.config) {
      return clipData.subtitles.config;
    }

    // Fallback to system presets
    if (systemPresets && systemPresets.length > 0) {
      // Directly use the first system preset as fallback
      return systemPresets[0].config;
    }

    // Final fallback config if store data not available
    return {
      id: 0, // Add an id for the fallback preset
      words_per_line: 5,
      size: 32,
      font: { family: "Inter", sub_family: "normal" },
      color: "#fff",
      stroke: null,
      shadow: null,
      highlight_color: null,
      preset_name: "Default",
      current_word_color: "#FFE100",
      current_word_bg_color: null, // Added missing property
      amplified_word_color: null,
    };
  }, [systemPresets, userPresets, clipData]);

  // Force render the loading label when rendering status is PROCESSING
  const showLoadingLabel =
    renderingStatus === ProcessingStatus.PROCESSING && !isBuffering;

  useEffect(() => {
    // If videoId changes, or hasValidDuration transitions from false to true, reset simulationInitializedRef
    if (
      prevVideoIdRef.current !== videoId ||
      (!prevHasValidDurationRef.current && hasValidDuration)
    ) {
      simulationInitializedRef.current = false;
    }
    prevHasValidDurationRef.current = hasValidDuration;
    prevVideoIdRef.current = videoId;
  }, [videoId, hasValidDuration]);

  // Create an isolated component to handle the processing label
  const ProcessingLabelComponent = React.memo(
    ({
      clipId,
      estimatedTimeMs,
    }: {
      clipId: string;
      estimatedTimeMs: number;
    }) => {
      // Use its own instance of the simulation hook
      const [percentage, setPercentage] = useState(0);
      const [eta, setEta] = useState("5m");

      // Get current values from a separate simulation instance
      const simulation = useNoRenderProcessingSimulation(
        clipId,
        () => { }, // No-op handler
        estimatedTimeMs
      );

      // Update the state values periodically
      useEffect(() => {
        // Set initial values
        setPercentage(simulation.percentage);
        setEta(simulation.eta);

        // Update periodically
        const intervalId = setInterval(() => {
          setPercentage(simulation.percentage);
          setEta(simulation.eta);
        }, 3000); // Update every 3 seconds

        return () => clearInterval(intervalId);
      }, [simulation]);

      return (
        <VFLabel type={VFLabelType.LOADING} percentage={percentage} eta={eta} />
      );
    }
  );

  return (
    <div className={classes.card}>
      <div className={classes.videoContainer}>
        <Player
          renderPoster={renderPoster}
          style={{
            width: "100%",
            background: "transparent",
            maxHeight: "100%",
          }}
          initiallyMuted={!isSoundOn}
          ref={playerRef}
          component={ClipPreview}
          inputProps={{
            segments,
            sourceVideoUrl: sourceVideo.compressed_file_path,
            dimensions,
            cropMode: false,
            subtitles: {
              ...subtitles,
              config: defaultPreset,
            },
            sourceVideoMetadata: sourceVideo.media_metadata,
            isBuffering,
          }}
          durationInFrames={Math.floor(totalDurationInFrames)}
          compositionWidth={dimensions.width}
          compositionHeight={Math.floor(dimensions.height)}
          fps={sourceVideo.media_metadata.fps}
          showPosterWhenUnplayed
          showPosterWhenPaused
          showPosterWhenBuffering
          bufferStateDelayInMilliseconds={200}
          clickToPlay={false}
          playbackRate={1}
          spaceKeyToPlayOrPause={false}
          acknowledgeRemotionLicense={true}
          doubleClickToFullscreen={false}
        />

        <div
          className={cx(
            classes.hoverOverlay,
            !actionsVisible ||
              isBuffering ||
              renderingStatus === ProcessingStatus.PROCESSING
              ? "hidden"
              : ""
          )}
          onMouseEnter={() =>
            !isBuffering && !showLoadingLabel && setActionsVisible(true)
          }
          onMouseLeave={() => setActionsVisible(false)}
        >
          <div
            className={classes.favoriteButton}
            onMouseEnter={() => setIsFavoriteHovered(true)}
            onMouseLeave={() => setIsFavoriteHovered(false)}
            style={
              isFavoriteLoading ? { opacity: 0.5, pointerEvents: "none" } : {}
            }
          >
            <VFIconComponent
              type={
                isFavoriteHovered && !isFavorite
                  ? "heart-filled"
                  : isFavoriteHovered && isFavorite
                    ? "heart-outlined"
                    : isFavorite
                      ? "heart-filled"
                      : "heart-outlined"
              }
              onClick={handleToggleFavorite}
              color={theme.other["btn-secondary-text-color"]}
              size={16}
            />
          </div>

          <div
            className={classes.soundButton}
            onMouseEnter={() => setIsVolumeHovered(true)}
            onMouseLeave={() => setIsVolumeHovered(false)}
          >
            <VFIconComponent
              type={
                isVolumeHovered && isSoundOn
                  ? "volume-off"
                  : isVolumeHovered && !isSoundOn
                    ? "volume-on"
                    : isSoundOn
                      ? "volume-on"
                      : "volume-off"
              }
              onClick={() => {
                if (playerRef.current) {
                  if (isSoundOn) {
                    playerRef.current.mute();
                  } else {
                    playerRef.current.unmute();
                  }
                  handleToggleSound();
                }
              }}
              color={theme.other["btn-secondary-text-color"]}
              size={16}
            />
          </div>

          <div className={classes.playButton}>
            <VFIconComponent
              type={isPlaying ? "pause" : "play"}
              onClick={() => {
                playerRef.current?.toggle();
                setIsPlaying(!isPlaying);
                handlePlayPreview();
              }}
              color={theme.other["btn-secondary-text-color"]}
              dimensions={{ width: 48, height: 48 }}
            />
          </div>

          {/* Only show progress when playing and not buffering */}
          {isPlaying && !isBuffering && (
            <Tooltip
              label={isProgressHovered ? hoverTime : null}
              position="top"
              withArrow
              arrowSize={5}
              opened={isProgressHovered}
            >
              <div
                className={classes.progressBarContainer}
                ref={progressBarRef}
                onClick={handleProgressBarClick}
                onMouseMove={handleProgressBarHover}
                onMouseEnter={() => setIsProgressHovered(true)}
                onMouseLeave={() => setIsProgressHovered(false)}
              >
                <Progress value={playbackProgress} />
              </div>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Always display loading label when in PROCESSING status */}
      {showLoadingLabel && (
        <ProcessingLabelComponent
          clipId={id}
          estimatedTimeMs={estimatedProcessingTimeMs}
        />
      )}

      <div className={classes.infoRow}>
        <VFLabel
          type={
            isHighVirality
              ? VFLabelType.HIGH_VIRALITY
              : VFLabelType.LOW_VIRALITY
          }
          score={score || "--"}
        />
        <span className={cx(classes.duration, "small-p-regular")}>
          {duration}
        </span>
      </div>

      <div className={classes.titleRow}>
        <div className={`${classes.title} p-heavy`}>{title}</div>
        <div className={classes.menuButton}>
          <VFButton
            variant="secondary"
            buttonDisplay="onlyIcon"
            iconName="more"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          />
          <VFMenu
            items={menuItems}
            isOpen={isMenuOpen}
            position="right"
            onClose={() => setIsMenuOpen(false)}
          />
        </div>
      </div>
    </div>
  );
};
