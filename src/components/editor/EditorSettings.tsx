import { useState, useCallback, useEffect, useMemo, useRef, memo } from "react";
import {
  Box,
  Group,
  createStyles,
  Modal,
  useMantineTheme,
} from "@mantine/core";
import { VFSwitch } from "../ui/Switch";
import { VFSelector } from "../ui/Selector";
import { VFExpander } from "../ui/Expander";
import { VFTextbox } from "../ui/Textbox";
import { VFColorPicker } from "../ui/ColorPicker";
import { VFButton } from "../ui/Button";
import { ButtonGroup, ButtonGroupItem } from "../ui/ButtonGroup";
import {
  PresetConfig,
  IVideoSegment,
  ISubtitle,
  IVideoMetadata,
  FontItem,
} from "../../remotion/types";
import { DEFAULT_SUBTITLES_WORDS_PER_LINE } from "../../constants";
import {
  useClipConfigAPI,
  useClipSubtitles,
  useClipSegments,
} from "../../features/clipConfig/ClipConfigApi";
import { useDispatch, useSelector } from "react-redux";
import { fetchSubtitles } from "../../features/videos/videosSlice";
import { useParams } from "react-router-dom";
import * as React from "react";
import { isValidCropBox } from "../../utils";
import { PresetSettings } from "./PresetSettings";
import { openModal } from "../../features/modal/modalSlice";
import { ModalConfig } from "../../features/modal/types";
import { ModalType } from "../../features/modal/types";
import { closeModal } from "../../features/modal/modalSlice";
import { createPreset } from "../../features/presets/presetsSlice";
import { TimelineZoomContext } from "../../remotion/context/timeline-zoom";
import { playerRef } from "../../remotion/components/timeline-refs";
import { RootState, AppDispatch } from "../../store";
import { IVideoClip } from "../../types";

interface StylesProps {
  leftVisible: boolean;
}

const useStyles = createStyles((theme, { leftVisible }: StylesProps) => ({
  settingsContainer: {
    padding: "16px",
    position: "relative",
    zIndex: 1,
  },

  settingSection: {
    marginBottom: "24px",
  },

  sectionHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "8px",
  },

  sectionTitle: {
    color: "#FFFFFF",
    margin: 0,
  },

  settingRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: "12px",
  },

  settingLabel: {
    color: theme.other["surface-subtext-color"],
    display: "block",
  },

  colorRow: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },

  typographyControls: {
    display: "flex",
    flexDirection: "column",
    gap: "12px",
  },

  alignmentControls: {
    display: "flex",
    gap: "8px",
  },

  fontSizeGroup: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },

  colorPickerWrapper: {
    position: "relative",
    zIndex: 5,
  },
}));

interface EditorSettingsProps {
  aspectRatio: string | null;
  onAspectRatioChange: (ratio: string | null) => void;
  originalSubtitles: ISubtitle[] | null;
  sourceVideoMetadata: IVideoMetadata;
}

// --- Crop Box Logic ---

// CropBox type
export interface CropBox {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

// Helper: getVirtualCropBox
function getVirtualCropBox(
  sourceWidth: number,
  sourceHeight: number,
  targetAspectRatio: string
): CropBox {
  const sourceRatio = sourceWidth / sourceHeight;
  let targetRatio: number;
  switch (targetAspectRatio) {
    case "16:9":
      targetRatio = 16 / 9;
      break;
    case "9:16":
      targetRatio = 9 / 16;
      break;
    case "1:1":
      targetRatio = 1;
      break;
    default:
      targetRatio = sourceRatio;
  }
  let newWidth, newHeight, x1, y1;
  if (Math.abs(sourceRatio - targetRatio) < 0.01) {
    // Already at target aspect ratio
    newWidth = sourceWidth;
    newHeight = sourceHeight;
    x1 = 0;
    y1 = 0;
  } else if (targetRatio > sourceRatio) {
    // Constrained by width
    newWidth = sourceWidth;
    newHeight = sourceWidth / targetRatio;
    x1 = 0;
    y1 = Math.max(0, (sourceHeight - newHeight) / 2);
  } else {
    // Constrained by height
    newHeight = sourceHeight;
    newWidth = sourceHeight * targetRatio;
    x1 = Math.max(0, (sourceWidth - newWidth) / 2);
    y1 = 0;
  }
  return {
    x1: Math.round(x1),
    y1: Math.round(y1),
    x2: Math.round(x1 + newWidth),
    y2: Math.round(y1 + newHeight),
  };
}

// --- API: getCurrentCropBox ---
// Usage: getCurrentCropBox({ mode, aspectRatio, segmentIndex, sourceVideoMetadata, segments })
export type GetCurrentCropBoxFn = (args: {
  aspectRatio: string;
  segmentIndex?: number;
  sourceVideoMetadata: { width: number; height: number };
  segments?: IVideoSegment[];
}) => CropBox | null;

const getCurrentCropBox: GetCurrentCropBoxFn = ({
  aspectRatio,
  segmentIndex,
  sourceVideoMetadata,
  segments,
}) => {
  if (!sourceVideoMetadata?.width || !sourceVideoMetadata?.height) return null;

  if (segments && typeof segmentIndex === "number") {
    const seg = segments[segmentIndex];
    if (!seg) return null;
    // If aspect ratio matches the segment's crop box, use the real crop box
    if (seg.crop_box) {
      const cropBox = seg.crop_box;
      const cropBoxWidth = cropBox.x2 - cropBox.x1;
      const cropBoxHeight = cropBox.y2 - cropBox.y1;
      const segRatio = +(cropBoxWidth / cropBoxHeight).toFixed(2);
      let targetRatio = 1;
      if (aspectRatio === "16:9") targetRatio = +(16 / 9).toFixed(2);
      else if (aspectRatio === "9:16") targetRatio = +(9 / 16).toFixed(2);
      // else 1:1
      if (Math.abs(segRatio - targetRatio) < 0.01) {
        return cropBox;
      }
    }
    // Otherwise, use a virtual crop box for this segment
    return getVirtualCropBox(
      sourceVideoMetadata.width,
      sourceVideoMetadata.height,
      aspectRatio
    );
  }
  return null;
};

const EditorSettings = React.memo(
  React.forwardRef(function EditorSettings(
    {
      aspectRatio,
      onAspectRatioChange,
      originalSubtitles,
      sourceVideoMetadata,
    }: EditorSettingsProps,
    ref: React.ForwardedRef<{ getCurrentCropBox: GetCurrentCropBoxFn }>
  ) {
    const { classes, cx } = useStyles({ leftVisible: true });

    // Get config API and current state
    const { onSubtitlesChange, onSegmentsChange } = useClipConfigAPI();
    const subtitles = useClipSubtitles();
    const segments = useClipSegments();

    // Preset management
    const [aspectRatioSelectorOpen, setAspectRatioSelectorOpen] =
      useState(false);
    const [presetSelectorOpen, setPresetSelectorOpen] = useState(false);
    const [fontSelectorOpen, setFontSelectorOpen] = useState(false);
    const [fontWeightSelectorOpen, setFontWeightSelectorOpen] = useState(false);

    // Local state tracking
    const [captionsEnabled, setCaptionsEnabled] = useState(true);
    const [textAlignment, setTextAlignment] = useState<string>("center");
    const [fontWeight, setFontWeight] = useState<string>("Regular");

    const dispatch = useDispatch<AppDispatch>();
    const { projectId } = useParams<{ projectId: string }>();

    // Get presets from Redux store
    const presets = useSelector((state: RootState) => state.presets.presets);
    const fonts = useSelector((state: RootState) => state.presets.fonts);
    const userData = useSelector((state: RootState) => state.auth.userData);

    // We need to get the clip to check its preset_id
    const { clipId } = useParams<{ clipId: string }>();
    const clipsForVideo = useSelector((state: RootState) =>
      state.videos.videos.flatMap((video) => video.clips || [])
    );
    const clipData = useMemo(() => {
      if (!clipId || !clipsForVideo.length) return null;
      // Cast to include the optional preset_id property
      return clipsForVideo.find(
        (clip) => clip.id === clipId
      ) as IVideoClip | null;
    }, [clipId, clipsForVideo]);

    // Get the default preset from Redux store based on user's default_preset_id
    const defaultPreset = useMemo(() => {
      // Find user's default preset if available
      if (userData?.default_preset_id && presets) {
        // Check system presets first
        if (presets.system) {
          const userDefaultSystemPreset = presets.system.find(
            (preset) => preset.id === userData.default_preset_id
          );
          if (userDefaultSystemPreset) return userDefaultSystemPreset.config;
        }

        // Then check user presets
        if (presets.user) {
          const userDefaultUserPreset = presets.user.find(
            (preset) => preset.id === userData.default_preset_id
          );
          if (userDefaultUserPreset) return userDefaultUserPreset.config;
        }
      }

      // Fallback to system presets if no default preset was found
      if (presets?.system?.length) {
        // Directly use the first system preset without any preference
        return presets.system[0].config;
      }

      // If no presets available, return a minimal default preset
      return {
        id: 1,
        preset_name: "Default",
        words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
        size: 40,
        font: {
          family: "Inter",
          sub_family: "normal",
        },
        color: "#FFFFFF",
        stroke: {
          color: "#000000",
          weight: 1,
          opacity: 100,
        },
        shadow: null,
        highlight_color: null,
        current_word_color: "#FFE100",
        amplified_word_color: null,
        current_word_bg_color: "#000000",
      };
    }, [presets, userData]);

    // Initialize with a default to avoid null errors
    const [currentPreset, setCurrentPreset] = useState<string>("Custom");

    // Ensure defaultPreset has all required properties
    const completeDefaultPreset = useMemo(
      () => ({
        ...defaultPreset,
        font: defaultPreset.font || { family: "Inter", sub_family: "normal" },
        color: defaultPreset.color || "#FFFFFF",
        size: defaultPreset.size || 40,
        stroke: defaultPreset.stroke || null,
        shadow: defaultPreset.shadow || null,
        current_word_color: defaultPreset.current_word_color || "#FFE100",
        current_word_bg_color: defaultPreset.current_word_bg_color || "#000000",
      }),
      [defaultPreset]
    );

    // Track if we've already initialized
    const isInitialized = useRef(false);

    // Track current subtitle config (derived from context or local state)
    const [subtitleConfig, setSubtitleConfig] = useState<PresetConfig>(
      subtitles?.config || completeDefaultPreset
    );

    const [isFetchingSubtitles, setIsFetchingSubtitles] = useState(false);
    const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // Add a ref to track the last selected preset and its config
    const lastSelectedPresetRef = useRef<{
      name: string;
      config: PresetConfig;
    } | null>(null);

    // Unified initialization logic
    useEffect(() => {
      if (isInitialized.current) return;

      // Only proceed with initialization if we have the necessary data
      if (!presets) {
        console.log("Waiting for presets data...");
        return;
      }

      // 1. First determine what preset config to use
      let configToUse: PresetConfig = completeDefaultPreset; // Initialize with default
      let presetNameToUse: string = "Custom"; // Default fallback

      // Check if clip has a preset_id (this is prioritized)
      if (clipData?.preset_id) {
        let foundClipPreset = false;

        // Check system presets first
        if (presets.system) {
          const clipSystemPreset = presets.system.find(
            (preset) => preset.id === clipData.preset_id
          );
          if (clipSystemPreset) {
            console.log(
              "Found clip's preset in system presets:",
              clipSystemPreset.name
            );
            presetNameToUse = clipSystemPreset.name;
            configToUse = clipSystemPreset.config;
            foundClipPreset = true;
          }
        }

        // Then check user presets if not found in system presets
        if (!foundClipPreset && presets.user) {
          const clipUserPreset = presets.user.find(
            (preset) => preset.id === clipData.preset_id
          );
          if (clipUserPreset) {
            console.log(
              "Found clip's preset in user presets:",
              clipUserPreset.name
            );
            presetNameToUse = clipUserPreset.name;
            configToUse = clipUserPreset.config;
            foundClipPreset = true;
          }
        }
      }

      // If no clip preset id or preset not found, try using existing subtitles config
      if (presetNameToUse === "Custom") {
        if (subtitles?.config) {
          console.log("Using existing subtitles config");
          configToUse = {
            ...completeDefaultPreset,
            ...subtitles.config,
          };

          // Try to match this config with a named preset
          let matchFound = false;

          // Check system presets
          if (presets.system) {
            const matchingSystemPreset = presets.system.find((preset) =>
              isConfigEqual(preset.config, configToUse)
            );
            if (matchingSystemPreset) {
              console.log(
                "Found matching system preset for subtitles config:",
                matchingSystemPreset.name
              );
              presetNameToUse = matchingSystemPreset.name;
              matchFound = true;
            }
          }

          // Check user presets if no match yet
          if (!matchFound && presets.user) {
            const matchingUserPreset = presets.user.find((preset) =>
              isConfigEqual(preset.config, configToUse)
            );
            if (matchingUserPreset) {
              console.log(
                "Found matching user preset for subtitles config:",
                matchingUserPreset.name
              );
              presetNameToUse = matchingUserPreset.name;
              matchFound = true;
            }
          }

          if (!matchFound) {
            console.log(
              "No matching preset found for subtitles config, using Custom"
            );
          }
        }
        // No subtitles config, use first system preset as fallback
        else if (presets.system?.length) {
          console.log("Using first system preset:", presets.system[0].name);
          presetNameToUse = presets.system[0].name;
          configToUse = presets.system[0].config;
        } else {
          console.log("No system presets available, using default config");
          configToUse = completeDefaultPreset;
        }
      }

      // 2. Apply the determined config and preset name
      setCurrentPreset(presetNameToUse);
      setSubtitleConfig(configToUse);
      setCaptionsEnabled(true);

      // Mark initialization as complete
      isInitialized.current = true;
    }, [subtitles, presets, completeDefaultPreset, clipData]);

    // Track major config changes separately with a deep comparison
    const subtitlesConfigRef = useRef(subtitles?.config);
    useEffect(() => {
      // Skip on first render, we handle that in the initializing effect
      if (!isInitialized.current) return;

      // Skip if config is the same object reference
      if (subtitlesConfigRef.current === subtitles?.config) return;
      subtitlesConfigRef.current = subtitles?.config;

      // Only update if there are actual changes
      if (
        subtitles?.config &&
        JSON.stringify(subtitles.config) !== JSON.stringify(subtitleConfig)
      ) {
        console.log(
          "Updating subtitleConfig from context changes (likely undo/redo)"
        );
        const completeConfig = {
          ...completeDefaultPreset,
          ...subtitles.config,
        };
        setSubtitleConfig(completeConfig);
      }

      // Update captionsEnabled based on whether subtitles is null
      setCaptionsEnabled(subtitles !== null);
    }, [subtitles, subtitles?.config, completeDefaultPreset, subtitleConfig]);

    // Ensure currentPreset is properly set even if initialization hasn't run yet
    useEffect(() => {
      if (currentPreset === "Custom" && presets && !isInitialized.current) {
        // Check if clip has a preset_id
        if (clipData?.preset_id) {
          // Check system presets first
          if (presets.system) {
            const clipSystemPreset = presets.system.find(
              (preset) => preset.id === clipData.preset_id
            );
            if (clipSystemPreset) {
              setCurrentPreset(clipSystemPreset.name);
              return;
            }
          }

          // Then check user presets
          if (presets.user) {
            const clipUserPreset = presets.user.find(
              (preset) => preset.id === clipData.preset_id
            );
            if (clipUserPreset) {
              setCurrentPreset(clipUserPreset.name);
              return;
            }
          }
        }

        // If subtitles config exists, use Custom preset
        if (subtitles?.config) {
          setCurrentPreset("Custom");
          return;
        }

        // If no preset_id or subtitles config, use first system preset as fallback
        if (presets.system?.length) {
          setCurrentPreset(presets.system[0].name);
        }
      }
    }, [currentPreset, presets, clipData, subtitles, isInitialized]);

    // Initialize font weight from config when it changes
    useEffect(() => {
      if (subtitleConfig?.font?.sub_family) {
        let displayWeight = subtitleConfig.font.sub_family;
        // Convert from CSS weight to display name
        if (displayWeight === "normal") displayWeight = "Regular";
        if (
          displayWeight === "bold" ||
          displayWeight === "light" ||
          displayWeight === "medium"
        ) {
          displayWeight =
            displayWeight.charAt(0).toUpperCase() + displayWeight.slice(1);
        }
        setFontWeight(displayWeight);
      }
    }, [subtitleConfig?.font?.sub_family]);

    // Debounced fetchSubtitles handler
    const handleWordsPerLineChange = useCallback(
      (value: number, baseConfig?: PresetConfig) => {
        if (!projectId || value < 1 || value > 10) return;
        if (fetchTimeoutRef.current) clearTimeout(fetchTimeoutRef.current);
        // Use baseConfig if provided (from preset), otherwise local subtitleConfig
        const configToUse = baseConfig
          ? { ...baseConfig, words_per_line: value }
          : { ...subtitleConfig, words_per_line: value };
        setSubtitleConfig((prev) => ({ ...prev, words_per_line: value }));
        fetchTimeoutRef.current = setTimeout(async () => {
          setIsFetchingSubtitles(true);
          try {
            const result = await dispatch(
              fetchSubtitles({
                video_id: projectId,
                words_per_line: value,
              }) as any
            );
            if (result.payload && result.payload.subtitles) {
              onSubtitlesChange({
                items: result.payload.subtitles,
                config: configToUse,
                position: { x: 0, y: 0 },
              });
            }
          } catch (err) {
            // Optionally handle error
          } finally {
            setIsFetchingSubtitles(false);
          }
        }, 1);
      },
      [dispatch, projectId, subtitleConfig, onSubtitlesChange, segments]
    );

    // General handler for subtitle config updates
    const updateSubtitleConfig = useCallback(
      (updatedConfig: PresetConfig) => {
        setSubtitleConfig(updatedConfig);
        if (captionsEnabled) {
          if (subtitles) {
            onSubtitlesChange({
              ...subtitles,
              config: updatedConfig,
            });
          } else if (originalSubtitles && originalSubtitles.length > 0) {
            onSubtitlesChange({
              items: originalSubtitles,
              config: updatedConfig,
              position: { x: 0, y: 0 },
            });
          } else {
            onSubtitlesChange({
              items: [],
              config: updatedConfig,
              position: { x: 0, y: 0 },
            });
          }
        }
      },
      [
        onSubtitlesChange,
        subtitles,
        originalSubtitles,
        captionsEnabled,
        segments,
      ]
    );

    // Handle captions toggle with defensive coding to prevent null/undefined errors
    const handleCaptionsToggle = useCallback(
      (enabled: boolean) => {
        setCaptionsEnabled(enabled);
        if (!enabled) {
          onSubtitlesChange(null);
        } else if (originalSubtitles && originalSubtitles.length > 0) {
          onSubtitlesChange({
            items: originalSubtitles,
            config: subtitleConfig,
            position: { x: 0, y: 0 },
          });
        } else {
          onSubtitlesChange({
            items: [],
            config: subtitleConfig,
            position: { x: 0, y: 0 },
          });
        }
      },
      [onSubtitlesChange, originalSubtitles, subtitleConfig, segments]
    );

    // Create preset options from Redux presets
    const presetOptions = useMemo(() => {
      if (!presets) return [{ value: "Custom", label: "Custom" }];

      const options = [];

      // Create options from system presets
      if (presets.system && presets.system.length > 0) {
        const systemOptions = presets.system.map((preset) => ({
          value: preset.name,
          label: preset.name,
        }));
        options.push(...systemOptions);
      }

      // Add user presets if available
      if (presets.user && presets.user.length > 0) {
        const userOptions = presets.user.map((preset) => ({
          value: preset.name,
          label: preset.name,
        }));
        options.push(...userOptions);
      }

      // Add custom option
      options.push({ value: "Custom", label: "Custom" });

      return options;
    }, [presets]);

    // Handle preset change with system presets from Redux
    const handlePresetChange = useCallback(
      (presetName: string | null) => {
        if (!presetName) return;
        let selectedPreset: PresetConfig | undefined;
        if (presetName !== "Custom") {
          if (presets?.system && presets.system.length > 0) {
            const systemPreset = presets.system.find(
              (preset) => preset.name === presetName
            );
            if (systemPreset) {
              selectedPreset = systemPreset.config;
            }
          }
          if (!selectedPreset && presets?.user && presets.user.length > 0) {
            const userPreset = presets.user.find(
              (preset) => preset.name === presetName
            );
            if (userPreset) {
              selectedPreset = userPreset.config;
            }
          }
        }
        if (selectedPreset) {
          setCurrentPreset(presetName);
          lastSelectedPresetRef.current = {
            name: presetName,
            config: selectedPreset,
          };
          updateSubtitleConfig(selectedPreset);
          if (selectedPreset.words_per_line !== subtitleConfig.words_per_line) {
            handleWordsPerLineChange(
              selectedPreset.words_per_line,
              selectedPreset
            );
          }
        } else {
          setCurrentPreset(presetName);
          lastSelectedPresetRef.current = null;
        }
      },
      [
        updateSubtitleConfig,
        subtitleConfig.words_per_line,
        handleWordsPerLineChange,
        presets,
      ]
    );

    // Aspect ratio options
    const aspectRatioOptions = [
      { value: "9:16", label: "9:16\nTikTok, IG Reels, YT Shorts" },
      { value: "1:1", label: "1:1\nIG Feed, FB Feed, LinkedIn" },
      { value: "16:9", label: "16:9\nYouTube, Facebook, X, LinkedIn" },
    ];

    // Create font options from all available fonts in Redux store
    const fontOptions = useMemo(() => {
      if (!fonts) return [];

      // Combine system and user fonts
      const allFonts = [...(fonts.system || []), ...(fonts.user || [])];

      // Map to options format for selector
      return allFonts.map((font: FontItem) => ({
        value: font.family,
        label: font.family,
      }));
    }, [fonts]);

    const fontWeightOptions = [
      { value: "Light", label: "Light" },
      { value: "Regular", label: "Regular" },
      { value: "Medium", label: "Medium" },
      { value: "Bold", label: "Bold" },
    ];

    // Handle aspect ratio change
    const handleAspectRatioChange = useCallback(
      (newAspectRatio: string | null) => {
        if (!newAspectRatio || newAspectRatio === aspectRatio) return;

        // Call parent handler to update aspect ratio
        onAspectRatioChange(newAspectRatio);

        const newSegs: IVideoSegment[] = segments.map(
          (segment: IVideoSegment, idx: number) => {
            let segCropBox = getCurrentCropBox({
              aspectRatio: newAspectRatio,
              segmentIndex: idx,
              sourceVideoMetadata,
              segments: segments,
            });

            if (!isValidCropBox(segCropBox)) {
              segCropBox = segment.crop_box || {
                x1: 0,
                y1: 0,
                x2: sourceVideoMetadata.width,
                y2: sourceVideoMetadata.height,
              };
            }
            return {
              ...segment,
              crop_box: segCropBox,
            };
          }
        );

        onSegmentsChange(newSegs);

        // Close selector
        setAspectRatioSelectorOpen(false);
      },
      [aspectRatio, onAspectRatioChange, segments, onSegmentsChange]
    );

    // Handle text alignment change
    const handleAlignmentChange = useCallback((value: string) => {
      setTextAlignment(value);
      // Do not update subtitleConfig, as alignment is not part of PresetConfig
    }, []);

    // --- Original Crop Boxes Ref (for clip mode) ---
    const originalClipCropBoxesRef = useRef<{
      [segmentIndex: number]: CropBox;
    }>({});

    const theme = useMantineTheme();

    // Expose getCurrentCropBox via ref
    React.useImperativeHandle(ref, () => ({ getCurrentCropBox }), []);

    const SavePresetModalBody = React.memo(
      React.forwardRef(function SavePresetModalBody(
        {
          value,
        }: {
          value: string;
        },
        ref: React.Ref<{ getValue: () => string }>
      ) {
        const [presetName, setPresetName] = useState(value);

        React.useImperativeHandle(
          ref,
          () => ({
            getValue: () => presetName,
          }),
          [presetName]
        );

        return (
          <VFTextbox
            placeholder="Enter preset name"
            leftIconType="subtitles"
            value={presetName}
            onChange={setPresetName}
          />
        );
      })
    );

    // Restore system preset logic for isCustomPreset using Redux presets
    function getSystemPresetByName(name: string) {
      if (!presets?.system) return null;
      const preset = presets.system.find((preset) => preset.name === name);
      return preset ? preset.config : null;
    }

    function getUserPresetByName(name: string) {
      if (!presets?.user) return null;
      const preset = presets.user.find((preset) => preset.name === name);
      return preset ? preset.config : null;
    }

    // Update isConfigEqual to allow ignoring words_per_line
    function isConfigEqual(
      a: PresetConfig,
      b: PresetConfig,
      options?: { ignoreWordsPerLine?: boolean }
    ) {
      const normalizeConfig = (config: PresetConfig) => {
        const {
          size,
          color,
          font,
          stroke,
          shadow,
          current_word_color,
          amplified_word_color,
          current_word_bg_color,
          words_per_line,
        } = config;
        if (options?.ignoreWordsPerLine) {
          return {
            size,
            color,
            font,
            stroke,
            shadow,
            current_word_color,
            amplified_word_color,
            current_word_bg_color,
          };
        }
        return {
          size,
          color,
          font,
          stroke,
          shadow,
          current_word_color,
          amplified_word_color,
          current_word_bg_color,
          words_per_line,
        };
      };
      return (
        JSON.stringify(normalizeConfig(a)) ===
        JSON.stringify(normalizeConfig(b))
      );
    }

    const systemPreset = getSystemPresetByName(currentPreset);
    const userPreset = getUserPresetByName(currentPreset);
    const selectedPreset = systemPreset || userPreset;
    const isCustomPreset = (() => {
      if (lastSelectedPresetRef.current) {
        // If the last selected preset matches except for words_per_line, do not consider it custom
        if (
          isConfigEqual(subtitleConfig, lastSelectedPresetRef.current.config, {
            ignoreWordsPerLine: true,
          })
        ) {
          return false;
        }
      }
      return !selectedPreset || !isConfigEqual(subtitleConfig, selectedPreset);
    })();

    useEffect(() => {
      if (
        isCustomPreset &&
        currentPreset !== "Custom" &&
        !(
          lastSelectedPresetRef.current &&
          isConfigEqual(subtitleConfig, lastSelectedPresetRef.current.config, {
            ignoreWordsPerLine: true,
          })
        )
      ) {
        setCurrentPreset("Custom");
      }
    }, [subtitleConfig, currentPreset, isCustomPreset]);

    // Add effect to keep preset selector in sync with Redux state after preset creation
    useEffect(() => {
      // If a new user preset was added, check if it matches our current config
      if (presets?.user) {
        const matchingUserPreset = presets.user.find((preset) =>
          isConfigEqual(subtitleConfig, preset.config)
        );

        if (matchingUserPreset && currentPreset === "Custom") {
          setCurrentPreset(matchingUserPreset.name);
        }
      }
    }, [presets?.user, subtitleConfig, currentPreset]);

    // Save as preset modal logic
    const handleOpenSavePresetModal = useCallback(() => {
      const modalBodyRef = React.createRef<{ getValue: () => string }>();
      const modalBody: React.ReactNode = (
        <SavePresetModalBody ref={modalBodyRef} value={""} />
      );
      const modalConfig: ModalConfig = {
        type: ModalType.BASIC,
        title: "Create brand preset",
        body: modalBody,
        actions: [
          {
            label: "Cancel",
            variant: "secondary",
            fullWidth: true,
            buttonDisplay: "onlyText",
            onClick: () => dispatch(closeModal()),
          },
          {
            label: "Save preset",
            variant: "primary",
            fullWidth: true,
            onClick: async () => {
              const value = modalBodyRef.current?.getValue() ?? "";

              if (value) {
                const result = await dispatch(
                  createPreset({
                    name: value,
                    config: subtitleConfig,
                  })
                );

                // After preset is created, select it
                if (result.payload) {
                  setCurrentPreset(value);
                }
              }

              dispatch(closeModal());
            },
          },
        ],
      };

      dispatch(openModal(modalConfig));
    }, [dispatch, subtitleConfig]);

    // Add a very specific effect just for the words_per_line value
    useEffect(() => {
      // Skip if not initialized
      if (!isInitialized.current || !subtitles?.config) return;

      const contextWordsPerLine = subtitles.config.words_per_line;
      const localWordsPerLine = subtitleConfig.words_per_line;

      if (contextWordsPerLine !== localWordsPerLine) {
        console.log(
          `Direct words_per_line sync: context=${contextWordsPerLine}, local=${localWordsPerLine}`
        );

        // Update only the words_per_line without touching other properties
        setSubtitleConfig((prev) => ({
          ...prev,
          words_per_line: contextWordsPerLine,
        }));
      }
    }, [subtitles?.config?.words_per_line]); // Only depend on this specific property

    return (
      <Box className={classes.settingsContainer}>
        {/* CLIP LAYOUT section */}
        <Box className={classes.settingSection} style={{ paddingLeft: "16px" }}>
          <Box className={classes.sectionHeader}>
            <h4 className={classes.sectionTitle}>CLIP LAYOUT</h4>
          </Box>

          <Box className={classes.settingRow}>
            <span className={cx(classes.settingLabel, "p-regular")}>
              Aspect ratio
            </span>
            <Box style={{ position: "relative" }}>
              <VFButton
                variant="secondary"
                buttonDisplay="dropdown"
                fullWidth
                style={{ width: "250px" }}
                onClick={() =>
                  setAspectRatioSelectorOpen(!aspectRatioSelectorOpen)
                }
              >
                {aspectRatio || "Select aspect ratio"}
              </VFButton>
              <VFSelector
                options={aspectRatioOptions}
                value={aspectRatio || "16:9"}
                onChange={handleAspectRatioChange}
                isOpen={aspectRatioSelectorOpen}
                onClose={() => setAspectRatioSelectorOpen(false)}
                deselectable={true}
              />
            </Box>
          </Box>
        </Box>

        {/* CAPTIONS section */}
        <Box className={classes.settingSection}>
          <Box
            className={classes.sectionHeader}
            style={{ paddingLeft: "16px" }}
          >
            <h4 className={classes.sectionTitle}>CAPTIONS</h4>
            <VFSwitch value={captionsEnabled} onChange={handleCaptionsToggle} />
          </Box>

          {captionsEnabled && (
            <>
              <Box
                className={classes.settingRow}
                style={{ paddingLeft: "16px" }}
              >
                <span className={cx(classes.settingLabel, "p-regular")}>
                  Preset
                </span>
                <Box style={{ position: "relative" }}>
                  <VFButton
                    iconName="subtitles"
                    variant="secondary"
                    buttonDisplay="dropdown"
                    style={{
                      width: "250px",
                      display: "flex",
                      alignItems: "center",
                    }}
                    className="preset-dropdown"
                    onClick={() => setPresetSelectorOpen(!presetSelectorOpen)}
                  >
                    {currentPreset}
                  </VFButton>
                  <VFSelector
                    options={presetOptions}
                    value={currentPreset}
                    onChange={handlePresetChange}
                    isOpen={presetSelectorOpen}
                    onClose={() => setPresetSelectorOpen(false)}
                  />
                  {isCustomPreset && (
                    <Box style={{ textAlign: "right" }}>
                      <span
                        style={{
                          background: "none",
                          border: "none",
                          color: theme.other["surface-subtext-color"],
                          textDecoration: "underline",
                          fontSize: 12,
                          fontWeight: 400,
                          cursor: "pointer",
                          padding: 0,
                          margin: 0,
                        }}
                        className="p-regular"
                        onClick={handleOpenSavePresetModal}
                      >
                        Save current settings as a brand preset
                      </span>
                    </Box>
                  )}
                </Box>
              </Box>

              <VFExpander title="Preset settings" initiallyExpanded={false}>
                <PresetSettings
                  preset={subtitleConfig}
                  onChange={updateSubtitleConfig}
                  wordsPerLine={
                    subtitles?.config?.words_per_line ??
                    subtitleConfig.words_per_line
                  }
                  onWordsPerLineChange={(value) => {
                    // Pass the last selected preset config if available
                    handleWordsPerLineChange(
                      value,
                      lastSelectedPresetRef.current?.config
                    );
                  }}
                  isFetchingSubtitles={isFetchingSubtitles}
                />
              </VFExpander>
            </>
          )}
        </Box>
      </Box>
    );
  })
);

export default EditorSettings;
