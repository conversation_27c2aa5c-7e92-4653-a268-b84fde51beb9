import React, {
  useState,
  useRef,
  useEffect,
  use<PERSON>allback,
  useMemo,
  useImperativeHandle,
} from "react";
import {
  createStyles,
  Box,
  Text,
  useMantineTheme,
  ScrollArea,
} from "@mantine/core";
import { VFTextbox } from "../ui/Textbox";
import { VFContextMenu, ContextMenuItemProps } from "../ui/ContextMenu";
import VFIconComponent from "../icon/vf-icon";
import {
  useClipSubtitles,
  useClipConfigAPI,
  useClipSegments,
} from "../../features/clipConfig/ClipConfigApi";
import { currentWordRef } from "../../remotion/components/timeline-refs";
import { getCurrentSubtitleIndex } from "../../remotion/imperative-state";
import { calibrateSubtitles } from "../../utils";

interface StylesProps {
  // Passing an empty object to avoid TypeScript errors
}

const useStyles = createStyles((theme, _props: StylesProps) => ({
  transcriptContainer: {
    padding: "8px 16px",
    display: "flex",
    flexDirection: "column",
    gap: "24px",
    height: "100%",
    width: "100%",
    backgroundColor: "#121212",
  },

  transcriptHeader: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    marginBottom: "8px",
  },

  transcriptTitle: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },

  transcriptSearchWrapper: {
    marginBottom: "24px",
    width: "100%",
  },

  transcriptSearch: {
    background: "#121212",
    border: `1px solid #3A3A3A`,
    borderRadius: "4px",
    height: "40px",
    width: "100%",
    color: theme.white,

    "& input": {
      color: theme.white,
      "&::placeholder": {
        color: "#B3B3B3",
      },
      padding: "8px 16px",
    },
    "& .mantine-TextInput-rightSection": {
      color: "#B3B3B3",
      paddingRight: "16px",
    },
  },

  customTextbox: {
    "& .mantine-TextInput-input": {
      backgroundColor: "#121212 !important",
      border: "1px solid #3A3A3A !important",
      color: "#FFFFFF !important",
      height: "40px",

      "&::placeholder": {
        color: "#B3B3B3 !important",
      },
    },
    "& .mantine-TextInput-input:focus": {
      border: "1px solid #555555 !important",
    },
    "& .mantine-TextInput-input:hover": {
      border: "1px solid #555555 !important",
    },
    "& div[class*='rightIcon']": {
      "& svg": {
        "& path": {
          fill: "#B3B3B3 !important",
        },
      },
    },
  },

  darkSearchIcon: {
    "& svg path": {
      fill: "#B3B3B3 !important",
    },
  },

  transcriptContent: {
    flex: 1,
    overflowY: "auto",
    height: "calc(100% - 108px)", // Fixed height: subtracting header and search input heights
  },

  transcriptText: {
    fontSize: "20px",
    lineHeight: "1.4",
    color: theme.white,
    fontWeight: 400,
    fontFamily: "SF Pro Display, sans-serif",
    whiteSpace: "pre-wrap",
    wordBreak: "break-word",
  },

  highlightedText: {
    backgroundColor: "#FFE100",
    color: "#000000",
    display: "inline",
    padding: 0,
    margin: 0,
    borderRadius: 0,
  },

  activeSubtitle: {
    backgroundColor: "#E1E1E1 !important",
    color: "#121212 !important",
  },
}));

// TranscriptContent component definition
interface TranscriptContentProps {
  fps: number;
  searchQuery: string;
  currentMatchIndex: number;
  setCurrentMatchIndex: React.Dispatch<React.SetStateAction<number>>;
}

const TranscriptContent: React.FC<TranscriptContentProps> = React.memo(
  ({ fps, searchQuery, currentMatchIndex, setCurrentMatchIndex }) => {
    const subtitles = useClipSubtitles();
    const segments = useClipSegments();
    const { onSubtitlesChange } = useClipConfigAPI();
    const { classes } = useStyles({});
    const theme = useMantineTheme();
    const contentRef = useRef<HTMLDivElement>(null);
    const [selectedSubtitles, setSelectedSubtitles] = useState<Set<number>>(
      new Set()
    );
    const [contextMenuOpen, setContextMenuOpen] = useState(false);
    const [contextMenuItems, setContextMenuItems] = useState<
      ContextMenuItemProps[]
    >([]);
    const [contextMenuPosition, setContextMenuPosition] = useState<{
      top: number;
      left: number;
    }>({ top: 0, left: 0 });
    // Edit mode state
    const [editingSubtitleIndex, setEditingSubtitleIndex] = useState<
      number | null
    >(null);
    const [editingText, setEditingText] = useState<string>("");

    // Active subtitle tracking state managed imperatively
    const activeSubtitleIndexRef = useRef<number | null>(null);
    const [currentActiveSubtitle, setCurrentActiveSubtitle] = useState<
      number | null
    >(null);

    // Create a memoized calibrated subtitles array
    const calibratedSubtitles = useMemo(() => {
      if (!subtitles?.items || segments.length === 0) return [];
      return calibrateSubtitles(subtitles.items, segments);
    }, [subtitles?.items, segments]);

    // Implement the imperative ref functionality for active subtitle tracking
    useImperativeHandle(
      currentWordRef,
      () => ({
        setCurrentSubtitleIndex: (subtitleIndex: number) => {
          // Update the ref
          activeSubtitleIndexRef.current = subtitleIndex;
          // Update React state to trigger re-render
          setCurrentActiveSubtitle(subtitleIndex);
        },
        clearCurrentSubtitle: () => {
          // Clear the ref
          activeSubtitleIndexRef.current = null;
          // Update React state to trigger re-render
          setCurrentActiveSubtitle(null);
        },
      }),
      []
    );

    // On mount, check if there's an active subtitle and highlight it
    useEffect(() => {
      const subtitleIndex = getCurrentSubtitleIndex();

      if (subtitleIndex !== null && currentWordRef.current) {
        currentWordRef.current.setCurrentSubtitleIndex(subtitleIndex);
      }
    }, []);

    // Auto-scroll to the active subtitle when it changes
    useEffect(() => {
      if (activeSubtitleIndexRef.current !== null && contentRef.current) {
        const activeSubtitleIndex = activeSubtitleIndexRef.current;
        const el = contentRef.current.querySelector(
          `[data-subtitle-index="${activeSubtitleIndex}"]`
        ) as HTMLElement | null;

        if (el) {
          el.scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "nearest",
          });
        }
      }
    }, [currentActiveSubtitle]);

    // Handle subtitle selection
    const toggleSubtitleSelection = useCallback(
      (index: number, event: React.MouseEvent) => {
        event.stopPropagation();
        setSelectedSubtitles((prev) => {
          const newSelection = new Set(prev);
          if (newSelection.has(index)) {
            newSelection.delete(index);
          } else {
            newSelection.add(index);
          }
          return newSelection;
        });
      },
      []
    );

    // --- ACTION HANDLERS ---
    // Edit text handler
    const handleEditText = useCallback(
      (index: number) => {
        if (!subtitles?.items) return;
        const sortedSubtitles = [...subtitles.items]
          .filter((s) => s !== null && s !== undefined)
          .sort((a, b) => a.start - b.start);
        if (!sortedSubtitles[index]) return;
        setEditingSubtitleIndex(index);
        setEditingText(sortedSubtitles[index].text);
        setContextMenuOpen(false);
      },
      [subtitles]
    );

    // Save edit handler
    const handleSaveEdit = useCallback(() => {
      if (
        editingSubtitleIndex === null ||
        !subtitles?.items ||
        editingText.trim() === ""
      ) {
        setEditingSubtitleIndex(null);
        setEditingText("");
        return;
      }
      const sortedSubtitles = [...subtitles.items]
        .filter((s) => s !== null && s !== undefined)
        .sort((a, b) => a.start - b.start);
      const updated = sortedSubtitles.map((s, i) => {
        if (i !== editingSubtitleIndex) return s;
        // Update both text and words
        const wordsArr = editingText.trim().split(/\s+/);
        const wordCount = wordsArr.length;
        const totalDuration = s.end - s.start;
        let wordStart = s.start;
        const newWords = wordsArr.map((word, idx) => {
          // Distribute time evenly
          const wordDuration =
            idx === wordCount - 1
              ? s.end - wordStart // last word gets the rest
              : totalDuration / wordCount;
          const wordObj = {
            start: wordStart,
            end: wordStart + wordDuration,
            word,
          };
          wordStart += wordDuration;
          return wordObj;
        });
        return { ...s, text: editingText, words: newWords };
      });
      onSubtitlesChange({ ...subtitles, items: updated });
      setEditingSubtitleIndex(null);
      setEditingText("");
      setSelectedSubtitles(new Set());
    }, [editingSubtitleIndex, editingText, subtitles, onSubtitlesChange]);

    // Delete subtitle handler
    const handleDelete = useCallback(
      (index: number) => {
        if (!subtitles?.items) return;
        const sortedSubtitles = [...subtitles.items]
          .filter((s) => s !== null && s !== undefined)
          .sort((a, b) => a.start - b.start);
        const updated = sortedSubtitles.filter((_, i) => i !== index);
        onSubtitlesChange({ ...subtitles, items: updated });
      },
      [subtitles, onSubtitlesChange]
    );

    // Merge handler - keeping original implementation
    const handleMerge = useCallback(() => {
      if (selectedSubtitles.size < 2 || !subtitles?.items) return;
      const sortedIndices = Array.from(selectedSubtitles).sort((a, b) => a - b);
      // Check consecutive
      const areConsecutive = sortedIndices.every(
        (v, i, arr) => i === 0 || v === arr[i - 1] + 1
      );
      if (!areConsecutive) return;
      const sortedSubtitles = [...subtitles.items]
        .filter((s) => s !== null && s !== undefined)
        .sort((a, b) => a.start - b.start);
      // Merge text and words
      const mergedText = sortedIndices
        .map((i) => sortedSubtitles[i].text)
        .join(" ");
      const mergedWords = sortedIndices.flatMap(
        (i) => sortedSubtitles[i].words
      );
      const mergedSubtitle = {
        ...sortedSubtitles[sortedIndices[0]],
        text: mergedText,
        start: sortedSubtitles[sortedIndices[0]].start,
        end: sortedSubtitles[sortedIndices[sortedIndices.length - 1]].end,
        words: mergedWords,
      };
      // Remove all selected, insert merged at first index
      const updated = [
        ...sortedSubtitles.slice(0, sortedIndices[0]),
        mergedSubtitle,
        ...sortedSubtitles.slice(sortedIndices[sortedIndices.length - 1] + 1),
      ];
      onSubtitlesChange({ ...subtitles, items: updated });
      setSelectedSubtitles(new Set());
      setContextMenuOpen(false);
    }, [selectedSubtitles, subtitles, onSubtitlesChange]);

    // Context menu setup - with fixed type issues
    useEffect(() => {
      if (selectedSubtitles.size === 0) {
        setContextMenuOpen(false);
        return;
      }
      const items: ContextMenuItemProps[] = [];
      // Edit text only for single selection
      items.push({
        label: "Edit text",
        iconName: "edit",
        onClick: () => {
          // Call handleEditText with the selected subtitle index
          if (selectedSubtitles.size === 1) {
            const selectedIndex = Array.from(selectedSubtitles)[0];
            handleEditText(selectedIndex);
          }
        },
        variant: "primary",
        disabled: selectedSubtitles.size !== 1,
      });
      // Merge for multiple consecutive
      if (selectedSubtitles.size > 1) {
        const sortedIndices = Array.from(selectedSubtitles).sort(
          (a, b) => a - b
        );
        const areConsecutive = sortedIndices.every(
          (v, i, arr) => i === 0 || v === arr[i - 1] + 1
        );
        items.push({
          label: "Merge",
          iconName: "merge",
          onClick: areConsecutive ? handleMerge : undefined,
          variant: "primary",
          disabled: !areConsecutive,
        });
      }
      // Delete always enabled
      items.push({
        label: "Delete",
        iconName: "trash-can",
        onClick: () => {
          // Call handleDelete for each selected subtitle
          const sortedIndices = Array.from(selectedSubtitles).sort(
            (a, b) => b - a
          ); // Delete in reverse order
          sortedIndices.forEach((index) => handleDelete(index));
          setSelectedSubtitles(new Set());
          setContextMenuOpen(false);
        },
        variant: "primary",
      });
      setContextMenuItems(items);

      // Position the context menu
      if (selectedSubtitles.size > 0) {
        // For positioning, use the most recent selection
        const selectedIndex =
          Array.from(selectedSubtitles)[selectedSubtitles.size - 1];
        const elements = contentRef.current?.querySelectorAll(
          "[data-subtitle-index]"
        );
        const selectedElement =
          elements && (elements[selectedIndex] as HTMLElement);

        if (selectedElement && contentRef.current) {
          const rect = selectedElement.getBoundingClientRect();
          const contentRect = contentRef.current.getBoundingClientRect();

          // Estimate menu width based on number of buttons
          // Each button is ~100-120px wide on average plus some padding
          const estimatedMenuWidth = items.length * 120 + 16;
          const safeMargin = 16; // Safe margin from the right edge

          // Calculate initial position
          let menuLeft = rect.left - contentRect.left;

          // Check if menu would go beyond the right edge
          const rightEdgePosition = menuLeft + estimatedMenuWidth;
          const containerWidth = contentRect.width;

          if (rightEdgePosition > containerWidth - safeMargin) {
            // Adjust menu position to ensure it fits within the container
            menuLeft = Math.max(
              0,
              containerWidth - estimatedMenuWidth - safeMargin
            );
          }

          setContextMenuPosition({
            top: rect.bottom - contentRect.top,
            left: menuLeft,
          });

          setContextMenuOpen(true);
        }
      }
    }, [selectedSubtitles, handleEditText, handleMerge, handleDelete]);

    // Clear selection when clicking outside
    useEffect(() => {
      const handleClickOutside = (e: MouseEvent) => {
        if (
          contentRef.current &&
          !contentRef.current.contains(e.target as Node)
        ) {
          setSelectedSubtitles(new Set());
          setContextMenuOpen(false);
        }
      };

      document.addEventListener("mousedown", handleClickOutside);

      // Handle focus out
      const handleFocusOut = () => {
        setContextMenuOpen(false);
      };

      document.addEventListener("focusout", handleFocusOut);

      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        document.removeEventListener("focusout", handleFocusOut);
      };
    }, []);

    // --- SEARCH MATCH LOGIC ---
    // Compute all subtitle indices that match the search query using calibrated subtitles
    const matchIndices = useMemo(() => {
      if (
        !searchQuery ||
        !calibratedSubtitles ||
        calibratedSubtitles.length === 0
      )
        return [];
      const query = searchQuery.trim().toLowerCase();
      return calibratedSubtitles
        .map((subtitle, idx) =>
          subtitle && subtitle.text.toLowerCase().includes(query) ? idx : -1
        )
        .filter((idx) => idx !== -1);
    }, [searchQuery, calibratedSubtitles]);

    // Cycle currentMatchIndex if out of bounds
    useEffect(() => {
      if (currentMatchIndex >= matchIndices.length && matchIndices.length > 0) {
        setCurrentMatchIndex(0);
      }
    }, [currentMatchIndex, matchIndices.length, setCurrentMatchIndex]);

    // Scroll to the current match
    useEffect(() => {
      if (!contentRef.current || matchIndices.length === 0) return;
      const matchIdx =
        matchIndices[
          currentMatchIndex >= matchIndices.length ? 0 : currentMatchIndex
        ];
      if (matchIdx === undefined) return;
      const el = contentRef.current.querySelector(
        `[data-subtitle-index="${matchIdx}"]`
      ) as HTMLElement | null;
      if (el) {
        el.scrollIntoView({
          behavior: "smooth",
          block: "center",
          inline: "nearest",
        });
      }
    }, [currentMatchIndex, matchIndices, searchQuery]);

    // Memoize the transcript text to avoid unnecessary re-renders
    const transcriptContent = useMemo(() => {
      if (!calibratedSubtitles || calibratedSubtitles.length === 0) {
        return (
          <Box style={{ height: "100%", minHeight: "400px" }}>
            <Text className={classes.transcriptText}>
              No transcript available
            </Text>
          </Box>
        );
      }

      // If there's a search query, show search results
      if (searchQuery && searchQuery.trim() !== "") {
        const query = searchQuery.toLowerCase();
        return (
          <Box
            style={{ height: "100%", minHeight: "400px", position: "relative" }}
          >
            <Text className={classes.transcriptText}>
              {calibratedSubtitles.map((subtitle, subtitleIndex) => {
                const text = subtitle.text;
                const lowerText = text.toLowerCase();
                const matchPos = lowerText.indexOf(query);
                const isCurrentMatch =
                  matchIndices.length > 0 &&
                  matchIndices[
                    currentMatchIndex >= matchIndices.length
                      ? 0
                      : currentMatchIndex
                  ] === subtitleIndex;
                if (matchPos === -1) {
                  return (
                    <span
                      key={subtitleIndex}
                      data-subtitle-index={subtitleIndex}
                    >
                      {text + " "}
                    </span>
                  );
                }
                // Highlight only the current match
                if (isCurrentMatch) {
                  return (
                    <span
                      key={subtitleIndex}
                      data-subtitle-index={subtitleIndex}
                      style={{ display: "inline", position: "relative" }}
                    >
                      {text.substring(0, matchPos)}
                      <span
                        style={{
                          backgroundColor: "#FFE100",
                          color: "#000000",
                          boxSizing: "border-box",
                          display: "inline",
                          borderRadius: "2px",
                        }}
                      >
                        {text.substring(matchPos, matchPos + query.length)}
                      </span>
                      {text.substring(matchPos + query.length) + " "}
                    </span>
                  );
                }
                // Not current match, render normally
                return (
                  <span key={subtitleIndex} data-subtitle-index={subtitleIndex}>
                    {text + " "}
                  </span>
                );
              })}
            </Text>
          </Box>
        );
      }

      // If no search query, show the transcript with selectable subtitle sections
      return (
        <Box
          style={{ height: "100%", minHeight: "400px", position: "relative" }}
        >
          <Text className={classes.transcriptText}>
            {calibratedSubtitles.map((subtitle, subtitleIndex) => {
              const isSelected = selectedSubtitles.has(subtitleIndex);
              const isCurrentSubtitle =
                activeSubtitleIndexRef.current === subtitleIndex;

              // Inline edit UI
              if (editingSubtitleIndex === subtitleIndex) {
                return (
                  <span
                    key={subtitleIndex}
                    data-subtitle-index={subtitleIndex}
                    style={{
                      display: "inline",
                      position: "relative",
                      borderRadius: "4px",
                      backgroundColor: "#E1E1E1",
                      color: "#121212",
                      padding: "2px 0",
                    }}
                    tabIndex={0}
                    onBlur={handleSaveEdit}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") handleSaveEdit();
                      if (e.key === "Escape") {
                        setEditingSubtitleIndex(null);
                        setEditingText("");
                      }
                    }}
                  >
                    <VFTextbox
                      value={editingText}
                      onChange={setEditingText}
                      width={200}
                    />
                  </span>
                );
              }

              return (
                <span
                  key={subtitleIndex}
                  data-subtitle-index={subtitleIndex}
                  style={{
                    display: "inline",
                    position: "relative",
                    cursor: "pointer",
                    padding: "2px 4px",
                    margin: "0 2px",
                    borderRadius: "4px",
                    backgroundColor:
                      isSelected || isCurrentSubtitle
                        ? "#E1E1E1"
                        : "transparent",
                    color:
                      isSelected || isCurrentSubtitle ? "#121212" : "#FFFFFF",
                  }}
                  onClick={(e) => toggleSubtitleSelection(subtitleIndex, e)}
                >
                  {subtitle.text}
                </span>
              );
            })}
          </Text>

          {contextMenuOpen && (
            <Box
              style={{
                position: "absolute",
                top: contextMenuPosition.top,
                left: contextMenuPosition.left,
                zIndex: 1000,
              }}
            >
              <VFContextMenu
                items={contextMenuItems}
                isOpen={contextMenuOpen}
                position="left"
                onClose={() => setContextMenuOpen(false)}
              />
            </Box>
          )}
        </Box>
      );
    }, [
      calibratedSubtitles,
      searchQuery,
      classes.transcriptText,
      selectedSubtitles,
      currentActiveSubtitle, // Use React state instead of direct ref
      contextMenuOpen,
      contextMenuItems,
      contextMenuPosition,
      toggleSubtitleSelection,
      editingSubtitleIndex,
      editingText,
      handleSaveEdit,
      matchIndices,
      currentMatchIndex,
    ]);

    return (
      <Box
        ref={contentRef}
        style={{ height: "100%", minHeight: "400px", position: "relative" }}
      >
        {transcriptContent}
      </Box>
    );
  }
);

interface TranscriptProps {
  fps: number;
}

const Transcript: React.FC<TranscriptProps> = ({ fps }) => {
  const { classes, cx } = useStyles({});
  const [searchValue, setSearchValue] = useState("");
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);

  // Reset match index when search changes
  const handleSearchChange = React.useCallback((value: string) => {
    setSearchValue(value);
    setCurrentMatchIndex(0);
  }, []);

  // Handle Enter key in search field
  const handleSearchKeyDown = React.useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Enter") {
        setCurrentMatchIndex((prev) => prev + 1); // Will cycle in TranscriptContent
      }
    },
    []
  );

  return (
    <Box className={classes.transcriptContainer}>
      {/* Header */}
      <Box className={classes.transcriptHeader}>
        <Box className={classes.transcriptTitle}>
          <VFIconComponent
            type="captions"
            color="#FFFFFF"
            dimensions={{ width: 16, height: 16 }}
          />
          <span
            style={{
              color: "#FFFFFF",
            }}
            className="p-heavy"
          >
            Transcript
          </span>
        </Box>
      </Box>

      {/* Search bar */}
      <Box className={classes.transcriptSearchWrapper}>
        <Box className={classes.customTextbox}>
          <VFTextbox
            width="100%"
            placeholder="Search in transcript"
            value={searchValue}
            onChange={handleSearchChange}
            rightIconType="search"
            useFixedWidth={true}
            onKeyDown={handleSearchKeyDown}
          />
        </Box>
      </Box>

      {/* Content */}
      <ScrollArea
        className={classes.transcriptContent}
        styles={{
          viewport: { paddingRight: "16px" },
          root: { height: "calc(100% - 108px)" },
        }}
      >
        <TranscriptContent
          fps={fps}
          searchQuery={searchValue}
          currentMatchIndex={currentMatchIndex}
          setCurrentMatchIndex={setCurrentMatchIndex}
        />
      </ScrollArea>
    </Box>
  );
};

export default Transcript;
