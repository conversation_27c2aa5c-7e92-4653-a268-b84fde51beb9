import React, { useState, useMemo, useEffect } from "react";
import { Box, Group, createStyles } from "@mantine/core";
import { VFTextbox } from "../ui/Textbox";
import { VFColorPicker } from "../ui/ColorPicker";
import { VFButton } from "../ui/Button";
import { ButtonGroup, ButtonGroupItem } from "../ui/ButtonGroup";
import { VFSelector } from "../ui/Selector";
import { hexToRgb } from "../../utils";
import { PresetConfig } from "../../remotion/types";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { FontItem } from "../../remotion/types";

interface StylesProps {
  usePercentageLayout: boolean;
}

const useStyles = createStyles(
  (theme, { usePercentageLayout }: StylesProps) => ({
    settingRow: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "12px",
    },
    settingLabel: {
      color: theme.other["surface-subtext-color"],
      display: "block",
      width: usePercentageLayout ? "40%" : "auto",
    },
    settingInput: {
      width: usePercentageLayout ? "60%" : "140px",
    },
    colorRow: {
      display: "flex",
      alignItems: "center",
      gap: "8px",
      width: usePercentageLayout ? "40%" : "140px",
    },
    typographyControls: {
      display: "flex",
      flexDirection: "column",
      gap: "12px",
    },
    colorPickerWrapper: {
      position: "relative",
      zIndex: 5,
    },
    fontSelector: {
      position: "relative",
      width: usePercentageLayout ? "70%" : "272px",
    },
    fontColorPicker: {
      width: usePercentageLayout ? "30%" : "140px",
    },
    fontWeightSelector: {
      position: "relative",
      width: usePercentageLayout ? "40%" : "150px",
    },
    fontSizeInput: {
      width: usePercentageLayout ? "40%" : "110px",
    },
    alignmentButtonGroup: {
      width: usePercentageLayout ? "20%" : "120px",
    },
    strokeInput: {
      width: usePercentageLayout ? "70%" : "214px",
    },
    strokeColor: {
      width: usePercentageLayout ? "30%" : "200px",
    },
    colorInput: {
      width: usePercentageLayout ? "40%" : "140px",
    },
    shadowControls: {
      display: "flex",
      gap: "8px",
      justifyContent: "space-between",
    },
    shadowColorPicker: {
      width: usePercentageLayout ? "25%" : "194px",
    },
    shadowOffset: {
      width: usePercentageLayout ? "25%" : "66px",
    },
    animationDropdown: {
      width: usePercentageLayout ? "40%" : "200px",
    },
    // Special setting labels for current word, amplified word, and animation
    specialSettingLabel: {
      color: theme.other["surface-subtext-color"],
      display: "block",
      width: usePercentageLayout ? "60%" : "auto",
    },
    // New styles for section headers with toggle buttons
    sectionHeader: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
    },
  })
);

interface PresetSettingsProps {
  preset: PresetConfig;
  onChange: (updated: PresetConfig) => void;
  wordsPerLine: number;
  onWordsPerLineChange: (value: number) => void;
  isFetchingSubtitles: boolean;
  usePercentageLayout?: boolean;
}

export function PresetSettings({
  preset,
  onChange,
  wordsPerLine,
  onWordsPerLineChange,
  isFetchingSubtitles,
  usePercentageLayout = false,
}: PresetSettingsProps) {
  const { classes, cx } = useStyles({ usePercentageLayout });

  // Get fonts from Redux store
  const fonts = useSelector((state: RootState) => state.presets.fonts);

  // Local state for dropdowns
  const [fontSelectorOpen, setFontSelectorOpen] = useState(false);
  const [fontWeightSelectorOpen, setFontWeightSelectorOpen] = useState(false);

  // Initialize font weight as current sub-family from preset
  const [fontWeight, setFontWeight] = useState<string>(() => {
    let displayWeight = preset.font.sub_family;
    if (displayWeight === "normal") return "Regular";
    if (
      displayWeight === "bold" ||
      displayWeight === "light" ||
      displayWeight === "medium"
    ) {
      return displayWeight.charAt(0).toUpperCase() + displayWeight.slice(1);
    }
    return displayWeight;
  });

  // Alignment is not part of PresetConfig, so keep it local only
  const [textAlignment, setTextAlignment] = useState<string>("center");
  const [animationSelectorOpen, setAnimationSelectorOpen] = useState(false);

  // State for expanded/collapsed sections
  const [isStrokeExpanded, setIsStrokeExpanded] = useState<boolean>(
    !!preset.stroke
  );
  const [isShadowExpanded, setIsShadowExpanded] = useState<boolean>(
    !!preset.shadow
  );
  const [isCurrentWordBgEnabled, setIsCurrentWordBgEnabled] = useState<boolean>(
    !!preset.current_word_bg_color
  );
  const [isCurrentWordColorEnabled, setIsCurrentWordColorEnabled] =
    useState<boolean>(!!preset.current_word_color);

  // Create font options from all available fonts in Redux store
  const fontOptions = useMemo(() => {
    if (!fonts) return [];

    // Combine system and user fonts
    const allFonts = [...(fonts.system || []), ...(fonts.user || [])];

    // Map to options format for selector
    return allFonts.map((font: FontItem) => ({
      value: font.family,
      label: font.family,
    }));
  }, [fonts]);

  // Get sub-families (weights) for the currently selected font
  const fontWeightOptions = useMemo(() => {
    if (!fonts) return [];

    // Combine system and user fonts
    const allFonts = [...(fonts?.system || []), ...(fonts?.user || [])];

    // Find the current font
    const currentFont = allFonts.find(
      (font) => font.family === preset.font.family
    );

    if (!currentFont) {
      return [
        { value: "Light", label: "Light" },
        { value: "Regular", label: "Regular" },
        { value: "Medium", label: "Medium" },
        { value: "Bold", label: "Bold" },
      ];
    }

    // Map sub-families to options
    return currentFont.sub_families.map((subFamily) => {
      let displayName = subFamily.sub_family;

      // Capitalize first letter for display
      if (displayName === "normal") displayName = "Regular";
      else if (
        displayName === "bold" ||
        displayName === "light" ||
        displayName === "medium"
      ) {
        displayName =
          displayName.charAt(0).toUpperCase() + displayName.slice(1);
      }

      return {
        value: subFamily.sub_family,
        label: displayName,
      };
    });
  }, [fonts, preset.font.family]);

  // Get animation options from Redux store or define defaults
  const animationOptions = useMemo(() => {
    // This would ideally come from the API in the future
    // For now, providing these as defaults
    return [
      { value: "Bounce", label: "Bounce" },
      { value: "Fade", label: "Fade" },
      { value: "Slide", label: "Slide" },
      { value: "None", label: "None" },
    ];
  }, []);

  // Handlers
  function handleFontChange(selectedValue: string) {
    if (selectedValue) {
      // Find the selected font to get default sub_family
      const allFonts = [...(fonts?.system || []), ...(fonts?.user || [])];
      const selectedFont = allFonts.find(
        (font) => font.family === selectedValue
      );

      // Default to first sub-family if available
      const defaultSubFamily =
        selectedFont?.sub_families[0]?.sub_family || "normal";

      onChange({
        ...preset,
        font: {
          family: selectedValue,
          sub_family: defaultSubFamily,
        },
      });

      // Also update the local state for the weight dropdown
      setFontWeight(defaultSubFamily);
    }
  }

  function handleFontWeightChange(weight: string) {
    setFontWeight(weight);
    onChange({
      ...preset,
      font: {
        ...preset.font,
        sub_family: weight,
      },
    });
  }

  function handleSizeChange(value: string) {
    const size = parseInt(value) || 40;
    onChange({
      ...preset,
      size,
    });
  }

  // change all functions to arrow functions in file

  const handleCurrentWordBgColorChange = (color: string) => {
    onChange({
      ...preset,
      current_word_bg_color: color,
    });
  };

  const handleAlignmentChange = (value: string) => {
    setTextAlignment(value);
    // Alignment is not part of PresetConfig, so do not call onChange for it
  };

  const handleStrokeWeightChange = (value: string) => {
    const weight = parseInt(value) || 0;
    onChange({
      ...preset,
      stroke:
        weight > 0
          ? {
              color: preset.stroke?.color || "#000000",
              weight,
              opacity: preset.stroke?.opacity || 100,
            }
          : null,
    });
  };

  const handleStrokeColorChange = (color: string) => {
    const opacity = parseFloat(color.split(",")[3] || "1") * 100;
    const rgbColor = color.substring(0, color.lastIndexOf(",")) + ")";
    onChange({
      ...preset,
      stroke: {
        ...(preset.stroke || { weight: 0 }),
        color: rgbColor,
        opacity: opacity,
      },
    });
  };

  const handleShadowAdd = () => {
    setIsShadowExpanded(true);
    onChange({
      ...preset,
      shadow: {
        color: "#000000",
        opacity: 100,
        offset: { x: 1, y: 4 },
        blur: 0,
      },
    });
  };

  const handleShadowColorChange = (color: string) => {
    const opacity = parseFloat(color.split(",")[3] || "1") * 100;
    const rgbColor = color.substring(0, color.lastIndexOf(",")) + ")";
    onChange({
      ...preset,
      shadow: {
        ...preset.shadow!,
        color: rgbColor,
        opacity: opacity,
      },
    });
  };

  const handleShadowOffsetXChange = (value: string) => {
    const x = parseInt(value) || 0;
    onChange({
      ...preset,
      shadow: {
        ...preset.shadow!,
        offset: {
          ...(preset.shadow?.offset || { y: 0 }),
          x,
        },
      },
    });
  };

  const handleShadowOffsetYChange = (value: string) => {
    const y = parseInt(value) || 0;
    onChange({
      ...preset,
      shadow: {
        ...preset.shadow!,
        offset: {
          ...(preset.shadow?.offset || { x: 0 }),
          y,
        },
      },
    });
  };

  const handleShadowBlurChange = (value: string) => {
    const blur = parseInt(value) || 0;
    onChange({
      ...preset,
      shadow: {
        ...preset.shadow!,
        blur,
      },
    });
  };

  const handleCurrentWordColorChange = (color: string) => {
    onChange({
      ...preset,
      current_word_color: color,
    });
  };

  const handleColorChange = (color: string) => {
    onChange({
      ...preset,
      color,
    });
  };

  const handleAmplifiedWordColorChange = (color: string) => {
    onChange({
      ...preset,
      amplified_word_color: color,
    });
  };

  const toggleStrokeExpanded = () => {
    const newExpandedState = !isStrokeExpanded;
    setIsStrokeExpanded(newExpandedState);

    // If collapsing, set stroke to null in the preset
    if (!newExpandedState) {
      onChange({
        ...preset,
        stroke: null,
      });
    } else if (newExpandedState && !preset.stroke) {
      // If expanding and no stroke exists, initialize with default values
      onChange({
        ...preset,
        stroke: {
          color: "#000000",
          weight: 1,
          opacity: 100,
        },
      });
    }
  };

  const toggleShadowExpanded = () => {
    const newExpandedState = !isShadowExpanded;
    setIsShadowExpanded(newExpandedState);

    // If collapsing, set shadow to null in the preset
    if (!newExpandedState) {
      onChange({
        ...preset,
        shadow: null,
      });
    } else if (newExpandedState && !preset.shadow) {
      // If expanding and no shadow exists, add a default shadow
      handleShadowAdd();
    }
  };

  const toggleCurrentWordColor = () => {
    const newEnabledState = !isCurrentWordColorEnabled;
    setIsCurrentWordColorEnabled(newEnabledState);

    // If disabling, set current_word_color to null in the preset
    if (!newEnabledState) {
      onChange({
        ...preset,
        current_word_color: null,
      });
    } else if (newEnabledState && !preset.current_word_color) {
      // If enabling and no color exists, initialize with default value
      onChange({
        ...preset,
        current_word_color: "#FFE100",
      });
    }
  };

  const toggleCurrentWordBg = () => {
    const newEnabledState = !isCurrentWordBgEnabled;
    setIsCurrentWordBgEnabled(newEnabledState);

    // If disabling, set current_word_bg_color to null in the preset
    if (!newEnabledState) {
      onChange({
        ...preset,
        current_word_bg_color: null,
      });
    } else if (newEnabledState && !preset.current_word_bg_color) {
      // If enabling and no background color exists, initialize with default value
      onChange({
        ...preset,
        current_word_bg_color: "#000000",
      });
    }
  };

  // Add useEffect to log when wordsPerLine changes
  useEffect(() => {
    console.log(`PresetSettings wordsPerLine prop updated: ${wordsPerLine}`);
  }, [wordsPerLine]);

  return (
    <Box
      style={{ padding: `${!usePercentageLayout ? "24px" : "0"} 0 24px 16px` }}
    >
      {/* Words per line */}
      <Box className={classes.settingRow}>
        <span className={cx(classes.settingLabel, "p-regular")}>
          Words per line
        </span>
        <Box className={classes.settingInput}>
          <VFTextbox
            type="numeric"
            width="100%"
            value={String(wordsPerLine)}
            onChange={(v: string) => onWordsPerLineChange(parseInt(v) || 3)}
            min={1}
            max={10}
            useFixedWidth={false}
            disabled={isFetchingSubtitles}
          />
        </Box>
      </Box>
      {/* Typography */}
      <Box style={{ marginTop: "16px" }}>
        <span
          className={cx(classes.settingLabel, "small-p-heavy")}
          style={{ marginBottom: "16px", width: "auto" }}
        >
          Typography
        </span>
        <Box className={classes.typographyControls}>
          {/* First row: Font selector + Color picker */}
          <Group spacing={8} position="apart" noWrap>
            <Box className={classes.fontSelector}>
              <VFButton
                variant="secondary"
                buttonDisplay="dropdown"
                style={{ width: "100%" }}
                onClick={() => setFontSelectorOpen(!fontSelectorOpen)}
              >
                {preset.font.family || "Arial"}
              </VFButton>
              <VFSelector
                options={fontOptions}
                value={
                  fontOptions.find((f) => f.value === preset.font.family)
                    ?.value || fontOptions[0].value
                }
                onChange={(selectedValue: string | null) =>
                  selectedValue && handleFontChange(selectedValue)
                }
                isOpen={fontSelectorOpen}
                onClose={() => setFontSelectorOpen(false)}
              />
            </Box>
            <Box
              className={cx(
                classes.colorPickerWrapper,
                classes.fontColorPicker
              )}
            >
              <VFColorPicker
                value={preset.color || "#FFFFFF"}
                onChange={handleColorChange}
                width="100%"
              />
            </Box>
          </Group>
          {/* Second row: Font weight + size + alignment */}
          <Group
            spacing={8}
            position="apart"
            noWrap
            style={{ marginTop: "12px" }}
          >
            <Box className={classes.fontWeightSelector}>
              <VFButton
                variant="secondary"
                buttonDisplay="dropdown"
                style={{ width: "100%" }}
                onClick={() =>
                  setFontWeightSelectorOpen(!fontWeightSelectorOpen)
                }
              >
                {fontWeight}
              </VFButton>
              <VFSelector
                options={fontWeightOptions}
                value={fontWeight}
                onChange={(weight: string | null) =>
                  weight && handleFontWeightChange(weight)
                }
                isOpen={fontWeightSelectorOpen}
                onClose={() => setFontWeightSelectorOpen(false)}
              />
            </Box>
            <Box className={classes.fontSizeInput}>
              <VFTextbox
                type="numeric"
                width="100%"
                value={(preset.size || 40).toString()}
                onChange={handleSizeChange}
                min={10}
                max={100}
                useFixedWidth={false}
              />
            </Box>
            <Box className={classes.alignmentButtonGroup}>
              <ButtonGroup fullWidth onChange={handleAlignmentChange}>
                <ButtonGroupItem
                  value="left"
                  selected={textAlignment === "left"}
                  buttonDisplay="onlyIcon"
                  iconName="align-left"
                  variant="secondary"
                />
                <ButtonGroupItem
                  value="center"
                  selected={textAlignment === "center"}
                  buttonDisplay="onlyIcon"
                  iconName="align-center"
                  variant="secondary"
                />
                <ButtonGroupItem
                  value="right"
                  selected={textAlignment === "right"}
                  buttonDisplay="onlyIcon"
                  iconName="align-right"
                  variant="secondary"
                />
              </ButtonGroup>
            </Box>
          </Group>
        </Box>
      </Box>
      {/* Stroke */}
      <Box style={{ marginTop: "16px" }}>
        <Box className={classes.sectionHeader}>
          <span
            className={cx(classes.settingLabel, "small-p-heavy")}
            style={{ marginBottom: "8px", display: "block", width: "auto" }}
          >
            Stroke
          </span>
          <VFButton
            variant="secondary"
            buttonDisplay="onlyIcon"
            iconName={isStrokeExpanded ? "minus" : "plus"}
            onClick={toggleStrokeExpanded}
          />
        </Box>
        {isStrokeExpanded && (
          <Box
            style={{
              display: "flex",
              gap: "8px",
              justifyContent: "space-between",
            }}
          >
            <Box className={classes.strokeInput}>
              <VFTextbox
                type="numeric"
                width="100%"
                leftIconType="stroke"
                value={
                  preset.stroke && preset.stroke.weight
                    ? preset.stroke.weight.toString()
                    : "0"
                }
                onChange={handleStrokeWeightChange}
                min={0}
                max={10}
                useFixedWidth={false}
              />
            </Box>
            <Box className={classes.strokeColor}>
              <VFColorPicker
                value={`rgba(${hexToRgb(preset.stroke?.color || "#000000")}, ${
                  (preset.stroke?.opacity || 100) / 100
                })`}
                onChange={handleStrokeColorChange}
                width="100%"
                format="rgba"
                withAlpha={true}
              />
            </Box>
          </Box>
        )}
      </Box>
      {/* Shadow */}
      <Box style={{ marginTop: "16px" }}>
        <Box className={classes.sectionHeader}>
          <span
            className={cx(classes.settingLabel, "small-p-heavy")}
            style={{ width: "auto" }}
          >
            Shadow
          </span>
          <VFButton
            variant="secondary"
            buttonDisplay="onlyIcon"
            iconName={isShadowExpanded && preset.shadow ? "minus" : "plus"}
            onClick={toggleShadowExpanded}
          />
        </Box>
        {isShadowExpanded && preset.shadow && (
          <Box className={classes.shadowControls}>
            <Box className={classes.shadowColorPicker}>
              <VFColorPicker
                value={`rgba(${hexToRgb(preset.shadow?.color || "#000000")}, ${
                  (preset.shadow?.opacity || 100) / 100
                })`}
                onChange={handleShadowColorChange}
                width="100%"
                format="rgba"
                withAlpha={true}
              />
            </Box>
            <Box className={classes.shadowOffset}>
              <VFTextbox
                type="numeric"
                width="100%"
                leftIconType="x"
                value={preset.shadow?.offset?.x?.toString() || "1"}
                onChange={handleShadowOffsetXChange}
                min={-20}
                max={20}
                useFixedWidth={false}
                showNumericControls={false}
              />
            </Box>
            <Box className={classes.shadowOffset}>
              <VFTextbox
                type="numeric"
                width="100%"
                leftIconType="y"
                value={preset.shadow?.offset?.y?.toString() || "4"}
                onChange={handleShadowOffsetYChange}
                min={-20}
                max={20}
                useFixedWidth={false}
                showNumericControls={false}
              />
            </Box>
            <Box className={classes.shadowOffset}>
              <VFTextbox
                type="numeric"
                width="100%"
                leftIconType="blur"
                value={preset.shadow?.blur?.toString() || "0"}
                onChange={handleShadowBlurChange}
                min={0}
                max={20}
                useFixedWidth={false}
                showNumericControls={false}
              />
            </Box>
          </Box>
        )}
      </Box>
      {/* Current word color */}
      <Box style={{ marginTop: "16px" }}>
        <Box className={classes.settingRow}>
          <span
            className={cx(classes.specialSettingLabel, "p-regular")}
            style={{ width: usePercentageLayout ? "40%" : "auto" }}
          >
            Current word color
          </span>
          {isCurrentWordColorEnabled ? (
            <Group
              spacing={8}
              position="right"
              noWrap
              style={{ width: usePercentageLayout ? "60%" : "auto" }}
            >
              <Box
                className={classes.colorInput}
                style={{
                  width: usePercentageLayout ? "calc(100% - 40px)" : "125px",
                }}
              >
                <VFColorPicker
                  value={preset.current_word_color || "#FFE100"}
                  onChange={handleCurrentWordColorChange}
                  width="100%"
                />
              </Box>
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName="minus"
                onClick={toggleCurrentWordColor}
              />
            </Group>
          ) : (
            <VFButton
              variant="secondary"
              buttonDisplay="onlyIcon"
              iconName="plus"
              onClick={toggleCurrentWordColor}
            />
          )}
        </Box>
      </Box>
      {/* Current word background color */}
      <Box style={{ marginTop: "16px" }}>
        <Box className={classes.settingRow}>
          <span
            className={cx(classes.specialSettingLabel, "p-regular")}
            style={{ width: usePercentageLayout ? "40%" : "auto" }}
          >
            Current word background color
          </span>
          {isCurrentWordBgEnabled ? (
            <Group
              spacing={8}
              position="right"
              noWrap
              style={{ width: usePercentageLayout ? "60%" : "auto" }}
            >
              <Box
                className={classes.colorInput}
                style={{
                  width: usePercentageLayout ? "calc(100% - 40px)" : "125px",
                }}
              >
                <VFColorPicker
                  value={preset.current_word_bg_color || "#000000"}
                  onChange={handleCurrentWordBgColorChange}
                  width="100%"
                />
              </Box>
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName="minus"
                onClick={toggleCurrentWordBg}
              />
            </Group>
          ) : (
            <VFButton
              variant="secondary"
              buttonDisplay="onlyIcon"
              iconName="plus"
              onClick={toggleCurrentWordBg}
            />
          )}
        </Box>
      </Box>
      {/* Amplified word color */}
      {/* <Box style={{ marginTop: "16px" }}>
        <Box className={classes.settingRow}>
          <span className={cx(classes.specialSettingLabel, "p-regular")}>
            Amplified word color
          </span>
          <Box className={classes.colorInput}>
            <VFColorPicker
              value={preset.amplified_word_color || "#88C540"}
              onChange={handleAmplifiedWordColorChange}
              width="100%"
            />
          </Box>
        </Box>
      </Box> */}
      {/* Animation */}
      {/* <Box style={{ marginTop: "16px" }}>
        <Box className={classes.settingRow}>
          <span className={cx(classes.specialSettingLabel, "p-regular")}>
            Animation
          </span>
          <Box
            className={classes.animationDropdown}
            style={{ display: "flex", gap: "8px", alignItems: "center" }}
          >
            <Box style={{ position: "relative", width: "80%" }}>
              <VFButton
                variant="secondary"
                buttonDisplay="dropdown"
                style={{ width: "100%" }}
                onClick={() => setAnimationSelectorOpen(!animationSelectorOpen)}
              >
                Bounce
              </VFButton>
              <VFSelector
                options={animationOptions}
                value="Bounce"
                onChange={() => {}}
                isOpen={animationSelectorOpen}
                onClose={() => setAnimationSelectorOpen(false)}
              />
            </Box>
            <Box style={{ width: "20%" }}>
              <VFButton
                variant="secondary"
                buttonDisplay="withIcon"
                iconName="close"
                fullWidth
              />
            </Box>
          </Box>
        </Box>
      </Box> */}
    </Box>
  );
}
