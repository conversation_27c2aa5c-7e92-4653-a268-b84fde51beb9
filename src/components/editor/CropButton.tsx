import React from "react";
import { useClipConfigAPI } from "../../features/clipConfig/ClipConfigApi";
import {
  getCurrentSegmentIndex,
  getTemporaryCropBox,
} from "../../remotion/imperative-state";
import { VFButton } from "../ui/Button";

interface CropButtonProps {
  cropMode: boolean;
  setCropMode: (v: boolean) => void;
  clipData: any;
  setClipData: (data: any) => void;
}

export function CropButton({
  cropMode,
  setCropMode,
  clipData,
  setClipData,
}: CropButtonProps) {
  const { onSegmentsChange } = useClipConfigAPI();

  return (
    <VFButton
      onClick={() => {
        if (cropMode) {
          const tempCropBox = getTemporaryCropBox();
          const currentSegmentIndex = getCurrentSegmentIndex();

          if (
            tempCropBox &&
            clipData &&
            Array.isArray(clipData.segments) &&
            currentSegmentIndex !== null &&
            currentSegmentIndex >= 0 &&
            currentSegmentIndex < clipData.segments.length
          ) {
            const updatedSegments = [...clipData.segments];
            updatedSegments[currentSegmentIndex] = {
              ...updatedSegments[currentSegmentIndex],
              crop_box: tempCropBox,
            };
            onSegmentsChange(updatedSegments);
            setClipData({ ...clipData, segments: updatedSegments });
          }
        }
        setCropMode(!cropMode);
      }}
      buttonDisplay="withIcon"
      iconName={"crop"}
      style={{
        position: "absolute",
        top: 0,
        left: "50%",
        transform: "translateX(-50%)",
        zIndex: 1000,
      }}
    >
      {!cropMode ? "Crop" : "Save crop"}
    </VFButton>
  );
}
