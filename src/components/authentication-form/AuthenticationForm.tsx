import { useToggle } from "@mantine/hooks";
import { useForm } from "@mantine/form";
import {
  Paper,
  PaperProps,
  createStyles,
  Transition,
  Checkbox,
  useMantineTheme,
} from "@mantine/core";
import { showNotification } from "@mantine/notifications";
import { FC, useState } from "react";
import envConfig from "../../envConfig";
import { useDispatch } from "react-redux";
import type { AppDispatch } from "../../store";
import {
  registerUser,
  userLogin,
  getUserData,
} from "../../features/auth/authActions";
import { ErrorResponse, ILoginPayload, IRegisterPayload } from "../../types";
import { fetchFonts, fetchPresets } from "../../features/presets/presetsSlice";
import { loadAllFonts } from "../../features/presets/fontUtils";
import { FontsResponse } from "../../features/presets/types";
import "./AuthenticationForm.scss";
import LogoImage from "./../../assets/images/boostcast-logo.svg";
import { useModal } from "../../hooks/useModal";
import { ModalConfig, ModalType } from "../../features/modal/types";
import Turnstile from "react-turnstile";
import { VFTextbox } from "./../ui/Textbox";
import { VFButton } from "./../ui/Button";
import { VFCheckbox } from "./../ui/Checkbox";
import { useErrorHandler } from "../../hooks/useErrorHandler";
import { VFNotification } from "./../ui/Notification";
import { parseErrorMessage } from "../../utils";

const useStyles = createStyles((theme) => ({
  root: {
    width: 540,
    margin: "0 auto",
    backgroundColor: theme.other["surface-bg-color"],
    borderRadius: 4,
    padding: 48,
    boxSizing: "border-box",
    display: "flex",
    flexDirection: "column",
    gap: 48,
  },
  logo: {
    display: "block",
    margin: "0 auto ",
    width: 158,
    height: "auto",
  },
  title: {
    fontFamily: "SF Pro Display, sans-serif",
    color: "#FFF",
    margin: 0,
    textAlign: "center",
  },
  form: {
    display: "flex",
    flexDirection: "column",
    gap: 16,
    width: "100%",
  },
  fieldGroup: {
    display: "flex",
    flexDirection: "column",
    gap: 4,
    width: "100%",
  },
  label: {
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 400,
    fontSize: 16,
    lineHeight: 1.19,
    color: theme.other["authform-label-color"] || "#FFF",
    marginBottom: 0,
  },
  checkboxRow: {
    display: "flex",
    alignItems: "center",
    gap: 8,
    marginBottom: 0,
  },
  checkboxLabel: {
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 400,
    fontSize: 16,
    lineHeight: 1.19,
    color: theme.other["authform-label-color"] || "#FFF",
    marginLeft: 8,
  },
  actions: {
    display: "flex",
    flexDirection: "column",
    gap: 16,
    marginTop: 8,
    alignItems: "center",
  },
  toggleText: {
    color: "#FFF",
    textAlign: "center",
    cursor: "pointer",
    marginTop: 8,
    userSelect: "none",
    transition: "color 0.2s",
  },
  turnstile: {
    textAlign: "center",
  },
}));

export const AuthenticationForm: FC<PaperProps> = (props) => {
  const isLoginRoute = window.location.pathname === "/login";
  const [isLogin, toggle] = useToggle([isLoginRoute, !isLoginRoute]);
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm({
    initialValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
      captcha_token: "",
      marketing_consent: true,
    },
    validate: {
      fullName: (val) => {
        const nameRegex =
          /^[A-Za-z]+(?:[-'\s][A-Za-z]+)*\s[A-Za-z]+(?:[-'\s][A-Za-z]+)*$/;
        if (!val) return "Please enter your full name";
        if (val.length < 2 || val.length > 50)
          return "Name must be between 2 and 50 characters";
        return nameRegex.test(val) ? null : "Please enter a valid full name";
      },
      email: (val) => {
        const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!val) return "Please enter your email";
        return emailRegex.test(val)
          ? null
          : "Please enter a valid email address";
      },
      password: (val) =>
        val.length <= 3
          ? "Password should include at least 4 characters"
          : null,
      confirmPassword: (val, values) =>
        val === values.password ? null : "Passwords must match",
    },
    validateInputOnChange: true,
  });
  const [transitionToggle, setTransitionToggle] = useState(true);
  const [turnstileBound, setTurnstileBound] = useState<any>(null);
  const theme = useMantineTheme();
  const { classes, cx } = useStyles();
  const dispatch: AppDispatch = useDispatch();
  const { openModal } = useModal();
  const { handleError } = useErrorHandler();
  const [showErrorNotification, setShowErrorNotification] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Function to load initial user data after successful authentication
  const loadInitialData = async (): Promise<boolean> => {
    try {
      // Fetch user data first
      const userDataResult = await dispatch(getUserData());

      if (!getUserData.fulfilled.match(userDataResult)) {
        if (userDataResult.payload) {
          const errorPayload = userDataResult.payload as {
            error: ErrorResponse;
            statusCode: number;
          };
          handleError(errorPayload.error, errorPayload.statusCode);
        }
        return false;
      }

      try {
        // Fetch fonts
        const fontsPromise = dispatch(fetchFonts())
          .then(async (result) => {
            if (!fetchFonts.fulfilled.match(result)) {
              if (result.payload) {
                const errorPayload = result.payload as {
                  error: ErrorResponse;
                  statusCode: number;
                };
                handleError(errorPayload.error, errorPayload.statusCode);
              }
              return null;
            }

            const fontResponse = result.payload as FontsResponse;
            if (fontResponse) {
              try {
                await loadAllFonts(fontResponse.system, fontResponse.user);
              } catch (err) {
                console.error("Error loading fonts:", err);
              }
            }
            return null;
          })
          .catch((error: any) => {
            handleError(error.error, error.statusCode);
            return null;
          });

        // Fetch presets
        const presetsPromise = dispatch(fetchPresets())
          .then((result) => {
            if (!fetchPresets.fulfilled.match(result) && result.payload) {
              const errorPayload = result.payload as {
                error: ErrorResponse;
                statusCode: number;
              };
              handleError(errorPayload.error, errorPayload.statusCode);
            }
            return result;
          })
          .catch((error: any) => {
            handleError(error.error, error.statusCode);
            return null;
          });

        // Wait for all data to load
        await Promise.all([fontsPromise, presetsPromise]);

        return true;
      } catch (error: any) {
        handleError(error.error, error.statusCode);
        return false;
      }
    } catch (error: any) {
      handleError(error.error, error.statusCode);
      return false;
    }
  };

  const handleAuth = async (e: any) => {
    e.preventDefault();
    form.validate();
    if (!isFormValid()) return;

    setIsLoading(true);
    setErrorMessage("");
    setShowErrorNotification(false);

    if (isLogin) {
      const loginData: ILoginPayload = {
        username: form.values.email,
        password: form.values.password,
      };

      try {
        const resultAction: any = await dispatch(userLogin(loginData));

        if (userLogin.fulfilled.match(resultAction)) {
          // Keep loading state active while fetching data
          const dataLoaded = await loadInitialData();
          setIsLoading(false);
        } else {
          const errorResponse = resultAction.payload.error as ErrorResponse;

          const message = parseErrorMessage(errorResponse, "Login failed.");

          setErrorMessage(message);
          setShowErrorNotification(true);

          setIsLoading(false);
        }
      } catch (errorResponse: any) {
        handleError(errorResponse.error, errorResponse.statusCode);
      }
    } else {
      if (form.values.confirmPassword !== form.values.password) {
        setIsLoading(false);

        return 0;
      }

      turnstileBound?.execute();
    }
  };

  const handleCaptchaVerify = async (token: string) => {
    form.setFieldValue("captcha_token", token);

    const registerData: IRegisterPayload = {
      email: form.values.email,
      password: form.values.password,
      full_name: form.values.fullName,
      marketing_consent: form.values.marketing_consent,
      captcha_token: token,
    };

    try {
      const resultAction: any = await dispatch(registerUser(registerData));

      if (registerUser.fulfilled.match(resultAction)) {
        // After successful registration, load all required data
        // Keep loading state active while fetching data
        const dataLoaded = await loadInitialData();

        // Only show welcome modal if data was loaded successfully
        if (dataLoaded) {
          const modalConfig: ModalConfig = {
            type: ModalType.WELCOME,
          };

          openModal(modalConfig);
        }

        setIsLoading(false);
      } else {
        const errorResponse = resultAction.payload.error as ErrorResponse;

        const message = parseErrorMessage(errorResponse, "Login failed.");

        setErrorMessage(message);
        setShowErrorNotification(true);

        setIsLoading(false);
      }
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
    }
  };

  const isFormValid = (): boolean => {
    if (isLogin) {
      return (
        /^\S+@\S+$/.test(form.values.email) && form.values.password.length > 3
      );
    } else {
      return form.isValid();
    }
  };

  return (
    <div
      style={{
        width: "100%",
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        background:
          theme.other["authform-bg-color"] ||
          theme.other["surface-main-bg-color"],
      }}
    >
      <Paper className={classes.root} shadow={"md"} {...props}>
        <Transition
          mounted={transitionToggle}
          transition="pop"
          duration={300}
          timingFunction="ease"
        >
          {(styles) => (
            <div style={styles}>
              <img className={classes.logo} src={LogoImage} alt="Logo" />
              <h1 className={classes.title}>
                {isLogin ? "Log in" : "Sign up"}
              </h1>
              {showErrorNotification && (
                <VFNotification
                  style={{ margin: "16px 0" }}
                  message={errorMessage}
                  variant="error"
                />
              )}
              <form
                className={classes.form}
                onSubmit={(e: any) => handleAuth(e)}
                autoComplete="off"
              >
                {!isLogin && (
                  <div className={classes.fieldGroup}>
                    <label className={classes.label}>Name *</label>
                    <VFTextbox
                      placeholder="Enter name"
                      value={form.values.fullName}
                      onChange={(v) => form.setFieldValue("fullName", v)}
                      valid={!form.errors.fullName}
                      errorMessage={
                        typeof form.errors.fullName === "string"
                          ? form.errors.fullName
                          : undefined
                      }
                      width="100%"
                      type="text"
                    />
                  </div>
                )}
                <div className={classes.fieldGroup}>
                  <label className={classes.label}>E-mail *</label>
                  <VFTextbox
                    placeholder="Enter e-mail"
                    value={form.values.email}
                    onChange={(v) => form.setFieldValue("email", v)}
                    valid={!form.errors.email}
                    errorMessage={
                      typeof form.errors.email === "string"
                        ? form.errors.email
                        : undefined
                    }
                    width="100%"
                    type="text"
                  />
                </div>
                <div
                  className={classes.fieldGroup}
                  style={{ marginBottom: 15 }}
                >
                  <label className={classes.label}>Password *</label>
                  <VFTextbox
                    placeholder="Enter password"
                    value={form.values.password}
                    onChange={(v) => form.setFieldValue("password", v)}
                    valid={!form.errors.password}
                    errorMessage={
                      typeof form.errors.password === "string"
                        ? form.errors.password
                        : undefined
                    }
                    width="100%"
                    type="password"
                  />
                </div>
                {!isLogin && (
                  <div className={classes.fieldGroup}>
                    <label className={classes.label}>Confirm password *</label>
                    <VFTextbox
                      placeholder="Enter password again"
                      value={form.values.confirmPassword}
                      onChange={(v) => form.setFieldValue("confirmPassword", v)}
                      valid={!form.errors.confirmPassword}
                      errorMessage={
                        typeof form.errors.confirmPassword === "string"
                          ? form.errors.confirmPassword
                          : undefined
                      }
                      width="100%"
                      type="password"
                    />
                  </div>
                )}
                {!isLogin && (
                  <div className={classes.checkboxRow}>
                    <VFCheckbox
                      checked={form.values.marketing_consent}
                      onChange={(checked) =>
                        form.setFieldValue("marketing_consent", checked)
                      }
                      size="md"
                      radius={4}
                      styles={{
                        input: {
                          background:
                            theme.other["checkbox-bg-color"] || "#FAFAFA",
                          border: `1px solid ${
                            theme.other["checkbox-border-color"] || "#3A3A3A"
                          }`,
                          borderRadius: 4,
                        },
                        label: {
                          fontFamily: "SF Pro Display, sans-serif",
                          fontWeight: 400,
                          fontSize: 16,
                          color: theme.other["authform-label-color"] || "#FFF",
                        },
                      }}
                      label={
                        "I agree that my email will be used for marketing purposes"
                      }
                    />
                  </div>
                )}
                {!isLogin && (
                  <div className={classes.turnstile}>
                    <Turnstile
                      sitekey={envConfig.captchaSiteKey}
                      execution="execute"
                      onLoad={(widgetId, bound) => setTurnstileBound(bound)}
                      onVerify={handleCaptchaVerify}
                    />
                  </div>
                )}
                <div className={classes.actions}>
                  <VFButton
                    variant="primary"
                    label={isLogin ? "Log in" : "Sign up"}
                    loading={isLoading}
                    type="submit"
                  />
                  <span
                    className={cx(classes.toggleText, "p-regular")}
                    onClick={() => {
                      setErrorMessage("");
                      setShowErrorNotification(false);
                      form.reset();
                      form.clearErrors();
                      setTransitionToggle(false);
                      setTimeout(() => {
                        toggle();
                      }, 180);
                      setTimeout(() => {
                        setTransitionToggle(true);
                      }, 250);
                    }}
                  >
                    {isLogin ? (
                      <>
                        Don't have an account?{" "}
                        <span className="p-heavy">Sign up here.</span>
                      </>
                    ) : (
                      <>
                        Already have an account?{" "}
                        <span className="p-heavy">Log in here.</span>
                      </>
                    )}
                  </span>
                </div>
              </form>
            </div>
          )}
        </Transition>
      </Paper>
    </div>
  );
};
