import {
  createContext,
  ReactNode,
  FC,
  useEffect,
  useState,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { getUserData } from "../features/auth/authActions";
import { IUserData, ErrorResponse } from "../types";
import { AppDispatch, RootState } from "../store";
import { fetchFonts, fetchPresets } from "../features/presets/presetsSlice";
import { FontsResponse, PresetsResponse } from "../features/presets/types";
import { loadAllFonts } from "../features/presets/fontUtils";
import { useErrorHandler } from "../hooks/useErrorHandler";

interface AuthContextProps {
  isAuthenticated: boolean;
  loading: boolean;
  userData: IUserData | null;
  isAllDataLoaded: boolean;
  authenticationFailed: boolean;
}

const AuthContext = createContext<AuthContextProps>({
  isAuthenticated: false,
  loading: true,
  userData: null,
  isAllDataLoaded: false,
  authenticationFailed: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { isAuthenticated, userData, authenticationFailed } = useSelector(
    (state: RootState) => state.auth
  );
  const { fonts, presets } = useSelector((state: RootState) => state.presets);
  const { handleError } = useErrorHandler();

  const [loading, setLoading] = useState(true);
  const [isAllDataLoaded, setIsAllDataLoaded] = useState(false);
  const [isUserDataLoaded, setIsUserDataLoaded] = useState(false);
  const [isFontsLoaded, setIsFontsLoaded] = useState(false);
  const [isPresetsLoaded, setIsPresetsLoaded] = useState(false);

  const initialFetchRef = useRef(false);

  // Effect to set isAllDataLoaded when all required data is loaded
  useEffect(() => {
    if (isUserDataLoaded && isFontsLoaded && isPresetsLoaded) {
      setIsAllDataLoaded(true);
      setLoading(false);
    }
  }, [isUserDataLoaded, isFontsLoaded, isPresetsLoaded]);

  // Effect to react to authentication failures from Redux
  useEffect(() => {
    if (authenticationFailed) {
      // If authentication has failed, mark all data as loaded to allow redirection
      setIsUserDataLoaded(true);
      setIsFontsLoaded(true);
      setIsPresetsLoaded(true);
      setLoading(false);
    }
  }, [authenticationFailed]);

  // Single source of truth for data loading
  useEffect(() => {
    const loadData = async () => {
      if (initialFetchRef.current) return;
      initialFetchRef.current = true;

      try {
        // Step 1: Get user data first
        const userDataResult = await dispatch(getUserData());

        if (!getUserData.fulfilled.match(userDataResult)) {
          if (userDataResult.payload) {
            const errorPayload = userDataResult.payload as {
              error: ErrorResponse;
              statusCode: number;
            };
          }

          // If getUserData fails, mark all data as loaded to allow redirection
          setIsUserDataLoaded(true);
          setIsFontsLoaded(true);
          setIsPresetsLoaded(true);
          setLoading(false);
          return;
        }

        setIsUserDataLoaded(true);

        // Fetch fonts
        if (!fonts) {
          try {
            const fontsResult = await dispatch(fetchFonts());

            if (!fetchFonts.fulfilled.match(fontsResult)) {
              if (fontsResult.payload) {
                const errorPayload = fontsResult.payload as {
                  error: ErrorResponse;
                  statusCode: number;
                };
                handleError(errorPayload.error, errorPayload.statusCode);
              }
              setIsFontsLoaded(true);
              return;
            }

            const fontResponse = fontsResult.payload as FontsResponse;
            if (fontResponse) {
              await loadAllFonts(fontResponse.system, fontResponse.user).catch(
                (err: Error) => {
                  console.error("Error loading fonts:", err);
                }
              );
            }
            setIsFontsLoaded(true);
          } catch (error: any) {
            handleError(error.error, error.statusCode);
            setIsFontsLoaded(true);
          }
        } else {
          setIsFontsLoaded(true);
        }

        // Fetch presets
        if (!presets) {
          try {
            const presetsResult = await dispatch(fetchPresets());

            if (!fetchPresets.fulfilled.match(presetsResult)) {
              if (presetsResult.payload) {
                const errorPayload = presetsResult.payload as {
                  error: ErrorResponse;
                  statusCode: number;
                };
                handleError(errorPayload.error, errorPayload.statusCode);
              }
              setIsPresetsLoaded(true);
              return;
            }

            console.log("Presets loaded successfully");
            setIsPresetsLoaded(true);
          } catch (error: any) {
            handleError(error.error, error.statusCode);
            setIsPresetsLoaded(true);
          }
        } else {
          setIsPresetsLoaded(true);
        }
      } catch (error: any) {
        handleError(error.error, error.statusCode);
        // Mark everything as loaded to prevent indefinite loading state
        setIsUserDataLoaded(true);
        setIsFontsLoaded(true);
        setIsPresetsLoaded(true);
        setLoading(false);
      }
    };

    loadData();
  }, [dispatch, fonts, presets, handleError]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        loading,
        userData,
        isAllDataLoaded,
        authenticationFailed,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
