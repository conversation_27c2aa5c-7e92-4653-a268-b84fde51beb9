import React, { useState, useEffect, useRef } from "react";
import { createStyles, Image, useMantineTheme } from "@mantine/core";
import { Dropzone } from "@mantine/dropzone";
import { VFButton } from "../ui/Button";
import { VFTextbox } from "../ui/Textbox";
import VFIconComponent from "../icon/vf-icon";
import { useDispatch } from "react-redux";
import {
  addNewVideo,
  getYoutubeVideoDetails,
} from "../../features/videos/videosSlice";
import { showNotification } from "@mantine/notifications";
import { useErrorHandler } from "../../hooks/useErrorHandler";
import { FileUpload } from "../ui/FileUpload";
import { MAX_VIDEO_SIZE } from "../../constants";
import { GetYoutubeVideoDetailsResponse } from "../../features/videos/types";
import { ProgressBar } from "../ui/Progress";

const validateYouTubeUrl = (
  url: string
): { id: string; thumbnailUrl: string } | null => {
  const urlRegex = /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.?be)\/.+$/;
  if (!urlRegex.test(url)) {
    return null;
  }

  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
  const match = url.match(regExp);

  if (match && match[2].length === 11) {
    return {
      id: match[2],
      thumbnailUrl: `https://img.youtube.com/vi/${match[2]}/0.jpg`,
    };
  }

  return null;
};

const generateVideoThumbnail = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.preload = "metadata";
    video.playsInline = true;
    video.muted = true;

    video.onloadeddata = () => {
      const canvas = document.createElement("canvas");
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        URL.revokeObjectURL(video.src);
        reject(new Error("Failed to get canvas context"));
        return;
      }

      video.currentTime = 0;
    };

    video.onseeked = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const thumbnailUrl = URL.createObjectURL(blob);
            URL.revokeObjectURL(video.src);
            resolve(thumbnailUrl);
          } else {
            URL.revokeObjectURL(video.src);
            reject(new Error("Failed to generate thumbnail"));
          }
        },
        "image/jpeg",
        0.7
      );
    };

    video.onerror = () => {
      URL.revokeObjectURL(video.src);
      reject(new Error("Error loading video"));
    };

    const videoUrl = URL.createObjectURL(file);
    video.src = videoUrl;
  });
};

const useStyles = createStyles((theme) => ({
  wrapper: {
    position: "relative",
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: "32px",

    "@media (max-width: 744px)": {
      flexDirection: "column",
      alignItems: "center",
      gap: "32px",
    },
  },
  form: {
    width: "100%",
    position: "relative",
    maxWidth: 590,
    "@media (max-width: 440px)": {
      maxWidth: 311,
    },
  },
  inputGroup: {
    display: "flex",
    alignItems: "flex-start",
    gap: 8,
    width: "100%",
  },
  inputWrapper: {
    flex: "1 1 auto",
    minWidth: 0,

    ".file-name-holder": {
      display: "flex",
      gap: 10,
      minWidth: 200,
      width: "100%",

      span: {
        padding: "8px 0",
        color: theme.other["textbox-text-color"],
        whiteSpace: "nowrap",
        overflow: "hidden",
        textOverflow: "ellipsis",
      },

      "> div": {
        alignItems: "center",
      },
    },

    "@media (max-width: 440px)": {
      width: "100%",
    },
  },
  uploadView: {
    display: "flex",
    flexDirection: "column",
    gap: 16,

    "& > button": {
      "@media (max-width: 440px)": {
        display: "none",
      },
    },

    "& .mobileButtons": {
      display: "none",
      "@media (max-width: 440px)": {
        display: "flex",
        gap: 8,
        "& button:first-of-type": {
          width: "auto",
        },
        "& button:last-of-type": {
          flex: 1,
        },
      },
    },

    "& .desktopButtons": {
      "@media (max-width: 440px)": {
        display: "none",
      },
    },
  },
  preview: {
    width: 200,
    height: 112,
    position: "relative",
    backgroundColor: theme.other["video-preview-bg-color"],
    borderRadius: 4,
    overflow: "hidden",

    "& svg": {
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
    },

    "@media (max-width: 744px)": {
      width: 300,
      height: 168,
    },
    "@media (max-width: 440px)": {
      width: 310,
      height: 176,
    },
  },
  previewImage: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
  },
  dropzone: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    border: "none",
    pointerEvents: "none",
    opacity: 0,
  },
  activeDropzone: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    animation: "fadeIn 0.2s ease",
    zIndex: 10,
  },
  dropzoneInner: {
    width: "100%",
    height: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "column",
    gap: 20,
    backgroundColor: `${theme.other["uploader-bg-color"]} !important`,
    border: `1px dashed ${theme.other["uploader-border-color"]} !important`,
    borderRadius: 4,
    padding: "22px",

    span: {
      color: theme.other["uploader-text-color"],

      "&[data-rejected]": {
        color: theme.other["uploader-error-color"],
      },
    },

    "&[data-reject]": {
      borderColor: `${theme.other["uploader-error-color"]} !important`,
    },
  },
  "@keyframes fadeIn": {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  boostFade: {
    animation: "boostFade 1.2s linear infinite",
    "@keyframes boostFade": {
      "0%": { opacity: 0.2 },
      "50%": { opacity: 1 },
      "100%": { opacity: 0.2 },
    },
    display: "inline-flex",
    verticalAlign: "middle",
    margin: "0 4px",
  },
  animationText: {
    margin: "0 8px",
    color: theme.other["surface-text-color"],
  },
  loadingContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    opacity: 0.8,
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.other["uploader-bg-color"],
    border: `1px dashed ${theme.other["uploader-border-color"]}`,
    borderRadius: 4,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
    padding: "0px 16px",
    zIndex: 20,
  },
  loadingRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 8,
  },
  loadingText: {
    color: theme.other["uploader-text-color"],
    fontWeight: 500,
    fontSize: 16,
  },
  progressContainer: {
    width: "80%",
    maxWidth: 470,
  },
}));

// BoostFadeAnimation: Animated boost icon for loading state
function BoostFadeAnimation({
  className,
  style,
  delay = 0,
}: {
  className?: string;
  style?: React.CSSProperties;
  delay?: number;
}) {
  const { classes, cx } = useStyles();
  return (
    <span
      className={cx(classes.boostFade, className)}
      style={{
        ...style,
        animationDelay: `${delay}s`,
      }}
    >
      <VFIconComponent type="boost" size={18} />
    </span>
  );
}

export const VideoUploadForm = () => {
  const { classes, cx } = useStyles();
  const formRef = useRef<HTMLDivElement>(null);
  const dropzoneRef = useRef<HTMLDivElement>(null);
  const [url, setUrl] = useState("");
  const [urlValid, setUrlValid] = useState(false);
  const [youtubeData, setYoutubeData] = useState<{
    id: string;
    thumbnailUrl: string;
    title: string;
    duration: number;
  } | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [showDropzone, setShowDropzone] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [errorTimeout, setErrorTimeout] = useState<NodeJS.Timeout | null>(null);
  const openRef = useRef<() => void>(null);
  const theme = useMantineTheme();
  const dispatch = useDispatch();
  const { handleError } = useErrorHandler();
  const [videoDuration, setVideoDuration] = useState<number | null>(null);

  // Add a new ref to track loading timeouts
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Effect to handle progress bar animation
  useEffect(() => {
    if (isProcessing) {
      // Ensure progress is reset immediately
      setProcessingProgress(0);

      // Start progress animation after a small delay to ensure reset is applied
      const startProgressInterval = setTimeout(() => {
        const intervalId = setInterval(() => {
          setProcessingProgress((prev) => {
            if (prev >= 100) {
              clearInterval(intervalId);
              return 100;
            }
            // Adjust increment to complete in ~7 seconds (70 intervals at 100ms each)
            return prev + 1.5;
          });
        }, 100);

        // Store the interval ID for cleanup
        progressIntervalRef.current = intervalId;
      }, 50);

      // Ensure loading shows for at most 7 seconds as a fallback
      loadingTimeoutRef.current = setTimeout(() => {
        if (progressIntervalRef.current) {
          clearInterval(progressIntervalRef.current);
          progressIntervalRef.current = null;
        }
        setProcessingProgress(100);
        setTimeout(() => {
          setIsProcessing(false);
        }, 300); // Small delay to see 100% before hiding
      }, 7000);
    } else {
      // Reset progress when processing ends
      setProcessingProgress(0);
    }

    return () => {
      // Clear timeouts and intervals on component unmount or effect rerun
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
    };
  }, [isProcessing]);

  useEffect(() => {
    return () => {
      if (errorTimeout) {
        clearTimeout(errorTimeout);
      }
      if (preview) {
        URL.revokeObjectURL(preview);
      }
    };
  }, [errorTimeout, preview]);

  useEffect(() => {
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
      if (!showDropzone) {
        setShowDropzone(true);
      }
    };

    const wrapper = formRef.current;
    if (wrapper) {
      wrapper.addEventListener("dragover", handleDragOver);
    }

    return () => {
      if (wrapper) {
        wrapper.removeEventListener("dragover", handleDragOver);
      }
    };
  }, [showDropzone]);

  // Helper function to start processing with reset progress
  const startProcessing = () => {
    // Force reset progress to 0
    setProcessingProgress(0);
    // Small delay before starting to ensure UI updates
    setTimeout(() => {
      setIsProcessing(true);
    }, 10);
  };

  // Helper function to complete the loading process immediately
  const completeProcessing = () => {
    // Clear any existing timers
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }

    // Set progress to 100% and hide the loader
    setProcessingProgress(100);
    setTimeout(() => {
      setIsProcessing(false);
    }, 300); // Small delay to show 100% complete before hiding
  };

  const handleUrlChange = async (value: string) => {
    setUrl(value);
    const validYoutubeData = validateYouTubeUrl(value);
    setUrlValid(!!validYoutubeData);

    if (!!validYoutubeData) {
      // Reset progress and start processing
      startProcessing();

      try {
        const youtubeVideoDetails: GetYoutubeVideoDetailsResponse =
          await dispatch(
            getYoutubeVideoDetails({ youtube_url: value }) as any
          ).unwrap();

        setYoutubeData({
          id: validYoutubeData.id,
          thumbnailUrl: validYoutubeData.thumbnailUrl,
          title: youtubeVideoDetails.title,
          duration: youtubeVideoDetails.duration,
        });

        setVideoDuration(youtubeVideoDetails.duration);

        // Data is loaded - complete the processing immediately
        completeProcessing();
      } catch (error) {
        console.error("Failed to get YouTube video details:", error);
        // Complete processing on error too
        completeProcessing();
      }
    } else {
      setYoutubeData(null);
      setVideoDuration(null);
    }

    setFile(null);
    setPreview(null);
  };

  const handleDrop = async (files: File[]) => {
    if (files.length === 0 || isUploading) return;

    const file = files[0];

    // Reset progress and start processing
    startProcessing();
    setIsUploading(true);

    try {
      const videoUrl = URL.createObjectURL(file);
      const video = document.createElement("video");
      video.preload = "metadata";
      video.src = videoUrl;
      video.muted = true;
      video.playsInline = true;
      video.onloadedmetadata = async () => {
        const duration = video.duration;
        setVideoDuration(duration);
        URL.revokeObjectURL(videoUrl);
        const thumbnailUrl = await generateVideoThumbnail(file);
        setFile(file);
        setPreview(thumbnailUrl);
        setUrl("");
        setUrlValid(false);
        setYoutubeData(null);
        setIsUploading(false);
        setShowDropzone(false);

        // Data is loaded - complete the processing immediately
        completeProcessing();
      };
      video.onerror = () => {
        URL.revokeObjectURL(videoUrl);
        setIsUploading(false);
        setShowDropzone(false);
        setVideoDuration(null);
        setIsProcessing(false); // Stop processing on error
        console.error("Failed to load video for duration");
      };
    } catch (error) {
      console.error("Upload failed:", error);
      setIsUploading(false);
      setShowDropzone(false);
      setVideoDuration(null);
      setIsProcessing(false); // Stop processing on error
    }
  };

  const handleRemove = () => {
    if (preview) {
      URL.revokeObjectURL(preview);
    }
    setFile(null);
    setPreview(null);
    setVideoDuration(null);
    setYoutubeData(null);
  };

  const handleSubmit = async () => {
    if ((!url || !urlValid) && !file) {
      return;
    }

    setIsUploading(true);

    const formData = new FormData();

    if (file) {
      formData.append("file", file);
      formData.append("source_type", "file");
    } else if (url) {
      formData.append("url", url);
      formData.append("source_type", "youtube");
    }

    try {
      // @ts-ignore - Ignoring type error as we know the API accepts FormData
      await dispatch(addNewVideo(formData)).unwrap();

      // Reset form
      if (preview) {
        URL.revokeObjectURL(preview);
      }
      setFile(null);
      setPreview(null);
      setUrl("");
      setUrlValid(false);
      setYoutubeData(null);

      // showNotification({
      //   radius: "md",
      //   icon: <VFIconComponent type="success" backgroundColor="#FFFFFF" />,
      //   title: "Success",
      //   message: "Video successfully added!",
      // });
    } catch (response: any) {
      handleError(response.error, response.statusCode);
    } finally {
      setIsUploading(false);
    }
  };

  const LoadingContent = () => {
    return (
      <div className={classes.loadingContainer} aria-busy="true" tabIndex={-1}>
        <BoostFadeAnimation delay={0} />
        <span className={cx(classes.animationText, "p-heavy")}>
          Boosting your podcast, please don't close or refresh this window!
        </span>
        <BoostFadeAnimation delay={0.6} />
      </div>
    );
  };

  const boostCount =
    (file || youtubeData) && videoDuration != null
      ? Math.max(1, Math.ceil(videoDuration / 60))
      : 1;

  return (
    <div className={classes.wrapper}>
      <div className={cx(classes.form)} ref={formRef}>
        <div className={classes.uploadView}>
          <div className={classes.inputGroup}>
            <div className={classes.inputWrapper}>
              {file || youtubeData ? (
                <div className="file-name-holder">
                  <span className="p-heavy">
                    {file?.name || youtubeData?.title}
                  </span>
                  <VFIconComponent
                    type="check"
                    color={theme.other["textbox-valid-color"]}
                    dimensions={{ width: 16, height: 16 }}
                  />
                </div>
              ) : (
                <VFTextbox
                  placeholder="Paste a YouTube link to get started"
                  value={url}
                  onChange={handleUrlChange}
                  leftIconType="link"
                  valid={!url || urlValid}
                  errorMessage={
                    url && !urlValid
                      ? "Please enter a valid YouTube URL"
                      : undefined
                  }
                />
              )}
            </div>
            <div className="desktopButtons">
              {file || youtubeData ? (
                <VFButton
                  variant="secondary"
                  buttonDisplay="withIcon"
                  iconName="trash-can"
                  onClick={handleRemove}
                >
                  Remove
                </VFButton>
              ) : (
                <VFButton
                  variant="secondary"
                  buttonDisplay="withIcon"
                  iconName="upload-new"
                  onClick={() => openRef?.current?.()}
                >
                  Upload
                </VFButton>
              )}
            </div>
          </div>

          <div className="desktopButtons">
            {(file || youtubeData) && isUploading ? (
              <LoadingContent />
            ) : (
              <VFButton
                variant="primary"
                fullWidth
                disabled={(!url || !urlValid) && !file}
                onClick={handleSubmit}
              >
                {!file && !youtubeData
                  ? "Get clips"
                  : `Use ${boostCount} boost to transform your podcast into viral factory!`}
              </VFButton>
            )}
          </div>

          <div className="mobileButtons">
            {file || youtubeData ? (
              <VFButton
                variant="secondary"
                buttonDisplay="withIcon"
                iconName="trash-can"
                onClick={handleRemove}
              >
                Remove
              </VFButton>
            ) : (
              <VFButton
                variant="secondary"
                buttonDisplay="withIcon"
                iconName="upload-new"
                onClick={() => openRef?.current?.()}
              >
                Upload
              </VFButton>
            )}
            {(file || youtubeData) && isUploading ? (
              <LoadingContent />
            ) : (
              <VFButton
                variant="primary"
                disabled={(!url || !urlValid) && !file}
                onClick={handleSubmit}
              >
                {!file
                  ? "Get clips"
                  : `Use ${boostCount} boost to transform your podcast into viral factory!`}
              </VFButton>
            )}
          </div>
        </div>

        {/* Processing Overlay */}
        {isProcessing && (
          <div className={classes.loadingOverlay}>
            <div className={classes.progressContainer}>
              <ProgressBar value={processingProgress} />
            </div>
            <div className={classes.loadingRow}>
              <VFIconComponent
                type="upload-new"
                color={theme.other["uploader-text-color"]}
                dimensions={{ width: 16, height: 16 }}
              />
              <span className={classes.loadingText}>Uploading...</span>
            </div>
          </div>
        )}

        {showDropzone && (
          <FileUpload
            variant="video"
            onDrop={handleDrop}
            onDragLeave={() => setShowDropzone(false)}
            onReject={() => setShowDropzone(false)}
            loading={isUploading}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 10,
            }}
          />
        )}
      </div>

      <div className={classes.preview}>
        <VFIconComponent
          type="image"
          color={theme.other["video-preview-content-color"]}
          dimensions={{ width: 48, height: 42 }}
        />
        {(youtubeData?.thumbnailUrl || preview) && (
          <Image
            src={youtubeData?.thumbnailUrl || preview}
            alt="Video thumbnail"
            className={classes.previewImage}
          />
        )}
      </div>

      <Dropzone
        ref={dropzoneRef}
        openRef={openRef}
        onDrop={handleDrop}
        maxSize={MAX_VIDEO_SIZE}
        accept={{ "video/mp4": [".mp4"] }}
        className={classes.dropzone}
      >
        {null}
      </Dropzone>
    </div>
  );
};

export default VideoUploadForm;
