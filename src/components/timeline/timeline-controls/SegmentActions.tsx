import React, { useCallback } from "react";
import { Group } from "@mantine/core";
import { VFButton } from "../../ui/Button";
import { playerRef } from "../../../remotion/components/timeline-refs";
import {
  getCurrentFps,
  getCurrentSegmentIndex,
  setCurrentSegmentIndex,
} from "../../../remotion/imperative-state";
import {
  useClipConfigAPI,
  useClipSegments,
} from "../../../features/clipConfig/ClipConfigApi";

interface SegmentActionsProps {}

export const SegmentActions: React.FC<SegmentActionsProps> = React.memo(() => {
  const { onSegmentsChange } = useClipConfigAPI();
  const segments = useClipSegments();

  const handleSnip = useCallback(() => {
    // Handle split clip functionality
    if (playerRef.current) {
      const currentFrame = playerRef.current.getCurrentFrame();
      // Use getCurrentFps as fallback when media_metadata is null
      const fps = getCurrentFps();
      const currentTimeInSeconds = currentFrame / fps;

      // Find which segment the current frame is in
      // Segments are stored with absolute time values, but player starts from 0
      // We need to find the first segment
      const firstSegmentStart = segments.length > 0 ? segments[0].start : 0;

      const segmentIndex = segments.findIndex((segment) => {
        // Calibrate segment times relative to the first segment's start time
        const relativeStart = segment.start - firstSegmentStart;
        const relativeEnd = segment.end - firstSegmentStart;

        return (
          currentTimeInSeconds >= relativeStart &&
          currentTimeInSeconds <= relativeEnd
        );
      });

      if (segmentIndex !== -1) {
        // Make a copy of the segments array
        const updatedSegments = [...segments];
        const currentSegment = updatedSegments[segmentIndex];

        // Calibrate segment times for comparison
        const relativeStart = currentSegment.start - firstSegmentStart;
        const relativeEnd = currentSegment.end - firstSegmentStart;

        // Only split if we're not at the start or end of the segment
        if (
          currentTimeInSeconds > relativeStart &&
          currentTimeInSeconds < relativeEnd
        ) {
          // Calculate absolute time for the new segment boundary
          const absoluteSplitTime = firstSegmentStart + currentTimeInSeconds;

          // Create two segments from the current one
          const firstHalf = {
            ...currentSegment,
            end: absoluteSplitTime,
          };

          const secondHalf = {
            ...currentSegment,
            start: absoluteSplitTime,
          };

          // Replace the current segment with the two new ones
          updatedSegments.splice(segmentIndex, 1, firstHalf, secondHalf);

          onSegmentsChange(updatedSegments);
        }
      }
    }
  }, [segments]);

  const handleDelete = useCallback(() => {
    if (segments.length === 1) return;

    // Get the current active segment from imperative state
    const activeSegmentIndex = getCurrentSegmentIndex();

    if (
      activeSegmentIndex === undefined ||
      activeSegmentIndex === null ||
      activeSegmentIndex < 0
    ) {
      alert("Please select a segment first before deleting");
      return;
    }

    // Make sure activeSegmentIndex is valid
    if (activeSegmentIndex >= segments.length) {
      return;
    }

    // Create a new segments array with the segment marked as deleted
    const newSegments = [...segments];
    const deletedSegment = newSegments[activeSegmentIndex];

    // Mark the segment as deleted instead of removing it
    newSegments[activeSegmentIndex] = {
      ...deletedSegment,
      deleted: true,
    };

    onSegmentsChange(newSegments);

    // Set the active segment to the previous one or to the first if no previous
    const newActiveIndex =
      activeSegmentIndex > 0
        ? activeSegmentIndex - 1
        : newSegments.length > 1
        ? 0
        : 0;

    // Update imperative state only
    setCurrentSegmentIndex(newActiveIndex);
  }, [segments]);

  return (
    <Group spacing={8} position="left" noWrap>
      <VFButton
        variant="secondary"
        buttonDisplay="withIcon"
        iconName="snip"
        onClick={handleSnip}
        style={{ padding: "8px 16px" }}
      >
        Snip
      </VFButton>
      <VFButton
        variant="secondary"
        buttonDisplay="withIcon"
        iconName="trash-can"
        color="pink"
        onClick={handleDelete}
        style={{
          padding: "8px 16px",
          opacity:
            getCurrentSegmentIndex() === null || getCurrentSegmentIndex() < 0
              ? 0.6
              : 1,
          cursor:
            getCurrentSegmentIndex() === null || getCurrentSegmentIndex() < 0
              ? "not-allowed"
              : "pointer",
        }}
      >
        Delete
      </VFButton>
    </Group>
  );
});
