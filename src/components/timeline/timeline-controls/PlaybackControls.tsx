import React, { useCallback, useEffect, useRef } from "react";
import { Group } from "@mantine/core";
import { VFButton } from "../../ui/Button";
import { playerRef } from "../../../remotion/components/timeline-refs";

interface PlaybackControlsProps {
  playerState: {
    isPlaying: boolean;
    frame: number;
    isMuted: boolean;
  };
  setPlayerState: React.Dispatch<
    React.SetStateAction<{
      isPlaying: boolean;
      frame: number;
      isMuted: boolean;
    }>
  >;
  videoMetadata: {
    duration_seconds: number;
    fps: number;
  } | null;
}

export const PlaybackControls: React.FC<PlaybackControlsProps> = React.memo(
  ({ playerState, videoMetadata }) => {
    // Use a ref to track when we need to force re-render for UI updates
    const forceUpdateRef = useRef({ value: 0 });
    const [, setForceUpdate] = React.useState(0);

    // Force a UI update only when play/pause or mute state changes
    const updateUI = useCallback(() => {
      forceUpdateRef.current.value++;
      setForceUpdate(forceUpdateRef.current.value);
    }, []);

    // Set up minimal event listeners just for UI updates
    useEffect(() => {
      if (playerRef.current) {
        const onStateChange = updateUI;

        playerRef.current.addEventListener("play", onStateChange);
        playerRef.current.addEventListener("pause", onStateChange);
        playerRef.current.addEventListener("mutechange", onStateChange);

        return () => {
          playerRef.current?.removeEventListener("play", onStateChange);
          playerRef.current?.removeEventListener("pause", onStateChange);
          playerRef.current?.removeEventListener("mutechange", onStateChange);
        };
      }
    }, [updateUI]);

    const handlePrevious = useCallback(() => {
      if (playerRef.current) {
        const currentFrame = playerRef.current.getCurrentFrame();
        playerRef.current.seekTo(Math.max(0, currentFrame - 30));
      }
    }, []);

    const handlePlayPause = useCallback(() => {
      if (playerRef.current) {
        if (playerRef.current.isPlaying()) {
          playerRef.current.pause();
        } else {
          playerRef.current.play();
        }
      }
    }, []);

    const handleNext = useCallback(() => {
      if (playerRef.current && videoMetadata) {
        const currentFrame = playerRef.current.getCurrentFrame();
        const totalFrames = Math.round(
          videoMetadata.duration_seconds * videoMetadata.fps
        );
        playerRef.current.seekTo(Math.min(totalFrames - 1, currentFrame + 30));
      }
    }, [videoMetadata]);

    const handleMuteToggle = useCallback(() => {
      if (playerRef.current) {
        if (playerRef.current.isMuted()) {
          playerRef.current.unmute();
        } else {
          playerRef.current.mute();
        }
      }
    }, []);

    // Get current player state directly from the player
    const isPlaying = playerRef.current?.isPlaying() || false;
    const isMuted = playerRef.current?.isMuted() || false;

    return (
      <Group
        spacing={8}
        position="center"
        noWrap
        style={{ width: "129px", justifyContent: "center" }}
      >
        <VFButton
          variant="secondary"
          buttonDisplay="onlyIcon"
          iconName="prev"
          onClick={handlePrevious}
          style={{ padding: "8px" }}
        />
        <VFButton
          variant="secondary"
          buttonDisplay="onlyIcon"
          iconName={isPlaying ? "pause" : "play"}
          onClick={handlePlayPause}
          style={{ padding: "8px" }}
        />
        <VFButton
          variant="secondary"
          buttonDisplay="onlyIcon"
          iconName="next"
          onClick={handleNext}
          style={{ padding: "8px" }}
        />
        <VFButton
          variant="secondary"
          buttonDisplay="onlyIcon"
          iconName={isMuted ? "volume-off" : "volume-on"}
          onClick={handleMuteToggle}
        />
      </Group>
    );
  }
);
