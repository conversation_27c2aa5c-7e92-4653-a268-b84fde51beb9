import React from "react";
import { Box } from "@mantine/core";
import { SegmentActions } from "./SegmentActions";
import { PlaybackControls } from "./PlaybackControls";
import { TimelineZoomControls } from "../../../remotion/components/TimelineZoomControls";

interface TimelineControlsProps {
  playerState: {
    isPlaying: boolean;
    frame: number;
    isMuted: boolean;
  };
  setPlayerState: React.Dispatch<
    React.SetStateAction<{
      isPlaying: boolean;
      frame: number;
      isMuted: boolean;
    }>
  >;
  videoMetadata: {
    duration_seconds: number;
    fps: number;
  } | null;
}

export const TimelineControls: React.FC<TimelineControlsProps> = React.memo(
  ({ playerState, setPlayerState, videoMetadata }) => {
    return (
      <Box
        sx={(theme) => ({
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          padding: "8px 24px",
          marginBottom: 16,
          width: "100%",
          backgroundColor: theme.other["surface-bg-color"],
        })}
      >
        {/* Left section with Snip and Delete buttons */}
        <SegmentActions />

        {/* Center section with playback controls */}
        <PlaybackControls
          playerState={playerState}
          setPlayerState={setPlayerState}
          videoMetadata={videoMetadata}
        />

        {/* Right section with zoom controls */}
        <TimelineZoomControls />
      </Box>
    );
  }
);
