import React, { useEffect, useRef } from "react";
import { TimelineScrollable } from "../../remotion/components/TimelineScrollable";
import { TimelineTimeIndicators } from "../../remotion/components/TimelineTimeIndicators";
import { TimelineSegments } from "../../remotion/components/TimelineSegments";
import { TimelineDragHandler } from "../../remotion/components/TimelineDragHandler";
import { TimelineSlider } from "../../remotion/TimelineSlider";
import { PlayerFrameSyncer } from "../../remotion/components/PlayerFrameSyncer";

interface TimelineProps {
  sourceVideoUrl: string;
}

export const Timeline: React.FC<TimelineProps> = ({ sourceVideoUrl }) => {
  // Use the original duration from props if provided, otherwise use the global one
  const containerRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={containerRef}
      style={{
        position: "relative",
        // overflowX: "auto",
        minHeight: "130px",
        borderRadius: "4px",
        margin: "0 20px",
      }}
    >
      <div
        style={{
          width: "100%",
          height: "100%",
          position: "relative",
          display: "block",
          background: "transparent",
          zIndex: 1,
        }}
      >
        <TimelineScrollable>
          <TimelineTimeIndicators />
          <TimelineSegments sourceVideoUrl={sourceVideoUrl} />
          <TimelineDragHandler />
          <TimelineSlider />
          <PlayerFrameSyncer />
        </TimelineScrollable>
      </div>
    </div>
  );
};

export default Timeline;
