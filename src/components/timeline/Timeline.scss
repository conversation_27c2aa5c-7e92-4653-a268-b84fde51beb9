.timeline {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;

  .video-clip {
    height: 40px;
    background-color: green;
    margin-right: 5px; // added spacing between clips
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;

    &.dragging {
      opacity: 0.5;
    }
  }

  .timeline-seconds {
    position: absolute;
    bottom: -15px;
    width: 100%;
    display: flex;

    span {
      font-size: 10px;
      margin-right: 5px;
    }
  }
}
