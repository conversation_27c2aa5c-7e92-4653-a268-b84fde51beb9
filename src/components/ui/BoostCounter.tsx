import React, { useMemo } from "react";
import { createStyles } from "@mantine/core";
import VFIconComponent from "../icon/vf-icon";
import { useSelector } from "react-redux";
import { RootState } from "../../store";
import { formatDate } from "../../utils";

interface BoostCounterProps {}

const useStyles = createStyles((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    alignSelf: "stretch",
    gap: "8px",
    padding: "32px 16px",
    width: "100%",
    boxSizing: "border-box",
    border: `1px solid ${theme.other["surface-border-color"]}`,
    borderRadius: "4px",
  },
  frameRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: "4px",
    height: "26px", // Match design height
    width: "100%",
  },
  countText: {
    color: theme.other["surface-text-color"],
    margin: 0,
  },
  infoText: {
    color: theme.other["surface-text-color"],
    textAlign: "center",
    margin: 0,
  },
}));

export const BoostCounter: React.FC<BoostCounterProps> = ({}) => {
  const { classes } = useStyles();
  const { userData } = useSelector((state: RootState) => state.auth);
  const { subscriptionData } = useSelector((state: RootState) => state.auth);

  const boostCount = useMemo(
    () => (userData && userData.boost >= 0 ? userData?.boost : 0),
    [userData]
  );

  const subscriptionLevel = useMemo(() => {
    return userData?.subscription_level;
  }, [userData]);

  if (!subscriptionData || !userData) {
    return null;
  }
  const formattedDate = formatDate(new Date(subscriptionData.plan.renews_at));

  const boostCountMessage =
    subscriptionLevel === 0
      ? `You have ${boostCount} boost left. Upgrade to unlock your full experience.`
      : `You have ${boostCount} boost left. Next boost arrive on ${formattedDate}.`;

  return (
    <div className={classes.container}>
      <div className={classes.frameRow}>
        <VFIconComponent type="boost" size={24} />
        <h3 className={classes.countText}>{boostCount}</h3>
      </div>
      <div className={classes.frameRow}>
        <p className={`small-p-regular ${classes.infoText}`}>
          {boostCountMessage}
        </p>
      </div>
    </div>
  );
};

export default BoostCounter;
