import React, { useEffect, useRef, useState } from "react";
import { createStyles } from "@mantine/core";
import { VFButton } from "./Button";
import { IconType } from "../icons/types";

export interface ContextMenuItemProps {
  label: string;
  iconName?: IconType;
  onClick?: () => void;
  disabled?: boolean;
  variant?: "primary" | "secondary" | "destructive";
}

interface VFContextMenuProps {
  items: ContextMenuItemProps[];
  isOpen: boolean;
  position?: "left" | "center" | "right";
  onClose?: () => void;
}

const useStyles = createStyles((theme) => ({
  contextMenuContainer: {
    position: "absolute",
    backgroundColor: theme.other["btn-floating-contextmenu-bg-color"],
    border: `1px solid ${theme.other["btn-floating-contextmenu-border-color"]}`,
    borderRadius: "4px",
    zIndex: 9999,
    opacity: 0,
    transform: "scale(0.98)",
    transition: "opacity 0.2s ease, transform 0.2s ease",
    visibility: "hidden",
    overflow: "hidden",
    padding: "4px",
    display: "flex",
    flexDirection: "row",
    gap: "4px",

    "&[data-opened='true']": {
      opacity: 1,
      transform: "scale(1)",
      visibility: "visible",
    },

    "&[data-position='left']": {
      left: 0,
    },

    "&[data-position='center']": {
      left: "50%",
      transform: "translateX(-50%) scale(0.98)",
      "&[data-opened='true']": {
        transform: "translateX(-50%) scale(1)",
      },
    },

    "&[data-position='right']": {
      right: 0,
    },
  },
}));

export const VFContextMenu: React.FC<VFContextMenuProps> = ({
  items,
  isOpen,
  position = "right",
  onClose,
}) => {
  const { classes } = useStyles();
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPlacement, setMenuPlacement] = useState<"top" | "bottom">(
    "bottom"
  );

  useEffect(() => {
    if (isOpen && menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      if (spaceBelow < 10 && spaceAbove > rect.height) {
        setMenuPlacement("top");
      } else {
        setMenuPlacement("bottom");
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const menuStyle = {
    ...(menuPlacement === "top" ? { bottom: "100%" } : { top: "100%" }),
    marginTop: menuPlacement === "bottom" ? "5px" : undefined,
    marginBottom: menuPlacement === "top" ? "5px" : undefined,
  };

  return (
    <div
      className={classes.contextMenuContainer}
      ref={menuRef}
      data-opened={isOpen}
      data-position={position}
      style={menuStyle}
    >
      {items.map((item, index) => (
        <VFButton
          key={`${item.label}-${index}`}
          size="small"
          buttonDisplay={item.iconName ? "withIcon" : "onlyText"}
          iconName={item.iconName}
          variant={item.variant || "primary"}
          label={item.label}
          disabled={item.disabled}
          onClick={item.onClick}
        />
      ))}
    </div>
  );
};

// Usage example:
export const ContextMenuExample: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const items: ContextMenuItemProps[] = [
    {
      label: "Edit text",
      iconName: "edit",
      onClick: () => {},
      variant: "primary",
    },
    {
      label: "Merge",
      iconName: "merge",
      onClick: () => {},
      variant: "primary",
    },
    {
      label: "Delete",
      iconName: "trash-can",
      onClick: () => setIsOpen(false),
      variant: "primary",
    },
  ];

  return (
    <div style={{ position: "relative", display: "inline-block" }}>
      <button onClick={() => setIsOpen(!isOpen)}>Toggle Context Menu</button>
      <VFContextMenu
        items={items}
        isOpen={isOpen}
        position="left"
        onClose={() => setIsOpen(false)}
      />
    </div>
  );
};
