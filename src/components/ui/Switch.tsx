import { Switch, createStyles } from "@mantine/core";
import React from "react";

interface VFSwitchProps {
  value?: boolean;
  onChange?: (value: boolean) => void;
  disabled?: boolean;
}

const useStyles = createStyles((theme) => ({
  root: {
    width: "52px",
    height: "22px",
  },

  track: {
    width: "52px",
    height: "22px",
    backgroundColor: `${theme.other["switch-bg-color-off"]}`,
    border: "none",
    cursor: "pointer",
    transition: "background-color 150ms ease",

    // Focus state
    "input:focus + &": {
      outline: `1px solid ${theme.other["switch-focus-border-color"]}`,
    },

    // Hover + shecked + not disabled state
    "input:checked:hover:not(:disabled) + &": {
      backgroundColor: `${theme.other["switch-hover-color"]} !important`,
    },

    // Checked (ON) state
    "input:checked + &": {
      backgroundColor: `${theme.other["switch-bg-color-on"]} !important`,
      border: "none",

      "&:disabled": {
        backgroundColor: `${theme.other["switch-disabled-color"]} !important`,
      },
    },

    // Checked (ON) state and disabled
    "input:checked:disabled + &": {
      backgroundColor: `${theme.other["switch-disabled-color"]} !important`,
    },

    // Unckecked (OFF) state and disabled
    "input:disabled + &": {
      backgroundColor: `${theme.other["switch-bg-color-off"]} !important`,
    },

    // Disabled states
    "&:disabled": {
      cursor: "not-allowed",
      backgroundColor: `${theme.other["switch-bg-color-off"]} !important`,
    },
  },

  thumb: {
    width: "18px",
    height: "18px",
    borderRadius: "50%",
    backgroundColor: `${theme.other["switch-handle-color-off"]} !important`,
    border: "none",

    // hover + not checked + not disabled
    "input:hover:not(checked):not(:disabled) + * > &": {
      backgroundColor: `${theme.other["switch-hover-color"]} !important`,
    },

    // hover + checked + not disabled
    "input:checked:hover:not(:disabled) + * > &": {
      backgroundColor: `${theme.other["switch-handle-color-on"]} !important`,
    },

    // checked
    "input:checked + * > &": {
      backgroundColor: `${theme.other["switch-handle-color-on"]} !important`,
    },

    // checked and disabled
    "input:checked:disabled + * > &": {
      backgroundColor: `${theme.other["switch-handle-color-on"]} !important`,
    },

    "input:disabled + * > &": {
      backgroundColor: `${theme.other["switch-disabled-color"]} !important`,
    },
  },
}));

export const VFSwitch: React.FC<VFSwitchProps> = ({
  value = false,
  onChange,
  disabled = false,
}) => {
  const { classes } = useStyles();

  return (
    <Switch
      checked={value}
      onChange={(event) => onChange?.(event.currentTarget.checked)}
      disabled={disabled}
      size="md"
      classNames={{
        root: classes.root,
        track: classes.track,
        thumb: classes.thumb,
      }}
    />
  );
};

// Usage example showcasing all states
export const SwitchExample: React.FC = () => {
  const [checked1, setChecked1] = React.useState(false);
  const [checked2, setChecked2] = React.useState(true);

  const containerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    gap: "32px",
    padding: "20px",
  };

  const rowStyle = {
    display: "flex",
    alignItems: "center",
    gap: "24px",
  };

  const labelStyle = {
    width: "100px",
    fontSize: "14px",
    fontFamily: "SF Pro Display, sans-serif",
    color: "#333",
  };

  return (
    <div style={containerStyle}>
      {/* Default States */}
      <div>
        <h3
          style={{
            marginBottom: "16px",
            fontFamily: "SF Pro Display, sans-serif",
          }}
        >
          Default States
        </h3>
        <div style={rowStyle}>
          <span style={labelStyle}>OFF state:</span>
          <VFSwitch value={false} onChange={() => {}} />
        </div>
        <div style={{ height: "16px" }} />
        <div style={rowStyle}>
          <span style={labelStyle}>ON state:</span>
          <VFSwitch value={true} onChange={() => {}} />
        </div>
      </div>

      {/* Interactive States */}
      <div>
        <h3
          style={{
            marginBottom: "16px",
            fontFamily: "SF Pro Display, sans-serif",
          }}
        >
          Interactive States
        </h3>
        <div style={rowStyle}>
          <span style={labelStyle}>Controlled:</span>
          <VFSwitch value={checked1} onChange={setChecked1} />
          <span style={{ fontSize: "14px" }}>
            Current value: {checked1 ? "ON" : "OFF"}
          </span>
        </div>
      </div>

      {/* Disabled States */}
      <div>
        <h3
          style={{
            marginBottom: "16px",
            fontFamily: "SF Pro Display, sans-serif",
          }}
        >
          Disabled States
        </h3>
        <div style={rowStyle}>
          <span style={labelStyle}>OFF disabled:</span>
          <VFSwitch value={false} onChange={() => {}} disabled />
        </div>
        <div style={{ height: "16px" }} />
        <div style={rowStyle}>
          <span style={labelStyle}>ON disabled:</span>
          <VFSwitch value={true} onChange={() => {}} disabled />
        </div>
      </div>

      {/* Auto-switching Demo */}
      <div>
        <h3
          style={{
            marginBottom: "16px",
            fontFamily: "SF Pro Display, sans-serif",
          }}
        >
          Auto-switching Demo
        </h3>
        <div style={rowStyle}>
          <span style={labelStyle}>Auto-switch:</span>
          <VFSwitch value={checked2} onChange={setChecked2} />
          <button
            onClick={() => setChecked2(!checked2)}
            style={{
              padding: "8px 16px",
              borderRadius: "4px",
              border: "1px solid #ccc",
              background: "#fff",
              cursor: "pointer",
              fontSize: "14px",
            }}
          >
            Toggle Switch
          </button>
        </div>
      </div>
    </div>
  );
};
