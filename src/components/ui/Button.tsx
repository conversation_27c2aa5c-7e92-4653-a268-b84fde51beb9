import {
  Button as <PERSON>tine<PERSON><PERSON><PERSON>,
  <PERSON>tonProps as MantineButtonProps,
  createSty<PERSON>,
  useMantineTheme,
  Loader,
} from "@mantine/core";
import { forwardRef } from "react";
import VFIconComponent from "../icon/vf-icon";
import { IconType } from "../icons/types";

export type ButtonVariant = "primary" | "secondary" | "destructive";
export type ButtonSize = "default" | "small";
export type ButtonDisplay = "withIcon" | "onlyText" | "onlyIcon" | "dropdown";

export interface ButtonProps
  extends Omit<MantineButtonProps, "variant" | "size"> {
  label?: string;
  variant?: ButtonVariant;
  fullWidth?: boolean;
  size?: ButtonSize;
  buttonDisplay?: ButtonDisplay;
  iconName?: IconType;
  className?: string;
  onClick?: () => void;
  loading?: boolean;
}

const useStyles = createStyles(
  (
    theme,
    { buttonDisplay, size, fullWidth, variant, disabled }: Partial<ButtonProps>
  ) => ({
    root: {
      height: "auto",
      "&&": {
        width: fullWidth ? "100%" : "auto",
        minWidth: buttonDisplay === "dropdown" ? "120px" : "auto",
        "&:active": {
          transform: "none", // Disable click animation
          transition: "none", // Remove animation effect
          border: "none",
        },
        "&:focus": {
          transform: "none", // Disable click animation
          transition: "none", // Remove animation effect
          border: "none",
        },
        border:
          buttonDisplay === "dropdown"
            ? `1px solid ${theme.other["btn-secondary-border-color"]}`
            : "none",
        position: "relative",
        display: "inline-flex",
        alignItems: "center",
        justifyContent: buttonDisplay === "dropdown" ? "flex-start" : "center",
        transition: "all 0.1s ease-in-out",
        borderRadius: "4px",
        padding:
          size === "default"
            ? buttonDisplay === "onlyIcon"
              ? "10px"
              : buttonDisplay === "dropdown"
              ? "8px 40px 8px 16px" // Add extra padding on the right for dropdown
              : "8px 16px"
            : buttonDisplay === "onlyIcon"
            ? "5px"
            : buttonDisplay === "dropdown"
            ? "4px 32px 4px 8px" // Add extra padding on the right for small dropdown
            : "4px 8px",

        "&:not(:disabled)": {
          cursor: "pointer",
        },

        "&:disabled": {
          ...(buttonDisplay === "dropdown" && {
            border: `1px solid ${theme.other["btn-secondary-border-color"]}`,
          }),
        },
      },
    },

    primary: {
      "&&": {
        backgroundColor: theme.other["btn-primary-bg-color"],
        color: theme.other["btn-primary-text-color"],

        "&:hover:not(:disabled)": {
          backgroundColor: theme.other["btn-primary-hover-bg-color"],
        },

        "&:focus": {
          border: "transparent",
          outline: `2px solid ${theme.other["btn-primary-bg-color"]}`,
        },

        "&:disabled": {
          backgroundColor: theme.other["btn-primary-disable-bg-color"],
          cursor: "not-allowed",
        },
      },
    },

    secondary: {
      "&&": {
        backgroundColor: "transparent",
        color: theme.other["btn-secondary-text-color"],

        "&:hover:not(:disabled)": {
          backgroundColor: theme.other["btn-secondary-hover-bg-color"],
        },

        "&:focus": {
          border: "transparent",
          outline: `2px solid ${theme.other["btn-primary-bg-color"]}`,
        },

        "&:disabled": {
          color: theme.other["btn-secondary-disabled-text-color"],
          cursor: "not-allowed",
        },
      },
    },

    destructive: {
      "&&": {
        backgroundColor: theme.other["btn-destructive-bg-color"],
        color: theme.other["btn-destructive-text-color"],

        "&:hover:not(:disabled)": {
          backgroundColor: theme.other["btn-destructive-hover-bg-color"],
        },

        "&:focus": {
          border: "transparent",
          outline: `2px solid ${theme.other["btn-destructive-border-color"]}`,
        },

        "&:disabled": {
          color: theme.other["btn-primary-text-color"],
          backgroundColor: theme.other["btn-destructive-disabled-bg-color"],
          cursor: "not-allowed",
        },
      },
    },

    icon: {
      "&&": {
        marginRight: buttonDisplay === "onlyIcon" ? "0" : "4px",
      },
    },

    dropdownIcon: {
      "&&": {
        position: "absolute",
        right: "16px",
        top: "50%",
        transform: "translateY(-50%)",
      },
    },

    dropdownContent: {
      display: "flex",
      alignItems: "center",
      justifyContent: "flex-start",
      textAlign: "left",
      width: "100%",
      paddingRight: "24px", // Add space for the arrow icon
    },

    loaderContainer: {
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      width: "100%",
      minHeight: size === "default" ? "20px" : "16px",
    },
  })
);

export const VFButton = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      label,
      size = "default",
      buttonDisplay = "onlyText",
      iconName,
      fullWidth,
      className,
      children,
      disabled,
      onClick,
      loading = false,
      ...props
    },
    ref
  ) => {
    // If buttonDisplay is "dropdown" and variant is "destructive", default to "primary"
    const finalVariant =
      buttonDisplay === "dropdown" && variant === "destructive"
        ? "primary"
        : variant;

    const { classes, cx } = useStyles({
      buttonDisplay,
      fullWidth,
      size,
      variant: finalVariant,
      disabled,
    });

    const theme = useMantineTheme();

    const buttonClasses = cx(
      classes.root,
      {
        [classes.primary]: finalVariant === "primary",
        [classes.secondary]: finalVariant === "secondary",
        [classes.destructive]: finalVariant === "destructive",
      },
      size === "default" ? "p-heavy" : "small-p-heavy",
      className
    );

    // Determine loader color based on button variant
    const getLoaderColor = () => {
      if (finalVariant === "primary") {
        return theme.other["btn-primary-text-color"];
      } else if (finalVariant === "secondary") {
        return theme.other["btn-secondary-text-color"];
      } else if (finalVariant === "destructive") {
        return theme.other["btn-destructive-text-color"];
      }
      return undefined;
    };

    const renderContent = () => {
      // If loading, show loader
      if (loading) {
        return (
          <div className={classes.loaderContainer}>
            <Loader
              size={size === "default" ? "sm" : "xs"}
              color={getLoaderColor()}
            />
          </div>
        );
      }

      const iconColor =
        finalVariant === "destructive"
          ? disabled
            ? theme.other["btn-primary-text-color"]
            : theme.other["btn-destructive-text-color"]
          : finalVariant === "primary"
          ? disabled
            ? theme.other["btn-primary-text-color"]
            : theme.other["btn-primary-text-color"]
          : disabled
          ? theme.other["btn-secondary-disabled-text-color"]
          : theme.other["btn-secondary-text-color"];

      switch (buttonDisplay) {
        case "onlyText":
          return label ? label : children;
        case "withIcon":
          return (
            <>
              {iconName && (
                <span className={classes.icon}>
                  <VFIconComponent
                    type={iconName}
                    size={size === "default" ? 16 : 12}
                    color={iconColor}
                  />
                </span>
              )}
              {label ? label : children}
            </>
          );
        case "dropdown":
          return (
            <>
              <div
                className={classes.dropdownContent}
                style={{ display: "flex", alignItems: "center" }}
              >
                {iconName && (
                  <span
                    className={classes.icon}
                    style={{
                      display: "inline-flex",
                      alignItems: "center",
                      height: "100%",
                      marginTop: "0",
                      marginBottom: "1px",
                    }}
                  >
                    <VFIconComponent
                      type={iconName}
                      size={size === "default" ? 16 : 12}
                      color={iconColor}
                      style={{ display: "flex", alignItems: "center" }}
                    />
                  </span>
                )}
                <span
                  className="text-truncate"
                  style={{ display: "inline-flex", alignItems: "center" }}
                >
                  {label ? label : children}
                </span>
              </div>
              <span className={classes.dropdownIcon}>
                <VFIconComponent
                  type="arrow-down"
                  size={10}
                  color={iconColor}
                  aria-hidden="true"
                />
              </span>
            </>
          );
        case "onlyIcon":
          return (
            iconName && (
              <span className={classes.icon}>
                <VFIconComponent
                  type={iconName}
                  size={size === "default" ? 16 : 12}
                  color={iconColor}
                />
              </span>
            )
          );
        default:
          return children;
      }
    };

    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (!disabled && !loading && onClick) {
        onClick();
      }
    };

    // Add ARIA attributes for dropdown
    const ariaProps =
      buttonDisplay === "dropdown"
        ? {
            "aria-haspopup": true,
            "aria-expanded": false, // This should ideally be controlled by parent component
            role: "button",
          }
        : {};

    return (
      <MantineButton
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        onClick={handleClick}
        style={buttonDisplay === "dropdown" ? { textAlign: "left" } : undefined}
        {...ariaProps}
        {...props}
      >
        {renderContent()}
      </MantineButton>
    );
  }
);

VFButton.displayName = "VFButton";

export const ButtonExamples = () => {
  return (
    <div style={{ display: "flex", gap: "16px", flexDirection: "column" }}>
      <h3>Basic Dropdown Buttons</h3>

      {/* Primary dropdown button */}
      <VFButton
        buttonDisplay="dropdown"
        variant="primary"
        label="Primary Dropdown"
      />

      {/* Secondary dropdown button */}
      <VFButton
        buttonDisplay="dropdown"
        variant="secondary"
        label="Secondary Dropdown"
      />

      {/* Destructive dropdown - will default to primary variant */}
      <VFButton
        buttonDisplay="dropdown"
        variant="destructive"
        label="Destructive Dropdown (defaults to Primary)"
      />

      {/* Small dropdown button */}
      <VFButton
        buttonDisplay="dropdown"
        variant="primary"
        size="small"
        label="Small Dropdown"
      />

      {/* Disabled dropdown button */}
      <VFButton
        buttonDisplay="dropdown"
        variant="primary"
        disabled
        label="Disabled Dropdown"
      />

      {/* Dropdown button with left icon */}
      <VFButton
        buttonDisplay="dropdown"
        variant="primary"
        label="Dropdown with Icon"
      />

      <h3>Loading Button Examples</h3>

      {/* Primary loading button */}
      <VFButton variant="primary" label="Primary Loading" loading={true} />

      {/* Secondary loading button */}
      <VFButton variant="secondary" label="Secondary Loading" loading={true} />

      {/* Destructive loading button */}
      <VFButton
        variant="destructive"
        label="Destructive Loading"
        loading={true}
      />

      {/* Small loading button */}
      <VFButton
        variant="primary"
        size="small"
        label="Small Loading"
        loading={true}
      />
    </div>
  );
};
