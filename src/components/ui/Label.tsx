import { createStyles } from "@mantine/core";
import React from "react";
import { keyframes } from "@emotion/react";
import VFIconComponent from "../icon/vf-icon";

export enum VFLabelType {
  NEW = "new",
  ERROR = "error",
  LOADING = "loading",
  HIGH_VIRALITY = "high-virality",
  LOW_VIRALITY = "low-virality",
}

interface VFLabelBaseProps {
  type: VFLabelType;
}

interface VFLabelViralityProps extends VFLabelBaseProps {
  type: VFLabelType.HIGH_VIRALITY | VFLabelType.LOW_VIRALITY;
  score: number | string;
}

interface VFLabelLoadingProps extends VFLabelBaseProps {
  type: VFLabelType.LOADING;
  percentage: number;
  eta: string;
}

interface VFLabelErrorProps extends VFLabelBaseProps {
  type: VFLabelType.ERROR;
  message?: string;
}

interface VFLabelNewProps extends VFLabelBaseProps {
  type: VFLabelType.NEW;
}

type VFLabelProps =
  | VFLabelLoadingProps
  | VFLabelErrorProps
  | VFLabelNewProps
  | VFLabelViralityProps;

const pulse = keyframes`
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
`;

const useStyles = createStyles((theme) => ({
  label: {
    position: "absolute",
    fontFamily: "SF Pro Display, sans-serif",
    borderRadius: "4px",
  },

  new: {
    top: "5px",
    right: "5px",
    backgroundColor: theme.other["label-small-default-bg-color"],
    color: theme.other["label-small-default-text-color"],
    padding: "4px 8px",
  },

  error: {
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    backgroundColor: theme.other["surface-main-bg-color"],
    color: theme.other["label-default-text-error-color"],
    padding: "15px 16px",
    border: `1px solid ${theme.other["label-default-border-error-color"]}`,
    whiteSpace: "nowrap",
  },

  virality: {
    color: theme.other["label-small-virality-text-color"],
    padding: "4px 8px",
    position: "static",

    "&.low": {
      backgroundColor: theme.other["label-small-low-virality-bg-color"],
    },

    "&.high": {
      backgroundColor: theme.other["label-small-high-virality-bg-color"],
    },
  },

  loading: {
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    backgroundColor: theme.other["surface-main-bg-color"],
    color: theme.other["label-default-text-color"],
    border: `1px solid ${theme.other["label-default-border-color"]}`,
    padding: "5px 32px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },

  loadingRow: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
  },

  loadingSecondRow: {
    marginTop: "2px",
  },

  percentage: {
    // Pulsating animation
    // animation: `${pulse} 2s ease-in-out infinite`,
  },
}));

export const VFLabel: React.FC<VFLabelProps> = (props) => {
  const { classes, cx } = useStyles();

  switch (props.type) {
    case VFLabelType.NEW:
      return (
        <span className={cx(classes.label, classes.new, "small-p-heavy")}>
          New
        </span>
      );

    case VFLabelType.HIGH_VIRALITY:
    case VFLabelType.LOW_VIRALITY:
      return (
        <span
          className={cx(
            classes.label,
            classes.virality,
            props.type === VFLabelType.HIGH_VIRALITY ? "high" : "low",
            "small-p-heavy"
          )}
        >
          {props.score}
        </span>
      );

    case VFLabelType.ERROR:
      return (
        <span className={cx(classes.label, classes.error, "p-heavy")}>
          {props.message || "ERROR PROCESSING"}
        </span>
      );

    case VFLabelType.LOADING:
      return (
        <div className={cx(classes.label, classes.loading)}>
          <div className={cx(classes.loadingRow, "p-heavy")}>
            <VFIconComponent
              type="time"
              dimensions={{ width: 16, height: 16 }}
              color="currentColor"
            />
            <span className={classes.percentage}>{props.percentage}%</span>
          </div>
          <div className={cx("small-p-heavy", classes.loadingSecondRow)}>
            <span>ETA {props.eta}</span>
          </div>
        </div>
      );
  }
};

// Usage examples:
export const LabelExample: React.FC = () => {
  return (
    <div>
      <div
        style={{
          position: "relative",
          width: "400px",
          height: "200px",
          border: "1px solid #ccc",
          background: "pink",
        }}
      >
        <VFLabel type={VFLabelType.NEW} />
      </div>
      <div
        style={{
          position: "relative",
          width: "400px",
          height: "200px",
          border: "1px solid #ccc",
          background: "pink",
        }}
      >
        <VFLabel type={VFLabelType.ERROR} message="ERROR PROCESSING" />
      </div>
      <div
        style={{
          position: "relative",
          width: "400px",
          height: "200px",
          border: "1px solid #ccc",
          background: "pink",
        }}
      >
        <VFLabel type={VFLabelType.LOADING} percentage={50} eta="5m" />
      </div>
    </div>
  );
};
