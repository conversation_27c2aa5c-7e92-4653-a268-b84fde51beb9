import React, { useState } from "react";
import { createStyles } from "@mantine/core";
import { VFButton } from "./Button";
import VFIconComponent from "../icon/vf-icon";
import Visa<PERSON>ogo from "../../assets/images/visa.png";
import Mastercard<PERSON>ogo from "../../assets/images/mastercard.png";
import <PERSON>ex<PERSON>ogo from "../../assets/images/amex.png";
import <PERSON>cb<PERSON>ogo from "../../assets/images/jcb.png";
import DinersLogo from "../../assets/images/diners.png";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import { formatDate } from "../../utils";
import { ModalConfig, ModalType } from "../../features/modal/types";
import { openModal } from "../../features/modal/modalSlice";
import { getCustomerPortalUrl } from "@/features/auth/authActions";

/**
 * Turn an {@link Address} object into a single-line string.
 *
 * @param address   The raw address object (may contain nulls / empties).
 * @param delimiter What to put between the visible parts.  Default: `", "`.
 * @returns         The formatted address — or `""` if every field is empty.
 */
export function formatAddress(address: any, delimiter: string = ", "): string {
  /** Pushes a value into the collector iff it’s neither null nor blank. */
  const pushIfPresent = (collector: string[], value?: string | null) => {
    if (value && value.trim() !== "") {
      collector.push(value.trim());
    }
  };

  /* 1️⃣ Street lines ------------------------------------------------------ */
  const parts: string[] = [];
  pushIfPresent(parts, address.line1);
  pushIfPresent(parts, address.line2);

  /* 2️⃣ Locality (“City, State ZIP”) -------------------------------------- */
  const localityPieces: string[] = [];
  pushIfPresent(localityPieces, address.city);

  // City and state need a comma between them if both exist.
  if (address.state && address.state.trim() !== "") {
    const state = address.state.trim();
    if (localityPieces.length) {
      localityPieces[localityPieces.length - 1] += `, ${state}`;
    } else {
      localityPieces.push(state);
    }
  }

  // ZIP / postal code sits after the state with a space.
  if (address.postal_code && address.postal_code.trim() !== "") {
    const zip = address.postal_code.trim();
    if (localityPieces.length) {
      localityPieces[localityPieces.length - 1] += ` ${zip}`;
    } else {
      localityPieces.push(zip);
    }
  }

  // If anything ended up in localityPieces, add it as one chunk.
  if (localityPieces.length) {
    parts.push(localityPieces.join(""));
  }

  /* 3️⃣ Country (last) ----------------------------------------------------- */
  pushIfPresent(parts, address.country);

  /* 4️⃣ Join & return ------------------------------------------------------ */
  return parts.join(delimiter);
}

interface PricingFormProps {}

const useStyles = createStyles((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "48px",
  },
  formContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "48px",
    alignSelf: "stretch",
    padding: "16px 16px 32px",
    border: `1px solid ${theme.other["card-border-color"]}`,
    borderRadius: "4px",
    backgroundColor: theme.other["main-bg-color"],
  },
  title: {
    fontFamily: "SF Pro Display, sans-serif",
    textAlign: "left",
    color: theme.other["surface-text-color"],
  },
  fieldsContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "32px",
    alignSelf: "stretch",
  },
  fieldRow: {
    display: "flex",
    flexDirection: "column",
    gap: "24px",
    alignSelf: "stretch",
  },
  fieldHeader: {
    display: "flex",
    flexDirection: "column",
    gap: "4px",
  },
  fieldTitle: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  fieldSubtitle: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-subtext-color"],
  },
  contentRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    gap: "24px",
    alignItems: "center",
  },
  fieldContent: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  planName: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-subtext-color"],
  },
  planPrice: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  boostContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "4px",
  },
  boostText: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-subtext-color"],
  },
  divider: {
    height: "1px",
    width: "100%",
    backgroundColor: theme.other["card-border-color"],
  },
  billingDetails: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  billingName: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  billingInfo: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  cardContainer: {
    display: "flex",
    flexDirection: "row",
    gap: "16px",
    alignItems: "center",
  },
  cardIconContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#FFFFFF",
    borderRadius: "4px",
    width: "fit-content",
    height: "fit-content",
  },
  cardLogo: {
    width: "76px",
    height: "36px",
    display: "block",
  },
  cardDetails: {
    display: "flex",
    flexDirection: "column",
    gap: "4px",
  },
  cardType: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  cardNumber: {
    fontFamily: "SF Pro Display, sans-serif",
    color: theme.other["surface-text-color"],
  },
  actionButton: {
    alignSelf: "center",
  },
}));

// Function to get the correct card logo based on card type
const getCardLogo = (cardType: string) => {
  const type = cardType.toLowerCase();

  if (type.includes("visa")) {
    return { logo: VisaLogo, alt: "Visa" };
  } else if (type.includes("mastercard")) {
    return { logo: MastercardLogo, alt: "Mastercard" };
  } else if (type.includes("amex") || type.includes("american express")) {
    return { logo: AmexLogo, alt: "American Express" };
  } else if (type.includes("jcb")) {
    return { logo: JcbLogo, alt: "JCB" };
  } else if (type.includes("diners") || type.includes("discover")) {
    return { logo: DinersLogo, alt: "Diners Club" };
  }

  // Default to Visa if no match
  return { logo: VisaLogo, alt: "Credit Card" };
};

const getCurrencySymbol = (currency: string) => {
  if (currency === "usd") {
    return "$";
  }

  if (currency === "eur") {
    return "€";
  }

  if (currency === "gbp") {
    return "£";
  }

  return currency;
};

export const PricingForm: React.FC<PricingFormProps> = ({}) => {
  const { classes } = useStyles();
  const { subscriptionData } = useSelector((state: RootState) => state.auth);
  const { userData } = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const [customerPortalUrlLoading, setCustomerPortalUrlLoading] =
    useState(false);

  if (!subscriptionData) {
    return null;
  }

  const formattedDate = formatDate(new Date(subscriptionData.plan.renews_at));
  const planData = subscriptionData.plan;
  const billingData = subscriptionData.billing_details;

  // Get the appropriate card logo
  let cardLogo;

  if (billingData?.card_details) {
    cardLogo = getCardLogo(billingData.card_details.type);
  }

  const handleChangePlan = async () => {
    // User is in trial mode, cannot download
    if (!billingData && userData?.subscription_level === 0) {
      // Configure the modal
      const pricingModalConfig: ModalConfig = {
        type: ModalType.PRICING,
        title: "Upgrade your subscription",
        subtitle:
          "You're using a trial version of Boostcast and downloading your clips is currently unavailable for you. Upgrade to unlock your full experience.",
      };

      dispatch(openModal(pricingModalConfig));
    } else {
      setCustomerPortalUrlLoading(true);

      const result = await dispatch(getCustomerPortalUrl() as any);

      if (getCustomerPortalUrl.fulfilled.match(result)) {
        const { url } = result.payload;

        window.open(url, "_blank");
      }

      setCustomerPortalUrlLoading(false);
    }
  };

  return (
    <div className={classes.container}>
      <div className={classes.formContainer}>
        <h3 className={classes.title}>Pricing</h3>

        <div className={classes.fieldsContainer}>
          {/* Plan Information */}
          <div className={classes.fieldRow}>
            <div className={classes.fieldHeader}>
              <div className={`${classes.fieldTitle} p-heavy`}>Your plan</div>
              {billingData && (
                <div className={`${classes.fieldSubtitle} p-regular`}>
                  Renews {formattedDate}
                </div>
              )}
            </div>
            <div className={classes.contentRow}>
              <div className={classes.fieldContent}>
                <div className={`${classes.planName} p-regular`}>
                  {planData.name}
                </div>
                <div className={`${classes.planPrice} p-heavy`}>
                  {getCurrencySymbol(planData.currency)}
                  {planData.price}
                </div>
                <div className={classes.boostContainer}>
                  <VFIconComponent type="boost" size={16} />
                  <div className={`${classes.boostText} p-heavy`}>
                    {planData.boost} included
                  </div>
                </div>
              </div>
              <VFButton
                variant="secondary"
                label="Change plan"
                onClick={handleChangePlan}
                loading={customerPortalUrlLoading}
                className={classes.actionButton}
              />
            </div>
          </div>

          {billingData && cardLogo && (
            <>
              <div className={classes.divider} />

              <div className={classes.fieldRow}>
                <div className={classes.fieldHeader}>
                  <div className={`${classes.fieldTitle} p-heavy`}>Billing</div>
                </div>
                <div className={classes.contentRow}>
                  <div className={classes.billingDetails}>
                    <div className={`${classes.billingName} p-heavy`}>
                      {billingData.full_name}
                    </div>
                    <div className={`${classes.billingInfo} p-regular`}>
                      {billingData.email}
                    </div>
                    <div className={`${classes.billingInfo} p-regular`}>
                      {formatAddress(billingData.address)}
                    </div>
                  </div>
                  <VFButton
                    variant="secondary"
                    label="Edit details"
                    loading={customerPortalUrlLoading}
                    onClick={handleChangePlan}
                    className={classes.actionButton}
                  />
                </div>
              </div>

              <div className={classes.divider} />

              <div className={classes.fieldRow}>
                <div className={classes.fieldHeader}>
                  <div className={`${classes.fieldTitle} p-heavy`}>Card</div>
                </div>
                <div className={classes.contentRow}>
                  <div className={classes.cardContainer}>
                    <div className={classes.cardIconContainer}>
                      <img
                        src={cardLogo.logo}
                        alt={cardLogo.alt}
                        className={classes.cardLogo}
                      />
                    </div>
                    <div className={classes.cardDetails}>
                      <div className={`${classes.cardType} p-regular`}>
                        {billingData.card_details.type} ending in
                      </div>
                      <div className={`${classes.cardNumber} p-heavy`}>
                        **** {billingData.card_details.last_4_digits}
                      </div>
                    </div>
                  </div>
                  <VFButton
                    variant="secondary"
                    label="Update card"
                    loading={customerPortalUrlLoading}
                    onClick={handleChangePlan}
                    className={classes.actionButton}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};
