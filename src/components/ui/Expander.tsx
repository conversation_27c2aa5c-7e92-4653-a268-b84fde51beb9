import { Accordion, createStyles, useMantineTheme } from "@mantine/core";
import React, { useState } from "react";
import VFIconComponent from "../icon/vf-icon";

export type ExpanderState = "default" | "hover" | "focus" | "disabled";

interface VFExpanderProps {
  title: React.ReactNode;
  children: React.ReactNode;
  initiallyExpanded?: boolean;
  disabled?: boolean;
  className?: string;
  size?: "small" | "default";
}

const useStyles = createStyles(
  (theme, { size = "default" }: { size?: "small" | "default" }) => ({
    root: {
      position: "relative",
      width: "100%",
      fontFamily: "SF Pro Display, sans-serif",
    },

    control: {
      padding: size === "default" ? "8px 16px" : "4px 8px",
      borderRadius: "4px",
      cursor: "pointer",
      color: theme.other["expander-text-color-default"],
      backgroundColor: theme.other["expander-bg-color-default"],
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      borderBottom: `1px solid ${theme.other["expander-bottom-border-color"]}`,
      transition: "background-color 0.2s ease",

      "&:hover:not([data-disabled='true'])": {
        backgroundColor: theme.other["expander-bg-color-hover"],
      },

      "&:focus:not([data-disabled='true'])": {
        outline: "none",
        boxShadow: `inset 0 0 0 1px ${theme.other["expander-focus-border-color"]}`,
      },

      "&[data-disabled='true']": {
        cursor: "not-allowed",
        color: theme.other["expander-text-color-disabled"],
        backgroundColor: theme.other["expander-bg-color-default"],
        pointerEvents: "none",
        opacity: 0.6,
      },
    },

    content: {
      overflow: "hidden",
    },

    titleWrapper: {
      flex: 1,
      display: "flex",
      alignItems: "center",
      color: theme.other["expander-text-color-default"],
    },
  })
);

export const VFExpander: React.FC<VFExpanderProps> = ({
  title,
  children,
  initiallyExpanded = false,
  disabled = false,
  className = "",
  size = "default",
}) => {
  const [expanded, setExpanded] = useState(initiallyExpanded);
  const { classes, cx } = useStyles({ size });
  const theme = useMantineTheme();

  const toggleExpander = () => {
    if (!disabled) {
      setExpanded(!expanded);
    }
  };

  const controlClassNames = cx(
    classes.control,
    size === "default" ? "p-heavy" : "small-p-heavy"
  );

  // Create custom arrow down icon for Mantine's chevron
  const customChevron = (
    <VFIconComponent
      type="arrow-down"
      color={theme.other["expander-text-color-default"]}
      size={10}
    />
  );

  return (
    <div className={cx(classes.root, className)}>
      <Accordion
        multiple={false}
        value={expanded ? "content" : ""}
        onChange={() => toggleExpander()}
        chevron={customChevron}
        styles={{
          item: {
            border: "none",
            backgroundColor: "transparent",
          },
          control: {
            padding: 0,
            margin: 0,
            "&:hover": {
              backgroundColor: "transparent",
            },
          },
          content: {
            padding: 0,
          },
          chevron: {
            marginLeft: "8px",
            transition: "transform 0.2s ease",
            "&[data-rotate]": {
              transform: "rotate(180deg)",
            },
            justifyContent: "right",
          },
        }}
      >
        <Accordion.Item value="content">
          <Accordion.Control
            className={controlClassNames}
            data-disabled={disabled}
            disabled={disabled}
          >
            <div className={classes.titleWrapper}>{title}</div>
          </Accordion.Control>
          <Accordion.Panel>
            <div className={classes.content}>{children}</div>
          </Accordion.Panel>
        </Accordion.Item>
      </Accordion>
    </div>
  );
};

// Example usage component to demonstrate the VFExpander
export const ExpanderExample: React.FC = () => {
  return (
    <div style={{ padding: "20px", maxWidth: "500px" }}>
      {/* Default state */}
      <div style={{ marginBottom: "20px" }}>
        <VFExpander title="Default Expander Item">
          <div>
            This is the expanded content that appears when the expander is
            clicked. It can contain any React components or HTML.
          </div>
        </VFExpander>
      </div>

      {/* Initially expanded */}
      <div style={{ marginBottom: "20px" }}>
        <VFExpander title="Initially Expanded" initiallyExpanded>
          <div>
            This expander starts in the expanded state. You can collapse it by
            clicking.
          </div>
        </VFExpander>
      </div>

      {/* Disabled state */}
      <div style={{ marginBottom: "20px" }}>
        <VFExpander title="Disabled Expander" disabled>
          <div>
            This content won't be accessible because the expander is disabled.
          </div>
        </VFExpander>
      </div>

      {/* Small size */}
      <div style={{ marginBottom: "20px" }}>
        <VFExpander title="Small Size Expander" size="small">
          <div>This is a smaller variant of the expander component.</div>
        </VFExpander>
      </div>
    </div>
  );
};
