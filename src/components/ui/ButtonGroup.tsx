import React, {
  useState,
  useEffect,
  createRef,
  KeyboardEvent,
  Children,
  isValidElement,
  ReactElement,
  cloneElement,
} from "react";
import { createStyles } from "@mantine/core";
import { ButtonProps, ButtonVariant, VFButton } from "./Button";

export interface ButtonGroupItemProps extends ButtonProps {
  selected?: boolean;
  value: string;
}

export interface ButtonGroupProps {
  children: React.ReactNode;
  defaultSelected?: string;
  onChange?: (value: string) => void;
  size?: ButtonProps["size"];
  fullWidth?: boolean;
  className?: string;
}

const useStyles = createStyles((theme) => ({
  buttonGroup: {
    display: "inline-flex",
    overflow: "hidden",
  },
  fullWidth: {
    width: "100%",
  },
  buttonWrapper: {
    flex: 1,
    position: "relative",
    zIndex: 0,
    "&:hover": {
      zIndex: 1,
    },
  },
  // Style for buttons inside the group
  buttonInGroup: {
    "&&": {
      borderRadius: 0,
      position: "relative",
      boxSizing: "border-box",
      // Keep a consistent border to prevent size changes
      border: `1px solid transparent`,
      // Remove focus outline from individual buttons since we'll handle it at the group level
      "&:focus": {
        outline: "none",
      },
    },
  },
  // Style specifically for the first button in the group
  firstButton: {
    "&&": {
      borderTopLeftRadius: "4px",
      borderBottomLeftRadius: "4px",
    },
  },
  // Style specifically for the last button in the group
  lastButton: {
    "&&": {
      borderTopRightRadius: "4px",
      borderBottomRightRadius: "4px",
    },
  },
  // Style for secondary (unselected) buttons to match border requirements
  secondaryButton: {
    "&&": {
      border: `1px solid ${theme.other["btn-secondary-border-color"]} !important`,
      boxSizing: "border-box",

      // Override hover with !important to prevent flashing
      "&:hover:not(:disabled)": {
        border: "1px solid transparent !important",
        backgroundColor: `${theme.other["btn-secondary-hover-bg-color"]} !important`,
      },

      // Don't override focus state, as it's handled by the button component

      // Override disabled state to maintain border with !important
      "&:disabled": {
        border: `1px solid ${theme.other["btn-secondary-border-color"]} !important`,
      },
    },
  },
  // Style for adjacent buttons to remove double borders
  adjacentButton: {
    "&&": {
      marginLeft: "-1px",
    },
  },
  // Style for selected (primary) buttons
  primaryButton: {
    "&&": {
      position: "relative",
      zIndex: 2, // Ensure selected button appears on top
      border: "1px solid transparent !important",
      boxSizing: "border-box",
    },
  },
}));

export function ButtonGroupItem(props: ButtonGroupItemProps) {
  // This is just a typing component and doesn't render anything directly
  return null;
}

export function ButtonGroup({
  children,
  defaultSelected,
  onChange,
  size = "default",
  fullWidth = false,
  className,
}: ButtonGroupProps) {
  const { classes, cx } = useStyles();
  const [selectedValue, setSelectedValue] = useState<string | undefined>(
    defaultSelected
  );
  const buttonRefs: React.RefObject<HTMLButtonElement>[] = [];

  // Process children to get valid ButtonGroupItem elements
  const buttonItems = Children.toArray(children)
    .filter(
      (child) =>
        isValidElement(child) &&
        (child.type === ButtonGroupItem || child.props.value)
    )
    .map((child, index) => {
      const buttonRef = createRef<HTMLButtonElement>();
      buttonRefs.push(buttonRef);
      return { child: child as ReactElement<ButtonGroupItemProps>, index };
    });

  // Handle key navigation
  const handleKeyDown = (
    e: KeyboardEvent<HTMLDivElement>,
    currentIndex: number
  ) => {
    let newIndex = currentIndex;

    switch (e.key) {
      case "ArrowRight":
      case "ArrowDown":
        newIndex = (currentIndex + 1) % buttonItems.length;
        e.preventDefault();
        break;
      case "ArrowLeft":
      case "ArrowUp":
        newIndex = (currentIndex - 1 + buttonItems.length) % buttonItems.length;
        e.preventDefault();
        break;
      case "Home":
        newIndex = 0;
        e.preventDefault();
        break;
      case "End":
        newIndex = buttonItems.length - 1;
        e.preventDefault();
        break;
      case "Enter":
      case " ": // Space key
        const button = buttonItems[currentIndex].child;
        if (button.props.value && !button.props.disabled) {
          handleSelection(button.props.value);
          e.preventDefault();
        }
        break;
    }

    // Focus the new button
    if (newIndex !== currentIndex) {
      buttonRefs[newIndex]?.current?.focus();
    }
  };

  // Handle selection
  const handleSelection = (value: string) => {
    setSelectedValue(value);
    onChange?.(value);
  };

  // Container props
  const groupProps = {
    className: cx(
      classes.buttonGroup,
      { [classes.fullWidth]: fullWidth },
      className
    ),
    role: "tablist",
    "aria-orientation": "horizontal",
  };

  return (
    <div {...groupProps}>
      {buttonItems.map(({ child, index }) => {
        const { value, selected, disabled, ...rest } = child.props;
        const isSelected =
          selected !== undefined ? selected : value === selectedValue;
        const isFirst = index === 0;
        const isLast = index === buttonItems.length - 1;
        const isNotFirst = index > 0;

        // Determine button variant based on selection state
        const variant: ButtonVariant = isSelected ? "primary" : "secondary";

        return (
          <div
            key={value}
            className={cx(classes.buttonWrapper)}
            onKeyDown={(e) => handleKeyDown(e, index)}
          >
            {cloneElement(
              <VFButton
                {...rest}
                ref={buttonRefs[index]}
                variant={variant}
                disabled={disabled}
                onClick={() => !disabled && handleSelection(value)}
                className={cx(
                  classes.buttonInGroup,
                  {
                    [classes.firstButton]: isFirst,
                    [classes.lastButton]: isLast,
                    [classes.secondaryButton]: variant === "secondary",
                    [classes.primaryButton]: variant === "primary",
                    [classes.adjacentButton]: isNotFirst,
                  },
                  child.props.className
                )}
                size={size}
                role="tab"
                aria-selected={isSelected}
                tabIndex={isSelected ? 0 : -1}
              />,
              {}
            )}
          </div>
        );
      })}
    </div>
  );
}

// Example usage component
export const ButtonGroupExamples = () => {
  const [selected2, setSelected2] = useState("option1");
  const [selected3, setSelected3] = useState("tab1");
  const [selected5, setSelected5] = useState("view3");

  return (
    <div
      style={{
        display: "flex",
        gap: "32px",
        flexDirection: "column",
        padding: "20px",
      }}
    >
      <div>
        <h3>ButtonGroup with 2 buttons</h3>
        <ButtonGroup defaultSelected={selected2} onChange={setSelected2}>
          <ButtonGroupItem
            value="option1"
            label="Option 1"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="option2"
            label="Option 2"
            buttonDisplay="onlyText"
          />
        </ButtonGroup>
        <div style={{ marginTop: "8px" }}>Selected: {selected2}</div>
      </div>

      <div>
        <h3>ButtonGroup with 3 buttons</h3>
        <ButtonGroup defaultSelected={selected3} onChange={setSelected3}>
          <ButtonGroupItem
            value="tab1"
            label="Tab 1"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="tab2"
            label="Tab 2"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="tab3"
            label="Tab 3"
            buttonDisplay="onlyText"
          />
        </ButtonGroup>
        <div style={{ marginTop: "8px" }}>Selected: {selected3}</div>
      </div>

      <div>
        <h3>ButtonGroup with 5 buttons and icons</h3>
        <ButtonGroup
          defaultSelected={selected5}
          onChange={setSelected5}
          fullWidth
        >
          <ButtonGroupItem
            value="view1"
            label="List"
            buttonDisplay="withIcon"
            iconName="bell"
          />
          <ButtonGroupItem
            value="view2"
            label="Grid"
            buttonDisplay="withIcon"
            iconName="bell"
          />
          <ButtonGroupItem
            value="view3"
            label="Cards"
            buttonDisplay="withIcon"
            iconName="bell"
          />
          <ButtonGroupItem
            value="view4"
            label="Calendar"
            buttonDisplay="withIcon"
            iconName="bell"
          />
          <ButtonGroupItem
            value="view5"
            label="Timeline"
            buttonDisplay="withIcon"
            iconName="bell"
          />
        </ButtonGroup>
        <div style={{ marginTop: "8px" }}>Selected: {selected5}</div>
      </div>

      <div>
        <h3>ButtonGroup with small size</h3>
        <ButtonGroup size="small">
          <ButtonGroupItem
            value="small1"
            label="Small 1"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="small2"
            label="Small 2"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="small3"
            label="Small 3"
            buttonDisplay="onlyText"
          />
        </ButtonGroup>
      </div>

      <div>
        <h3>ButtonGroup with disabled button</h3>
        <ButtonGroup>
          <ButtonGroupItem
            value="enabled1"
            label="Enabled"
            buttonDisplay="onlyText"
          />
          <ButtonGroupItem
            value="disabled"
            label="Disabled"
            buttonDisplay="onlyText"
            disabled
          />
          <ButtonGroupItem
            value="enabled2"
            label="Enabled"
            buttonDisplay="onlyText"
          />
        </ButtonGroup>
      </div>
    </div>
  );
};
