import React from "react";
import { Dropzone } from "@mantine/dropzone";
import { createStyles, useMantineTheme } from "@mantine/core";
import VFIconComponent from "../icon/vf-icon";
import { MAX_VIDEO_SIZE } from "../../constants";

interface FileUploadProps {
  variant: "video" | "font";
  onDrop: (files: File[]) => void;
  onDragLeave?: () => void;
  onReject?: () => void;
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
  maxSize?: number;
  activateOnClick?: boolean;
}

const ACCEPTS = {
  video: { "video/mp4": [".mp4"] },
  font: {
    "font/ttf": [".ttf"],
    "font/otf": [".otf"],
    "font/woff": [".woff"],
    "font/woff2": [".woff2"],
    // Accept woff2 for future-proofing
    "application/x-font-ttf": [".ttf"],
    "application/x-font-otf": [".otf"],
    "application/font-woff": [".woff"],
    "application/font-woff2": [".woff2"],
  },
};

const MESSAGES = {
  video: {
    initial: "Drag your video file to get started",
    reject: "Please drag a MP4 video file under 10GB",
  },
  font: {
    initial: "Drag your TTF, OTF or WOFF file to get started",
    reject: "Please drag a TTF, OTF or WOFF font file",
  },
};

const useStyles = createStyles((theme) => ({
  root: {
    width: "100%",
    position: "relative",
  },
  dropzoneInner: {
    width: "100%",
    height: "100%",
    minHeight: 120,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "column",
    gap: 20,
    backgroundColor: `${theme.other["uploader-bg-color"]} !important`,
    border: `1px dashed ${theme.other["uploader-border-color"]} !important`,
    borderRadius: 4,
    padding: "22px",
    span: {
      color: theme.other["uploader-text-color"],
      "&[data-rejected]": {
        color: theme.other["uploader-error-color"],
      },
    },
    "&[data-reject]": {
      borderColor: `${theme.other["uploader-error-color"]} !important`,
    },
  },
}));

export function FileUpload({
  variant,
  onDrop,
  onDragLeave,
  onReject,
  loading,
  className,
  style,
  maxSize = MAX_VIDEO_SIZE,
  activateOnClick = false,
}: FileUploadProps) {
  const { classes, cx } = useStyles();
  const theme = useMantineTheme();
  const accept = ACCEPTS[variant];
  const messages = MESSAGES[variant];

  const initialState = (
    <>
      <VFIconComponent
        type="upload-new"
        color={theme.other["uploader-text-color"]}
        dimensions={{ width: 24, height: 24 }}
        style={{ marginBottom: 8 }}
      />
      <span className="p-heavy">{messages.initial}</span>
    </>
  );

  return (
    <div className={cx(classes.root, className)} style={style}>
      <Dropzone
        onDrop={onDrop}
        onDragLeave={onDragLeave}
        onReject={onReject}
        maxSize={maxSize}
        accept={accept}
        className={classes.dropzoneInner}
        activateOnClick={activateOnClick}
        loading={loading}
      >
        <Dropzone.Accept>{initialState}</Dropzone.Accept>
        <Dropzone.Idle>{initialState}</Dropzone.Idle>
        <Dropzone.Reject>
          <VFIconComponent
            type="upload-new"
            color={theme.other["uploader-error-color"]}
            dimensions={{ width: 24, height: 24 }}
            style={{ marginBottom: 8 }}
          />
          <span className="p-heavy" data-rejected>
            {messages.reject}
          </span>
        </Dropzone.Reject>
      </Dropzone>
    </div>
  );
}

export default FileUpload;
