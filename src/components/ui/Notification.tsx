import { createStyles } from "@mantine/core";
import { useEffect, useState } from "react";
import VFIconComponent from "../icon/vf-icon";

type NotificationVariant = "default" | "success" | "error";

interface VFNotificationProps {
  message: string;
  variant?: NotificationVariant;
  closable?: boolean;
  width?: number;
  onClose?: () => void;
  style?: React.CSSProperties;
}

const useStyles = createStyles(
  (theme, { variant = "default", width }: Partial<VFNotificationProps>) => {
    const getVariantStyles = (variant: NotificationVariant) => ({
      background: theme.other[`notification-${variant}-bg-color`],
      border: theme.other[`notification-${variant}-border-color`],
      text: theme.other[`notification-${variant}-text-color`],
    });

    return {
      notification: {
        position: "relative",
        width: width ? `${width}px` : "100%",
        padding: "16px 32px",
        backgroundColor: getVariantStyles(variant).background,
        border: `1px solid ${getVariantStyles(variant).border}`,
        borderRadius: "4px",
        color: getVariantStyles(variant).text,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        opacity: 0,
        transform: "translateY(-10px)",
        transition: "opacity 0.5s ease, transform 0.5s ease",

        "&[data-mounted='true']": {
          opacity: 1,
          transform: "translateY(0)",
        },
      },

      message: {
        textAlign: "center",
        maxWidth: "100%",
        overflow: "hidden",
        display: "-webkit-box",
        "-webkit-line-clamp": 2,
        "-webkit-box-orient": "vertical",
        margin: 0,
        pointerEvents: "none",
      },

      closeButton: {
        position: "absolute",
        right: "32px",
        top: "50%",
        transform: "translateY(-50%)",
        cursor: "pointer",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "4px",

        "&:hover": {
          opacity: 0.8,
        },

        "&:focus": {
          outline: "none",
        },
      },
    };
  }
);

export const VFNotification: React.FC<VFNotificationProps> = ({
  message,
  variant = "default",
  closable = false,
  width,
  onClose,
  style,
}) => {
  const { classes, cx } = useStyles({ variant, width });
  const [isMounted, setIsMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    // Trigger mount animation
    const mountTimer = setTimeout(() => {
      setIsMounted(true);
    }, 50);

    return () => clearTimeout(mountTimer);
  }, []);

  const handleClose = () => {
    setIsMounted(false);
    setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 500);
  };

  if (!isVisible) return null;

  return (
    <div
      className={classes.notification}
      data-mounted={isMounted}
      style={style}
    >
      <p className={cx(classes.message, "p-heavy")}>{message}</p>
      {closable && (
        <div
          className={classes.closeButton}
          onClick={handleClose}
          role="button"
          tabIndex={0}
        >
          <VFIconComponent type="x" size={10} color="currentColor" />
        </div>
      )}
    </div>
  );
};

// Example usage
export const NotificationExample: React.FC = () => {
  const [showNotifications, setShowNotifications] = useState({
    default: true,
    success: true,
    error: true,
    withWidth: true,
    nonClosable: true,
  });

  const handleClose = (key: keyof typeof showNotifications) => {
    setShowNotifications((prev) => ({
      ...prev,
      [key]: false,
    }));
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        flexDirection: "column",
        gap: "12px",
      }}
    >
      {/* Default variant with closable */}
      {showNotifications.default && (
        <VFNotification
          message="Preparing download of 10 clips (ETA 5 min)"
          variant="default"
          closable
          onClose={() => handleClose("default")}
        />
      )}

      {/* Success variant with closable */}
      {showNotifications.success && (
        <VFNotification
          message="Preparing download of 10 clips (ETA 5 min)"
          variant="success"
          closable
          onClose={() => handleClose("success")}
        />
      )}

      {/* Error variant with closable */}
      {showNotifications.error && (
        <VFNotification
          message="Preparing download of 10 clips (ETA 5 min)"
          variant="error"
          closable
          onClose={() => handleClose("error")}
        />
      )}

      {/* Fixed width example */}
      {showNotifications.withWidth && (
        <VFNotification
          message="Preparing download of 10 clips (ETA 5 min)"
          variant="default"
          width={700}
          closable
          onClose={() => handleClose("withWidth")}
        />
      )}

      {/* Non-closable example */}
      {showNotifications.nonClosable && (
        <VFNotification
          message="Preparing download of 10 clips (ETA 5 min)"
          variant="success"
        />
      )}
    </div>
  );
};
