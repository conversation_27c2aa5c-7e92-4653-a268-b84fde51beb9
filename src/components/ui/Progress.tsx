import { createStyles } from "@mantine/core";
import { useState, useEffect } from "react";

interface ProgressBarProps {
  /**
   * Progress value from 0 to 100
   */
  value: number;
  /**
   * Custom width of the progress bar (optional)
   */
  width?: number | string;
}

const useStyles = createStyles(
  (theme, { width }: Partial<ProgressBarProps>) => ({
    progressBarContainer: {
      width: width || "100%",
      height: 8, // Container height matches the progress indicator
      position: "relative",
    },

    track: {
      position: "absolute",
      width: "100%",
      height: 2, // Track height is 2px
      backgroundColor: theme.other["progressbar-track-color"],
      borderRadius: 100,
      top: "50%",
      transform: "translateY(-50%)", // Center the track vertically
    },

    progressIndicator: {
      position: "absolute",
      height: 8, // Progress indicator height is 8px
      backgroundColor: theme.other["progressbar-progress-color"],
      borderRadius: 100,
      transition: "width 0.3s ease-in-out",
      left: 0,
      top: 0,
    },
  })
);

export const ProgressBar: React.FC<ProgressBarProps> = ({ value, width }) => {
  // Ensure value is between 0 and 100
  const normalizedValue = Math.min(Math.max(value, 0), 100);
  const { classes } = useStyles({ width });

  return (
    <div
      className={classes.progressBarContainer}
      role="progressbar"
      aria-valuenow={normalizedValue}
      aria-valuemin={0}
      aria-valuemax={100}
    >
      <div className={classes.track} />
      <div
        className={classes.progressIndicator}
        style={{ width: `${normalizedValue}%` }}
      />
    </div>
  );
};

// Example usage with loading simulation
export const ProgressBarExample: React.FC = () => {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Reset loading state when component unmounts
  useEffect(() => {
    return () => {
      setIsLoading(false);
      setProgress(0);
    };
  }, []);

  // Handle loading simulation
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;

    if (isLoading) {
      setProgress(0);

      // Update progress every 50ms
      intervalId = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 100) {
            clearInterval(intervalId);
            return 100;
          }
          return prev + 1;
        });
      }, 50); // This will make it complete in ~5 seconds

      // Stop loading after 5.5 seconds
      timeoutId = setTimeout(() => {
        setIsLoading(false);
        setProgress(100);
      }, 5500);
    }

    return () => {
      clearInterval(intervalId);
      clearTimeout(timeoutId);
    };
  }, [isLoading]);

  const handleStartLoading = () => {
    setIsLoading(true);
  };

  return (
    <div style={{ padding: 20 }}>
      {/* Full width example with loading simulation */}
      <div>
        <ProgressBar value={progress} />
        <div style={{ marginTop: 10 }}>
          <button
            onClick={handleStartLoading}
            disabled={isLoading}
            style={{
              padding: "8px 16px",
              cursor: isLoading ? "not-allowed" : "pointer",
              opacity: isLoading ? 0.7 : 1,
            }}
          >
            {isLoading ? "Loading..." : "Start Loading"}
          </button>
          <span style={{ marginLeft: 10 }}>
            Progress: {Math.round(progress)}%
          </span>
        </div>
      </div>

      {/* Custom width example */}
      <div style={{ marginTop: 20 }}>
        <ProgressBar value={45} width={200} />
      </div>
    </div>
  );
};

export default ProgressBar;
