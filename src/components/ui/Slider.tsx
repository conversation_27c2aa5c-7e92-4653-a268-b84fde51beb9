import {
  Slider as MantineSlider,
  SliderProps as MantineSliderProps,
  createStyles,
  useMantineTheme,
} from "@mantine/core";
import { forwardRef } from "react";

export type SliderVariant = "default" | "filled" | "outlined";

export interface VFSliderProps extends Omit<MantineSliderProps, "variant"> {
  variant?: SliderVariant;
  fullWidth?: boolean;
  className?: string;
  value?: number;
  defaultValue?: number;
  min?: number;
  max?: number;
  onChange?: (value: number) => void;
  onChangeEnd?: (value: number) => void;
}

const useStyles = createStyles(
  (
    theme,
    {
      variant,
      fullWidth,
    }: {
      variant: SliderVariant;
      fullWidth?: boolean;
    }
  ) => ({
    root: {
      "&&": {
        width: fullWidth ? "100%" : "auto",
        padding: "12px 0",
      },
    },
    track: {
      "&&": {
        height: 2,
        borderRadius: 100,
        position: "relative",
        "&::before": {
          backgroundColor: theme.other["progressbar-track-color"],
        },
      },
    },
    bar: {
      "&&": {
        height: 8,
        backgroundColor: theme.other["progressbar-progress-color"],
        borderRadius: 100,
        position: "absolute",
        top: "50%",
        transform: "translateY(-50%)",
      },
    },
    thumb: {
      "&&": {
        height: 18,
        width: 19,
        backgroundColor: theme.other["progressbar-progress-color"],
        borderWidth: variant === "outlined" ? 2 : 0,
        borderColor:
          variant === "outlined"
            ? theme.other["progressbar-track-color"]
            : "transparent",

        "&:hover": {
          backgroundColor: "#FFFFFF",
        },

        "&:active": {
          transform: "translate(-50%, -50%) scale(1.1)",
        },
      },
    },
    // Styles for the 'filled' variant where the thumb is completely filled
    filledThumb: {
      "&&": {
        backgroundColor: theme.other["progressbar-progress-color"] || "#29B1A1",
        borderWidth: 0,
      },
    },
    // Default variant has no thumb (just a thicker bar)
    defaultThumb: {
      "&&": {
        display: "none",
      },
    },
  })
);

export const VFSlider = forwardRef<HTMLDivElement, VFSliderProps>(
  (
    {
      variant = "default",
      fullWidth,
      className,
      value,
      defaultValue,
      min = 0,
      max = 100,
      onChange,
      onChangeEnd,
      ...props
    },
    ref
  ) => {
    const { classes, cx } = useStyles({
      variant,
      fullWidth,
    });

    const theme = useMantineTheme();

    const sliderClasses = {
      root: cx(classes.root, className),
      track: classes.track,
      bar: classes.bar,
      thumb: cx(
        classes.thumb,
        variant === "default" && classes.defaultThumb,
        variant === "filled" && classes.filledThumb
      ),
    };

    const handleChange = (newValue: number) => {
      if (onChange) {
        onChange(newValue);
      }
    };

    const handleChangeEnd = (newValue: number) => {
      if (onChangeEnd) {
        onChangeEnd(newValue);
      }
    };

    return (
      <MantineSlider
        ref={ref}
        classNames={sliderClasses}
        value={value}
        defaultValue={defaultValue}
        min={min}
        max={max}
        onChange={handleChange}
        onChangeEnd={handleChangeEnd}
        {...props}
      />
    );
  }
);

VFSlider.displayName = "VFSlider";

export default VFSlider;
