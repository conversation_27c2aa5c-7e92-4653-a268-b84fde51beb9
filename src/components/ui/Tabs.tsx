import {
  Tabs as MantineTabs,
  createStyles,
  TabsProps as MantineTabsProps,
  TabsValue,
} from "@mantine/core";
import { TabsPlacement } from "@mantine/core/lib/Tabs/Tabs.types";
import React from "react";

interface VFTabsProps extends Omit<MantineTabsProps, "classNames" | "styles"> {
  children: React.ReactNode;
  orientation?: "horizontal" | "vertical";
  placement?: TabsPlacement;
  width?: number | string;
}

interface VFTabProps {
  value: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  rightSection?: React.ReactNode;
  disabled?: boolean;
}

interface VFTabsListProps {
  children: React.ReactNode;
  position?: "left" | "right" | "center" | "apart";
  grow?: boolean;
}

interface VFTabsPanelProps {
  value: string;
  children: React.ReactNode;
}

const useStyles = createStyles((theme) => ({
  root: {
    width: "100%",
  },

  tabsList: {
    border: "none",
  },

  tab: {
    borderRadius: "4px",
    textTransform: "uppercase",
    padding: "8px 16px",
    color: theme.other["tabs-default-text-color"],
    backgroundColor: "transparent",
    border: "1px solid transparent",
    textAlign: "left",
    width: 246,
    boxSizing: "border-box",
    transition: "background-color 0.2s, color 0.2s",

    "&:hover": {
      cursor: "pointer",
      backgroundColor: theme.other["tabs-default-bg-color-hover"],
      color: theme.other["tabs-default-text-color"],
    },

    "&:focus": {
      border: `1px solid ${theme.other["tabs-focus-border-color"]}`,
    },

    "&[data-active]": {
      backgroundColor: theme.other["tabs-selected-bg-color"],
      color: theme.other["tabs-selected-text-color"],

      "&:hover": {
        backgroundColor: theme.other["tabs-selected-bg-color-hover"],
        color: theme.other["tabs-selected-text-color"],
      },

      "&:focus": {
        border: `1px solid ${theme.other["tabs-focus-border-color"]}`,
        outline: "none",
      },
    },

    "&[data-disabled]": {
      opacity: 0.5,
      cursor: "not-allowed",
    },
  },

  tabIcon: {
    marginRight: "8px",
    display: "flex",
    alignItems: "center",
  },

  tabRightSection: {
    marginLeft: "8px",
    display: "flex",
    alignItems: "center",
  },

  panel: {
    // padding: "16px 0",
  },
}));

export const VFTabs: React.FC<VFTabsProps> & {
  Tab: React.FC<VFTabProps>;
  List: React.FC<VFTabsListProps>;
  Panel: React.FC<VFTabsPanelProps>;
} = ({
  children,
  orientation = "horizontal",
  width = "100%",
  placement = "left",
  ...props
}) => {
  const { classes } = useStyles();

  const styles = {
    root: {
      width: typeof width === "number" ? `${width}px` : width,
    },
  };

  return (
    <MantineTabs
      orientation={orientation}
      placement={placement}
      classNames={{
        root: classes.root,
        tabsList: classes.tabsList,
        tab: classes.tab,
        tabIcon: classes.tabIcon,
        tabRightSection: classes.tabRightSection,
        panel: classes.panel,
      }}
      styles={{ root: styles.root }}
      {...props}
    >
      {children}
    </MantineTabs>
  );
};

const Tab: React.FC<VFTabProps> = ({
  value,
  children,
  icon,
  rightSection,
  disabled,
}) => {
  return (
    <MantineTabs.Tab
      value={value}
      icon={icon}
      rightSection={rightSection}
      disabled={disabled}
    >
      <h4>{children}</h4>
    </MantineTabs.Tab>
  );
};

const List: React.FC<VFTabsListProps> = ({ children, position, grow }) => {
  return (
    <MantineTabs.List position={position} grow={grow}>
      {children}
    </MantineTabs.List>
  );
};

const Panel: React.FC<VFTabsPanelProps> = ({ value, children }) => {
  return <MantineTabs.Panel value={value}>{children}</MantineTabs.Panel>;
};

VFTabs.Tab = Tab;
VFTabs.List = List;
VFTabs.Panel = Panel;

// Usage example:
export const TabsExample: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<TabsValue>("settings");

  return (
    <div style={{ background: "#121212", color: "#FAFAFA", padding: "20px" }}>
      <h3>Horizontal Tabs</h3>
      <VFTabs defaultValue="gallery">
        <VFTabs.List>
          <VFTabs.Tab value="account">Account</VFTabs.Tab>
          <VFTabs.Tab value="settings">Settings</VFTabs.Tab>
          <VFTabs.Tab value="gallery">Gallery</VFTabs.Tab>
        </VFTabs.List>

        <VFTabs.Panel value="account">Account tab content</VFTabs.Panel>
        <VFTabs.Panel value="settings">Settings tab content</VFTabs.Panel>
        <VFTabs.Panel value="gallery">Gallery tab content</VFTabs.Panel>
      </VFTabs>

      <div style={{ height: "32px" }} />

      <h3>Vertical Tabs</h3>
      <VFTabs placement="left" orientation="vertical" defaultValue="gallery">
        <VFTabs.List>
          <VFTabs.Tab value="account">Account</VFTabs.Tab>
          <VFTabs.Tab value="settings">Settings</VFTabs.Tab>
          <VFTabs.Tab value="gallery">Gallery</VFTabs.Tab>
        </VFTabs.List>

        <VFTabs.Panel value="account">Account tab content</VFTabs.Panel>
        <VFTabs.Panel value="settings">Settings tab content</VFTabs.Panel>
        <VFTabs.Panel value="gallery">Gallery tab content</VFTabs.Panel>
      </VFTabs>

      <div style={{ height: "32px" }} />

      <h3>Controlled Tabs</h3>
      <VFTabs value={activeTab} onTabChange={setActiveTab}>
        <VFTabs.List>
          <VFTabs.Tab value="account">Account</VFTabs.Tab>
          <VFTabs.Tab value="settings">Settings</VFTabs.Tab>
          <VFTabs.Tab value="gallery" disabled>
            Gallery
          </VFTabs.Tab>
        </VFTabs.List>

        <VFTabs.Panel value="account">Account tab content</VFTabs.Panel>
        <VFTabs.Panel value="settings">Settings tab content</VFTabs.Panel>
        <VFTabs.Panel value="gallery">Gallery tab content</VFTabs.Panel>
      </VFTabs>
    </div>
  );
};
