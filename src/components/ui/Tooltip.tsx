import { createStyles, Tooltip, TooltipProps } from "@mantine/core";
import React from "react";

interface VFTooltipProps {
  title?: string;
  content?: string;
  children: React.ReactNode;
  position?: TooltipProps["position"];
  opened?: boolean;
  withArrow?: boolean;
  disabled?: boolean;
}

const useStyles = createStyles((theme) => ({
  tooltip: {
    backgroundColor: theme.other["tooltip-bg-color"],
    padding: "4px 8px",
    borderRadius: "4px",
  },
  title: {
    color: theme.other["tooltip-text-color"],
    margin: 0,
  },
  content: {
    color: theme.other["tooltip-text-color"],
  },
}));

export const VFTooltip: React.FC<VFTooltipProps> = ({
  title,
  content,
  children,
  position = "top",
  opened,
  withArrow = true,
  disabled = false,
}) => {
  const { classes } = useStyles();

  const tooltipContent = (
    <>
      {title && <h4 className={classes.title}>{title}</h4>}
      {content && <p className={`p-regular ${classes.content}`}>{content}</p>}
    </>
  );

  return (
    <Tooltip
      label={tooltipContent}
      position={position}
      withArrow={withArrow}
      classNames={{
        tooltip: classes.tooltip,
      }}
      opened={opened}
      disabled={disabled}
    >
      {children}
    </Tooltip>
  );
};

// Example usage
export const TooltipExample: React.FC = () => {
  return (
    <div style={{ padding: "100px", display: "flex", gap: "24px" }}>
      <VFTooltip title="TOOLTIP TITLE" content="Content">
        <div
          style={{
            padding: "8px 16px",
            background: "#333",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Content
        </div>
      </VFTooltip>

      <VFTooltip content="Content without title">
        <div
          style={{
            padding: "8px 16px",
            background: "#333",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Content
        </div>
      </VFTooltip>

      <VFTooltip
        title="TOOLTIP TITLE"
        content="Content"
        position="bottom"
        withArrow
      >
        <div
          style={{
            padding: "8px 16px",
            background: "#333",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Content
        </div>
      </VFTooltip>
    </div>
  );
};
