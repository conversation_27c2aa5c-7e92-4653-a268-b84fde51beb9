import { TextInput, createStyles, useMantineTheme } from "@mantine/core";
import { IconType } from "../icons/types";
import VFIconComponent from "../icon/vf-icon";
import React from "react";
import { VFButton } from "./Button";

interface VFTextboxProps {
  width?: number | string;
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  valid?: boolean;
  errorMessage?: string;
  hintMessage?: string;
  leftIconType?: IconType;
  rightIconType?: IconType;
  type?: "text" | "numeric" | "password";
  min?: number;
  max?: number;
  step?: number;
  showValidationIcons?: boolean;
  useFixedWidth?: boolean;
  showNumericControls?: boolean;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
}

const useStyles = createStyles(
  (
    theme,
    {
      disabled,
      width,
      type,
      showValidationIcons,
      useFixedWidth,
      showNumericControls,
    }: {
      disabled: boolean;
      width: number | string;
      type?: "text" | "numeric" | "password";
      showValidationIcons?: boolean;
      useFixedWidth?: boolean;
      showNumericControls?: boolean;
    }
  ) => ({
    root: {
      position: "relative",
      display: "flex",
      flexDirection: "column",
      ...(useFixedWidth
        ? { width: typeof width === "number" ? `${width}px` : width }
        : { minWidth: typeof width === "number" ? `${width}px` : width }),
    },
    invalidInput: {
      border: `1px solid ${theme.other["textbox-invalid-color"]}`,
    },
    input: {
      height: "36px",
      backgroundColor: `${theme.other["textbox-bg-color"]} !important`,
      border: `1px solid ${theme.other["textbox-border-color"]}`,
      borderRadius: "4px",
      color: `${theme.other["textbox-text-color"]} !important`,
      fontSize: "16px",
      lineHeight: "19px",
      fontFamily: "SF Pro Display, sans-serif",
      transition: "none",
      // Default padding with only left icon
      padding: "8px 16px",

      '&[data-with-right-icon="true"]': {
        paddingRight: "34px", // Add extra padding when right icon is present
      },

      '&[data-with-left-icon="true"]': {
        paddingLeft: "40px", // Add extra padding when left icon is present
      },

      '&[data-show-validation-icons="true"]': {
        paddingRight: "34px", // Add extra padding when validation icons are shown
      },

      '&[data-is-numeric="true"][data-show-numeric-controls="true"]': {
        paddingRight: "50px", // Add extra padding for numeric controls
      },

      "&::placeholder": {
        color: `${
          disabled
            ? theme.other["textbox-disabled-text-color"]
            : theme.other["textbox-placeholder-text-color"]
        } !important`,
      },

      "&:hover": {
        border: `1px solid ${theme.other["textbox-hover-border-color"]}`,
      },

      "&:focus": {
        border: `1px solid ${theme.other["textbox-focus-border-color"]}`,
      },

      "&:disabled": {
        border: `1px solid ${theme.other["textbox-border-color"]}`,
        backgroundColor: theme.other["textbox-bg-color"],
      },
    },

    leftIcon: {
      zIndex: 1,
      position: "absolute",
      left: "16px",
      top: "50%",
      transform: "translateY(-50%)",
      pointerEvents: "none",
    },

    rightIcon: {
      position: "absolute",
      right: type === "numeric" && showNumericControls ? "40px" : "16px",
      top: "50%",
      transform: "translateY(-50%)",
      cursor: "pointer",
    },

    numericControls: {
      position: "absolute",
      right: 0,
      top: "50%",
      transform: "translateY(-50%)",
      display: "flex",
      flexDirection: "column",
      height: "32px",
      width: "24px",
    },

    numericButton: {
      padding: "0 !important",
      minHeight: "16px !important",
      height: "16px !important",
      width: "24px !important",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",

      "&:focus": {
        outline: "none !important",
      },
    },

    additionalText: {
      marginTop: "5px",
      fontSize: "12px",
      fontFamily: "SF Pro Display, sans-serif",
      fontWeight: 400,
      lineHeight: "14.32px",
      textAlign: "left",
    },

    errorText: {
      color: theme.other["textbox-invalid-color"],
    },

    hintText: {
      color: theme.other["textbox-placeholder-text-color"],
    },

    inputWrapper: {
      position: "relative",
    },
  })
);

export const VFTextbox: React.FC<VFTextboxProps> = ({
  width = 200,
  value,
  onChange,
  placeholder,
  disabled = false,
  valid = true,
  errorMessage,
  hintMessage,
  leftIconType,
  rightIconType,
  type = "text",
  min,
  max,
  step = 1,
  showValidationIcons = false,
  useFixedWidth = false,
  showNumericControls = true,
  onKeyDown,
}) => {
  const { classes } = useStyles({
    disabled,
    width,
    type,
    showValidationIcons,
    useFixedWidth,
    showNumericControls,
  });
  const isEmpty = value === undefined || value === null || value === "";
  const theme = useMantineTheme();

  const inputColor = disabled
    ? theme.other["textbox-disabled-text-color"]
    : isEmpty
    ? theme.other["textbox-placeholder-text-color"]
    : theme.other["textbox-text-color"];

  // Password visibility state
  const [isPasswordVisible, setIsPasswordVisible] = React.useState(false);
  const isPasswordType = type === "password";
  const inputType = isPasswordType
    ? isPasswordVisible
      ? "text"
      : "password"
    : type === "numeric"
    ? "number"
    : "text";

  const handlePasswordToggle = (
    event:
      | React.MouseEvent<HTMLDivElement>
      | React.KeyboardEvent<HTMLDivElement>
  ) => {
    if (disabled) return;
    if (
      (event as React.KeyboardEvent<HTMLDivElement>).type === "keydown" &&
      (event as React.KeyboardEvent<HTMLDivElement>).key !== "Enter" &&
      (event as React.KeyboardEvent<HTMLDivElement>).key !== " "
    ) {
      return;
    }
    setIsPasswordVisible((v) => !v);
  };

  const handleNumericIncrement = () => {
    if (disabled || !onChange) return;

    const currentValue = isEmpty ? 0 : parseFloat(value!);
    if (isNaN(currentValue)) return;

    let newValue = currentValue + step;
    if (max !== undefined && newValue > max) {
      newValue = max;
    }

    onChange(newValue.toString());
  };

  const handleNumericDecrement = () => {
    if (disabled || !onChange) return;

    const currentValue = isEmpty ? 0 : parseFloat(value!);
    if (isNaN(currentValue)) return;

    let newValue = currentValue - step;
    if (min !== undefined && newValue < min) {
      newValue = min;
    }

    onChange(newValue.toString());
  };

  return (
    <div className={classes.root}>
      <div className={classes.inputWrapper} data-disabled={disabled}>
        {leftIconType && (
          <VFIconComponent
            type={leftIconType}
            className={classes.leftIcon}
            color={inputColor}
            dimensions={{ width: 16, height: 16 }}
          />
        )}
        <TextInput
          classNames={{
            input: classes.input,
            invalid: classes.invalidInput,
          }}
          value={value}
          onChange={(event) => onChange?.(event.currentTarget.value)}
          placeholder={placeholder}
          disabled={disabled}
          error={!valid}
          type={inputType}
          data-with-right-icon={!!rightIconType || isPasswordType}
          data-with-left-icon={!!leftIconType}
          data-is-valid={valid}
          data-is-numeric={type === "numeric"}
          data-show-numeric-controls={type === "numeric" && showNumericControls}
          data-show-validation-icons={showValidationIcons}
          onKeyDown={onKeyDown}
        />
        {/* Right icon: password toggle if type=password and no rightIconType */}
        {rightIconType && (
          <VFIconComponent
            type={rightIconType}
            className={classes.rightIcon}
            color={inputColor}
            dimensions={{ width: 16, height: 16 }}
          />
        )}
        {isPasswordType && !rightIconType && (
          <div
            className={classes.rightIcon}
            onClick={handlePasswordToggle}
            tabIndex={-1}
            aria-label={isPasswordVisible ? "Hide password" : "Show password"}
            onKeyDown={handlePasswordToggle}
            role="button"
          >
            <VFIconComponent
              type={isPasswordVisible ? "eye-slash" : "eye"}
              color={inputColor}
              dimensions={{ width: 16, height: 16 }}
            />
          </div>
        )}
        {/* Status icons */}
        {type !== "numeric" &&
          !isEmpty &&
          showValidationIcons &&
          valid &&
          !rightIconType &&
          !isPasswordType && (
            <VFIconComponent
              type="check"
              className={classes.rightIcon}
              color={theme.other["textbox-valid-color"]}
              dimensions={{ width: 16, height: 16 }}
            />
          )}
        {type !== "numeric" &&
          !isEmpty &&
          showValidationIcons &&
          !valid &&
          !rightIconType &&
          !isPasswordType && (
            <VFIconComponent
              type="warning-new"
              className={classes.rightIcon}
              color={theme.other["textbox-invalid-color"]}
              dimensions={{ width: 16, height: 16 }}
            />
          )}

        {/* For numeric type, show validation icons but positioned to the left */}
        {type === "numeric" &&
          !isEmpty &&
          showValidationIcons &&
          valid &&
          !rightIconType && (
            <VFIconComponent
              type="check"
              className={classes.rightIcon}
              color={theme.other["textbox-valid-color"]}
              dimensions={{ width: 16, height: 16 }}
            />
          )}
        {type === "numeric" &&
          !isEmpty &&
          showValidationIcons &&
          !valid &&
          !rightIconType && (
            <VFIconComponent
              type="warning-new"
              className={classes.rightIcon}
              color={theme.other["textbox-invalid-color"]}
              dimensions={{ width: 16, height: 16 }}
            />
          )}

        {/* Numeric controls */}
        {type === "numeric" && showNumericControls && (
          <div className={classes.numericControls}>
            <VFButton
              className={classes.numericButton}
              size="small"
              variant="secondary"
              buttonDisplay="onlyIcon"
              iconName="arrow-up"
              onClick={handleNumericIncrement}
              disabled={
                disabled ||
                (max !== undefined && parseFloat(value || "0") >= max)
              }
            />
            <VFButton
              className={classes.numericButton}
              size="small"
              variant="secondary"
              buttonDisplay="onlyIcon"
              iconName="arrow-down"
              onClick={handleNumericDecrement}
              disabled={
                disabled ||
                (min !== undefined && parseFloat(value || "0") <= min)
              }
            />
          </div>
        )}
      </div>
      {!valid && errorMessage && (
        <div className={`${classes.additionalText} ${classes.errorText}`}>
          {errorMessage}
        </div>
      )}
      {valid && hintMessage && (
        <div className={`${classes.additionalText} ${classes.hintText}`}>
          {hintMessage}
        </div>
      )}
    </div>
  );
};

// Usage example:
export const TextboxExample: React.FC = () => {
  const [value, setValue] = React.useState("");
  const [valid, setValid] = React.useState(true);
  const [numericValue, setNumericValue] = React.useState("0");
  const [searchValue, setSearchValue] = React.useState("");
  const [passwordValue, setPasswordValue] = React.useState("");

  return (
    <div>
      <VFTextbox
        placeholder="Placeholder"
        leftIconType="bell"
        rightIconType="bell"
        onChange={(value) => {
          setValue(value);
          setValid(!(value.length > 3));
        }}
        value={value}
        hintMessage="Hint text here"
        valid={valid}
        errorMessage="This field is required"
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        placeholder="Placeholder with validation icons"
        leftIconType="bell"
        rightIconType="bell"
        onChange={(value) => {
          setValue(value);
          setValid(!(value.length > 3));
        }}
        value={value}
        hintMessage="Hint text here"
        valid={valid}
        errorMessage="This field is required"
        showValidationIcons={true}
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        placeholder="Text with no right icon"
        leftIconType="bell"
        value={value}
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        placeholder="Search text"
        rightIconType="search"
        value={searchValue}
        onChange={setSearchValue}
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        placeholder="Text"
        disabled
        leftIconType="bell"
        rightIconType="bell"
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        type="numeric"
        placeholder="Enter a number"
        value={numericValue}
        onChange={setNumericValue}
        min={0}
        max={100}
        step={1}
        hintMessage="Hint text here"
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        type="numeric"
        placeholder="Enter a number"
        value={numericValue}
        onChange={setNumericValue}
        min={0}
        max={100}
        step={1}
        valid={false}
        errorMessage="Number must be between 0 and 100"
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        type="numeric"
        placeholder="Enter a number with validation icons"
        value={numericValue}
        onChange={setNumericValue}
        min={0}
        max={100}
        step={1}
        valid={false}
        errorMessage="Number must be between 0 and 100"
        showValidationIcons={true}
      />
      <div style={{ height: "20px" }} />
      <VFTextbox
        type="numeric"
        placeholder="Enter a number without controls"
        value={numericValue}
        onChange={setNumericValue}
        min={0}
        max={100}
        step={1}
        showNumericControls={false}
      />
      <div style={{ height: "20px" }} />
      {/* Password textbox example */}
      <VFTextbox
        type="password"
        placeholder="Enter your password"
        value={passwordValue}
        onChange={setPasswordValue}
        hintMessage="Password must be at least 8 characters"
      />
    </div>
  );
};
