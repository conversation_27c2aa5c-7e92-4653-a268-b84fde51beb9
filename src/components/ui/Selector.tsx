import { createStyles, useMantineTheme } from "@mantine/core";
import React, { useEffect, useRef, useState } from "react";
import VFIconComponent from "../icon/vf-icon";
import { VFButton } from "./Button";

interface Option {
  value: string;
  label: string;
  disabled?: boolean;
}

type VFSelectorSize = "small" | "default";

interface VFSelectorProps {
  options: Option[];
  isOpen: boolean;
  size?: VFSelectorSize;
  onChange?: (value: string | null) => void;
  value?: string | null;
  onClose?: () => void;
  disabled?: boolean;
  maxWidth?: number | string;
  position?: "left" | "center" | "right";
  deselectable?: boolean;
}

const useStyles = createStyles((theme, { size }: Partial<VFSelectorProps>) => ({
  root: {
    position: "absolute",
    width: "100%",
    minWidth: "158px",
    fontFamily: "SF Pro Display, sans-serif",
  },

  dropdownContainer: {
    position: "absolute",
    width: "100%",
    padding: "5px",
    backgroundColor: theme.other["selector-bg-color"],
    border: `1px solid ${theme.other["menu-border-color"]}`,
    borderRadius: "4px",
    zIndex: 9999,
    opacity: 0,
    transform: "scale(0.98)",
    transition: "opacity 0.2s ease, transform 0.2s ease",
    visibility: "hidden",
    overflow: "hidden",
    // Calculate max height based on option size (showing 6.5 options to indicate scrollability)
    maxHeight:
      size === "default"
        ? `${6.2 * 36 + 10}px` // Show 6.5 options to hint there's more content
        : `${6.2 * 24 + 10}px`, // Show 6.5 options in small size
    overflowY: "auto",

    "&[data-opened='true']": {
      opacity: 1,
      transform: "scale(1)",
      visibility: "visible",
    },

    "&[data-position='left']": {
      left: 0,
    },

    "&[data-position='center']": {
      left: "50%",
      transform: "translateX(-50%) scale(0.98)",
      "&[data-opened='true']": {
        transform: "translateX(-50%) scale(1)",
      },
    },

    "&[data-position='right']": {
      right: 0,
    },
  },

  option: {
    padding: size === "default" ? "8px 16px" : "4px 8px",
    borderRadius: "4px",
    cursor: "pointer",
    color: theme.other["selector-selector-item-text-color"],
    backgroundColor: theme.other["selector-bg-color"],
    position: "relative",
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",

    "&:hover:not([data-disabled='true']):not([data-selected='true'])": {
      backgroundColor: theme.other["selector-selector-item-hover-bg-color"],
    },

    "&:focus:not([data-disabled='true'])": {
      outline: "none",
      boxShadow: `inset 0 0 0 1px ${theme.other["selector-selector-item-focus-border-color"]}`,
    },

    "&[data-selected='true']": {
      backgroundColor: theme.other["selector-selector-item-selected-bg-color"],
      color: theme.other["selector-selector-item-selected-text-color"],
    },

    "&[data-disabled='true']": {
      cursor: "not-allowed",
      color: theme.other["selector-selector-item-disabled-text-color"],
      backgroundColor: theme.other["selector-bg-color"],
      pointerEvents: "none",
    },
  },

  checkIcon: {
    display: "flex",
    alignItems: "center",
    marginLeft: "8px",
  },
}));

export const VFSelector: React.FC<VFSelectorProps> = ({
  options,
  isOpen,
  size = "default",
  value,
  onChange,
  onClose,
  maxWidth,
  position = "right",
  deselectable = false, // Default to non-deselectable behavior
}) => {
  const { classes, cx } = useStyles({ size });
  const [dropdownPlacement, setDropdownPlacement] = useState<"top" | "bottom">(
    "bottom"
  );
  const rootRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const theme = useMantineTheme();

  useEffect(() => {
    if (isOpen && dropdownRef.current && rootRef.current) {
      const rootRect = rootRef.current.getBoundingClientRect();
      const dropdownHeight = dropdownRef.current.scrollHeight;
      const spaceBelow = window.innerHeight - rootRect.bottom;
      const spaceAbove = rootRect.top;

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPlacement("top");
      } else {
        setDropdownPlacement("bottom");
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (rootRef.current && !rootRef.current.contains(event.target as Node)) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleKeyDown = (event: React.KeyboardEvent, optionValue: string) => {
    switch (event.key) {
      case "Enter":
      case " ":
        event.preventDefault();
        if (!options.find((opt) => opt.value === optionValue)?.disabled) {
          // Handle deselection on keyboard events
          if (deselectable && optionValue === value) {
            onChange?.(null);
          } else {
            onChange?.(optionValue);
          }
          onClose?.();
        }
        break;
      case "Escape":
        event.preventDefault();
        onClose?.();
        break;
      case "ArrowDown":
        event.preventDefault();
        const nextElement = (event.target as HTMLElement).nextElementSibling;
        if (nextElement instanceof HTMLElement) {
          nextElement.focus();
        }
        break;
      case "ArrowUp":
        event.preventDefault();
        const prevElement = (event.target as HTMLElement)
          .previousElementSibling;
        if (prevElement instanceof HTMLElement) {
          prevElement.focus();
        }
        break;
    }
  };

  const dropdownStyle = {
    ...(dropdownPlacement === "top"
      ? { bottom: "100%", marginBottom: "5px" }
      : { top: "100%", marginTop: "5px" }),
  };

  const rootStyle = {
    maxWidth: maxWidth,
  };

  const optionClasses = cx(
    classes.option,
    size === "default" ? "p-heavy" : "small-p-heavy"
  );

  return (
    <div className={classes.root} ref={rootRef} style={rootStyle}>
      <div
        className={classes.dropdownContainer}
        ref={dropdownRef}
        data-opened={isOpen}
        data-position={position}
        style={dropdownStyle}
        role="listbox"
      >
        {options.map((option) => (
          <div
            key={option.value}
            className={optionClasses}
            onClick={() => {
              if (!option.disabled) {
                // Handle deselection if the clicked option is already selected and deselectable is true
                if (deselectable && option.value === value) {
                  onChange?.(null);
                } else {
                  onChange?.(option.value);
                }
                onClose?.();
              }
            }}
            onKeyDown={(e) => handleKeyDown(e, option.value)}
            data-selected={option.value === value}
            data-disabled={option.disabled}
            role="option"
            aria-selected={option.value === value}
            tabIndex={0}
          >
            {option.label}
            {option.value === value && (
              <span className={classes.checkIcon}>
                <VFIconComponent
                  type="check"
                  color={
                    theme.other["selector-selector-item-selected-text-color"]
                  }
                  size={size === "default" ? 16 : 12}
                />
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Example usage with VFButton showing both deselectable and non-deselectable cases
export const SelectorExample: React.FC = () => {
  // Non-deselectable selector
  const [selector1Open, setSelector1Open] = useState(false);
  const [selector1Value, setSelector1Value] = useState<string>("option1"); // Default value

  // Deselectable selector
  const [selector2Open, setSelector2Open] = useState(false);
  const [selector2Value, setSelector2Value] = useState<string | null>(
    "option1"
  ); // Can be null

  // Another deselectable selector
  const [selector3Open, setSelector3Open] = useState(false);
  const [selector3Value, setSelector3Value] = useState<string | null>(null); // Starts with no selection

  const options: Option[] = [
    { value: "option1", label: "Option 1" },
    { value: "option2", label: "Option 2" },
    { value: "option3", label: "Option 3", disabled: true },
    { value: "option4", label: "Option 4" },
  ];

  const getSelectedLabel = (value: string | null) => {
    return (
      options.find((opt) => opt.value === value)?.label || "Select an option"
    );
  };

  return (
    <div
      style={{
        padding: "20px",
        display: "flex",
        gap: "20px",
        flexDirection: "column",
      }}
    >
      {/* Example 1: Non-deselectable selector (acts like radio buttons) */}
      <div style={{ position: "relative", display: "inline-block" }}>
        <VFButton
          variant="secondary"
          label={getSelectedLabel(selector1Value)}
          onClick={() => setSelector1Open(!selector1Open)}
          buttonDisplay="withIcon"
          iconName="bell"
        />
        <VFSelector
          options={options}
          value={selector1Value}
          onChange={(val) => setSelector1Value(val || "option1")} // Ensure it always has a value
          isOpen={selector1Open}
          onClose={() => setSelector1Open(false)}
          position="left"
          maxWidth={158}
          size="small"
          deselectable={false} // Not deselectable
        />
      </div>

      {/* Example 2: Deselectable selector with initial value */}
      <div style={{ position: "relative", display: "inline-block" }}>
        <VFButton
          variant="primary"
          label={getSelectedLabel(selector2Value)}
          onClick={() => setSelector2Open(!selector2Open)}
        />
        <VFSelector
          options={options}
          value={selector2Value}
          onChange={setSelector2Value}
          isOpen={selector2Open}
          onClose={() => setSelector2Open(false)}
          position="center"
          maxWidth={250}
          deselectable={true} // Can be deselected
        />
      </div>

      {/* Example 3: Deselectable selector that starts with no selection */}
      <div style={{ position: "relative", display: "inline-block" }}>
        <VFButton
          variant="destructive"
          buttonDisplay="onlyIcon"
          iconName="more"
          onClick={() => setSelector3Open(!selector3Open)}
        />
        <VFSelector
          options={options}
          value={selector3Value}
          onChange={setSelector3Value}
          isOpen={selector3Open}
          onClose={() => setSelector3Open(false)}
          position="right"
          maxWidth={180}
          deselectable={true} // Can be deselected
        />
      </div>
    </div>
  );
};
