import {
  Checkbox,
  CheckboxProps,
  createStyles,
  useMantineTheme,
} from "@mantine/core";
import React, { forwardRef } from "react";
import VFIconComponent from "../icon/vf-icon";

export interface VFCheckboxProps extends Omit<CheckboxProps, "onChange"> {
  /**
   * Checkbox label
   */
  label?: string;
  /**
   * Whether the checkbox is checked
   */
  checked?: boolean;
  /**
   * Function to call when checkbox state changes
   */
  onChange?: (checked: boolean) => void;
  /**
   * Whether the checkbox should be disabled
   */
  disabled?: boolean;
  /**
   * Error message to display when the checkbox is invalid
   */
  error?: string;
  /**
   * Additional information about the checkbox
   */
  description?: string;
  /**
   * Whether the checkbox is in an invalid state
   */
  invalid?: boolean;
  /**
   * Whether the checkbox is in an indeterminate state
   */
  indeterminate?: boolean;
  /**
   * Additional className to apply to the checkbox
   */
  className?: string;
  /**
   * Required attribute for the input
   */
  required?: boolean;
  /**
   * Show asterisk (required indicator) even if not required
   */
  withAsterisk?: boolean;
  /**
   * Position of the label
   */
  labelPosition?: "right" | "left";
  /**
   * Value attribute for use with CheckboxGroup
   */
  value?: string;
}

const useStyles = createStyles(
  (
    theme,
    {
      disabled,
      invalid,
    }: {
      checked?: boolean;
      disabled?: boolean;
      invalid?: boolean;
      indeterminate?: boolean;
    }
  ) => ({
    root: {
      position: "relative",
    },
    body: {
      display: "flex",
      alignItems: "center",
    },
    label: {
      cursor: disabled ? "not-allowed" : "pointer",
      color: disabled
        ? theme.other["checkbox-disabled-color"]
        : theme.other["checkbox-label-color"],
      fontFamily: "SF Pro Display, sans-serif",
      fontSize: "16px",
      lineHeight: "19px",
      fontWeight: 400,
      userSelect: "none",
    },
    description: {
      fontSize: "12px",
      lineHeight: "14px",
      color: theme.other["textbox-placeholder-text-color"],
      marginTop: "4px",
    },
    error: {
      fontSize: "12px",
      lineHeight: "14px",
      color: theme.other["checkbox-invalid-color"],
      marginTop: "4px",
    },
    inner: {
      width: "16px",
      height: "16px",
      position: "relative",
    },
    input: {
      width: "16px",
      height: "16px",
      borderRadius: "4px",
      cursor: disabled ? "not-allowed" : "pointer",
      backgroundColor: theme.other["checkbox-bg-color-false"],
      border: `1px solid ${
        invalid
          ? theme.other["checkbox-invalid-color"]
          : theme.other["checkbox-border-color"]
      }`,
      "&:checked": {
        backgroundColor: invalid
          ? theme.other["checkbox-invalid-color"]
          : theme.other["checkbox-bg-color-true"],
        border: "none",
      },
      "&:disabled": {
        backgroundColor: theme.other["checkbox-bg-color-false"],
        borderColor: theme.other["checkbox-disabled-color"],
        "&:checked": {
          backgroundColor: theme.other["checkbox-disabled-color"],
        },
      },
      "&:not(:disabled):hover": {
        borderColor: theme.other["checkbox-hover-color"],
      },
      "&:focus": {
        borderColor: theme.other["checkbox-focus-color"],
      },
    },
  })
);

export const VFCheckbox = forwardRef<HTMLInputElement, VFCheckboxProps>(
  (
    {
      label,
      checked = false,
      onChange,
      disabled = false,
      error,
      description,
      invalid = false,
      indeterminate = false,
      className,
      required = false,
      withAsterisk = false,
      labelPosition = "right",
      value,
      ...props
    },
    ref
  ) => {
    const { classes, cx } = useStyles({
      checked,
      disabled,
      invalid,
      indeterminate,
    });

    const CheckboxIcon: CheckboxProps["icon"] = ({
      indeterminate,
      className,
    }) =>
      indeterminate ? (
        <VFIconComponent
          color={theme.other["checkbox-bg-color-false"]}
          size={10}
          type="check"
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            pointerEvents: "none",
          }}
        />
      ) : (
        <VFIconComponent
          color={theme.other["checkbox-bg-color-false"]}
          size={10}
          type="check"
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            pointerEvents: "none",
          }}
        />
      );

    const theme = useMantineTheme();

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      if (!disabled && onChange) {
        onChange(event.currentTarget.checked);
      }
    };

    // If withAsterisk is true, we need to modify the label to include an asterisk
    const finalLabel =
      withAsterisk && label ? (
        <span>
          {label}
          <span style={{ color: theme.other["checkbox-invalid-color"] }}>
            *
          </span>
        </span>
      ) : (
        label
      );

    return (
      <div className={cx(classes.root, className)}>
        <Checkbox
          ref={ref}
          icon={CheckboxIcon}
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          label={finalLabel}
          description={description}
          error={error || (invalid && " ")}
          indeterminate={indeterminate}
          required={required}
          labelPosition={labelPosition}
          value={value}
          size="xs"
          classNames={{
            body: classes.body,
            input: classes.input,
            label: classes.label,
            description: classes.description,
            error: classes.error,
            inner: classes.inner,
          }}
          {...props}
        />
      </div>
    );
  }
);

VFCheckbox.displayName = "VFCheckbox";

// Checkbox Group component
export interface VFCheckboxGroupProps {
  /**
   * Label for the checkbox group
   */
  label?: string;
  /**
   * Description for the checkbox group
   */
  description?: string;
  /**
   * Error message to display
   */
  error?: string;
  /**
   * Initial value for uncontrolled component
   */
  defaultValue?: string[];
  /**
   * Current value for controlled component
   */
  value?: string[];
  /**
   * Function to call when value changes
   */
  onChange?: (value: string[]) => void;
  /**
   * Whether the checkbox group is required
   */
  required?: boolean;
  /**
   * Show asterisk even if not required
   */
  withAsterisk?: boolean;
  /**
   * Orientation of the checkboxes
   */
  orientation?: "horizontal" | "vertical";
  /**
   * Spacing between checkboxes
   */
  spacing?: "xs" | "sm" | "md" | "lg" | "xl" | number;
  /**
   * Space between label and inputs
   */
  offset?: "xs" | "sm" | "md" | "lg" | "xl" | number;
  /**
   * Size of all child checkboxes
   */
  size?: "xs" | "sm" | "md" | "lg" | "xl";
  /**
   * Children - should be VFCheckbox components
   */
  children: React.ReactNode;
  /**
   * Additional className
   */
  className?: string;
}

export const VFCheckboxGroup: React.FC<VFCheckboxGroupProps> = ({
  label,
  description,
  error,
  defaultValue,
  value,
  onChange,
  required,
  withAsterisk,
  orientation = "vertical",
  spacing,
  offset,
  size,
  children,
  className,
  ...props
}) => {
  const theme = useMantineTheme();

  // If withAsterisk is true, we need to modify the label to include an asterisk
  const finalLabel =
    withAsterisk && label ? (
      <span>
        {label}
        <span style={{ color: theme.other["checkbox-invalid-color"] }}>*</span>
      </span>
    ) : (
      label
    );

  return (
    <Checkbox.Group
      label={finalLabel}
      description={description}
      error={error}
      defaultValue={defaultValue}
      value={value}
      onChange={onChange}
      required={required}
      orientation={orientation}
      spacing={spacing}
      offset={offset}
      size={size}
      className={className}
      {...props}
    >
      {children}
    </Checkbox.Group>
  );
};

// Example component showing usage
export const CheckboxExample: React.FC = () => {
  const [checked, setChecked] = React.useState(false);
  const [groupValue, setGroupValue] = React.useState<string[]>([]);

  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "20px" }}>
      <h3>Basic Checkbox Examples</h3>

      {/* Default checkbox */}
      <VFCheckbox
        label="Default checkbox"
        checked={checked}
        onChange={setChecked}
      />

      {/* Checked checkbox */}
      <VFCheckbox label="Checked checkbox" checked={true} />

      {/* Disabled checkbox */}
      <VFCheckbox label="Disabled checkbox" disabled />

      {/* Disabled checked checkbox */}
      <VFCheckbox label="Disabled checked checkbox" checked disabled />

      {/* Invalid checkbox */}
      <VFCheckbox
        label="Invalid checkbox"
        invalid
        error="This checkbox is required"
      />

      {/* Indeterminate checkbox */}
      <VFCheckbox label="Indeterminate checkbox" indeterminate />

      {/* With description */}
      <VFCheckbox
        label="Checkbox with description"
        description="This is a description of the checkbox"
      />

      {/* Required checkbox */}
      <VFCheckbox label="Required checkbox" required withAsterisk />

      {/* Label position left */}
      <VFCheckbox label="Label on the left" labelPosition="left" />

      <h3>Checkbox Group Example</h3>

      <VFCheckboxGroup
        label="Select your favorite frameworks"
        description="You can select multiple options"
        value={groupValue}
        onChange={setGroupValue}
        withAsterisk
      >
        <VFCheckbox value="react" label="React" />
        <VFCheckbox value="vue" label="Vue" />
        <VFCheckbox value="angular" label="Angular" />
        <VFCheckbox value="svelte" label="Svelte" />
      </VFCheckboxGroup>

      <VFCheckboxGroup
        label="Horizontal checkboxes"
        orientation="horizontal"
        spacing="lg"
      >
        <VFCheckbox value="option1" label="Option 1" />
        <VFCheckbox value="option2" label="Option 2" />
        <VFCheckbox value="option3" label="Option 3" />
      </VFCheckboxGroup>
    </div>
  );
};
