import { createStyles, useMantineTheme } from "@mantine/core";
import { IconType } from "../icons/types";
import VFIconComponent from "../icon/vf-icon";
import { useEffect, useRef, useState } from "react";

type MenuPosition = "left" | "center" | "right";

type MenuSize = "small" | "default";

export interface MenuItemProps {
  label: string;
  isDestructive?: boolean;
  leftIcon?: IconType;
  rightIcon?: IconType;
  onClick?: () => void;
  disabled?: boolean;
}

interface VFMenuProps {
  items: MenuItemProps[];
  isOpen: boolean;
  size?: MenuSize;
  width?: number;
  position?: MenuPosition;
  showLastItemBorder?: boolean;
  onClose?: () => void;
}

const useStyles = createStyles(
  (theme, { width, size }: Partial<VFMenuProps>) => ({
    menuContainer: {
      position: "absolute",
      minWidth: width,
      backgroundColor: theme.other["surface-surface-bg-color"],
      border: `1px solid ${theme.other["menu-border-color"]}`,
      borderRadius: "4px",
      zIndex: 9999,
      opacity: 0,
      transform: "scale(0.98)",
      transition: "opacity 0.2s ease, transform 0.2s ease",
      visibility: "hidden",
      overflow: "hidden",

      "&[data-opened='true']": {
        opacity: 1,
        transform: "scale(1)",
        visibility: "visible",
      },

      "&[data-position='left']": {
        left: 0,
      },

      "&[data-position='center']": {
        left: "50%",
        transform: "translateX(-50%) scale(0.98)",
        "&[data-opened='true']": {
          transform: "translateX(-50%) scale(1)",
        },
      },

      "&[data-position='right']": {
        right: 0,
      },
    },

    menuItem: {
      display: "flex",
      alignItems: "center",
      padding: size === "default" ? "8px 16px" : "4px 8px",
      cursor: "pointer",
      position: "relative",
      color: theme.other["menu-menu-item-text-color"],
      background: theme.other["surface-bg-color"],
      fontSize: "16px",
      lineHeight: "20px",
      fontWeight: 500,
      boxSizing: "border-box",
      outline: "none",

      "&:hover:not([data-disabled='true'])": {
        backgroundColor: theme.other["menu-border-color"],
      },

      "&:focus:not([data-disabled='true'])": {
        outline: "none",
        boxShadow: `inset 0 0 0 1px ${theme.other["menu-menu-item-focus-border-color"]}`,
      },

      "&[data-disabled='true'], &[data-disabled='true']:hover, &[data-disabled='true']:active":
        {
          cursor: "not-allowed",
          color: theme.other["menu-menu-item-disabled-text-color"],
          pointerEvents: "none",
          background: theme.other["surface-bg-color"],
        },

      "&[data-destructive='true']": {
        color: theme.other["menu-menu-item-error-color"],
      },

      "&[data-with-border='true']": {
        borderTop: `1px solid ${theme.other["menu-border-color"]}`,

        "&:focus:not([data-disabled='true'])": {
          borderTop: `1px solid ${theme.other["menu-menu-item-focus-border-color"]}`,
          boxShadow: `inset 1px 0 0 ${theme.other["menu-menu-item-focus-border-color"]}, inset -1px 0 0 ${theme.other["menu-menu-item-focus-border-color"]}, inset 0 -1px 0 ${theme.other["menu-menu-item-focus-border-color"]}`,
        },
      },
    },

    icon: {
      display: "flex",
      alignItems: "center",
    },

    leftIcon: {
      marginRight: "4px",
    },

    rightIcon: {
      marginLeft: "auto",
    },

    label: {
      flex: 1,
    },
  })
);

export const VFMenu: React.FC<VFMenuProps> = ({
  items,
  isOpen,
  width = 158,
  size = "default",
  position = "right",
  showLastItemBorder = true,
  onClose,
}) => {
  const { classes, cx } = useStyles({ width, size });
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPlacement, setMenuPlacement] = useState<"top" | "bottom">(
    "bottom"
  );
  const theme = useMantineTheme();

  useEffect(() => {
    if (isOpen && menuRef.current) {
      const rect = menuRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;

      if (spaceBelow < 10 && spaceAbove > rect.height) {
        setMenuPlacement("top");
      } else {
        setMenuPlacement("bottom");
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose?.();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  const menuStyle = {
    ...(menuPlacement === "top" ? { bottom: "100%" } : { top: "100%" }),
    marginTop: menuPlacement === "bottom" ? "5px" : undefined,
    marginBottom: menuPlacement === "top" ? "5px" : undefined,
  };

  const menuItemClasses = cx(
    classes.menuItem,
    size === "default" ? "p-heavy" : "small-p-heavy"
  );

  return (
    <div
      className={classes.menuContainer}
      ref={menuRef}
      data-opened={isOpen}
      data-position={position}
      style={menuStyle}
    >
      {items.map((item, index) => (
        <div
          key={`${item.label}-${index}`}
          className={menuItemClasses}
          onClick={
            !item.disabled
              ? (e) => {
                  e.stopPropagation();
                  item.onClick?.();
                }
              : undefined
          }
          data-disabled={item.disabled}
          data-destructive={item.isDestructive}
          data-with-border={showLastItemBorder && index === items.length - 1}
          tabIndex={item.disabled ? -1 : 0}
          role="menuitem"
          aria-disabled={item.disabled ? "true" : undefined}
        >
          {item.leftIcon && (
            <span className={`${classes.icon} ${classes.leftIcon}`}>
              <VFIconComponent
                type={item.leftIcon}
                color={
                  item.disabled
                    ? theme.other["menu-menu-item-disabled-text-color"]
                    : item.isDestructive
                    ? theme.other["menu-menu-item-error-color"]
                    : theme.other["menu-menu-item-text-color"]
                }
                size={size === "default" ? 16 : 12}
              />
            </span>
          )}
          <span className={classes.label}>{item.label}</span>
          {item.rightIcon && (
            <span className={`${classes.icon} ${classes.rightIcon}`}>
              <VFIconComponent
                type={item.rightIcon}
                color={
                  item.disabled
                    ? theme.other["menu-menu-item-disabled-text-color"]
                    : item.isDestructive
                    ? theme.other["menu-menu-item-error-color"]
                    : theme.other["menu-menu-item-text-color"]
                }
                dimensions={{ width: 16, height: 16 }}
              />
            </span>
          )}
        </div>
      ))}
    </div>
  );
};

// Usage example:
export const MenuExample: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const items: MenuItemProps[] = [
    {
      label: "Menu item 1",
      leftIcon: "locker",
      rightIcon: "locker",
      onClick: () => {},
    },
    {
      label: "Menu item 2",
      leftIcon: "locker",
      rightIcon: "locker",
      disabled: true,
    },
    {
      label: "Menu item 3",
      leftIcon: "locker",
      onClick: () => setIsOpen(false),
      rightIcon: "locker",
    },
  ];

  return (
    <div style={{ position: "relative", display: "inline-block" }}>
      <p
        style={{
          display: "inline-block",
          color: "#E1E1E1",
        }}
      >
        Some text over here
      </p>
      <VFIconComponent
        onClick={() => setIsOpen(!isOpen)}
        type="more"
        dimensions={{ width: 4, height: 16 }}
        style={{ cursor: "pointer" }}
      />
      <VFMenu
        items={items}
        isOpen={isOpen}
        width={224}
        position="left"
        onClose={() => setIsOpen(false)}
      />
    </div>
  );
};
