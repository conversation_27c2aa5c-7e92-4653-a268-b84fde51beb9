import {
  ColorPicker,
  createStyles,
  useMantineTheme,
  Portal,
} from "@mantine/core";
import React, { useState, useRef, useEffect, MutableRefObject } from "react";

export type ColorPickerState = "default" | "hover" | "focus" | "disabled";
export type ColorFormat = "hex" | "rgba" | "rgb" | "hsl" | "hsla";

// Add a simpler containerRef approach
interface ContainerRefData {
  containerElement: HTMLDivElement | null;
}

interface VFColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
  format?: ColorFormat;
  swatches?: string[];
  withAlpha?: boolean;
  size?: "sm" | "md" | "lg";
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  width?: string | number;
}

const useStyles = createStyles(
  (
    theme,
    { disabled, width }: { disabled: boolean; width?: string | number }
  ) => ({
    root: {
      position: "relative",
      width: width || "100%",
      fontFamily: "SF Pro Display, sans-serif",
    },

    inputWrapper: {
      position: "relative",
      width: "100%",
    },

    input: {
      height: "36px",
      backgroundColor: theme.other["textbox-bg-color"],
      border: `1px solid ${theme.other["textbox-border-color"]}`,
      borderRadius: "4px",
      color: `${theme.other["textbox-text-color"]} !important`,
      fontSize: "16px",
      lineHeight: "19px",
      fontFamily: "SF Pro Display, sans-serif",
      padding: "8px 12px",
      paddingRight: "46px",
      width: "100%",
      transition: "border-color 0.2s ease",

      "&::placeholder": {
        color: `${
          disabled
            ? theme.other["textbox-disabled-text-color"]
            : theme.other["textbox-placeholder-text-color"]
        } !important`,
      },

      "&:hover:not(:disabled)": {
        border: `1px solid ${theme.other["textbox-hover-border-color"]}`,
      },

      "&:focus": {
        outline: "none",
        border: `1px solid ${theme.other["textbox-focus-border-color"]}`,
      },

      "&:disabled": {
        border: `1px solid ${theme.other["textbox-border-color"]}`,
        backgroundColor: theme.other["textbox-bg-color"],
        opacity: 0.6,
        color: `${theme.other["textbox-disabled-text-color"]} !important`,
      },
    },

    pickerWrapper: {
      position: "absolute",
      zIndex: 100,
      backgroundColor: theme.other["color-picker-bg-color"],
      border: `1px solid ${theme.other["color-picker-border-color"]}`,
      borderRadius: "4px",
      padding: "12px",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
      width: "260px",
      marginTop: "4px",
    },

    pickerTop: {
      bottom: "calc(100% + 4px)",
    },

    pickerBottom: {
      top: "calc(100% + 4px)",
    },

    colorPicker: {
      "& .mantine-ColorPicker-wrapper": {
        width: "100%",
      },

      "& .mantine-ColorPicker-saturation": {
        borderRadius: "4px",
        height: "120px",
        boxShadow: "none",
        border: `1px solid ${theme.other["card-border-color"]}`,
        marginBottom: "8px",
      },

      "& .mantine-ColorPicker-sliders": {
        marginBottom: "8px",
      },
    },

    disabled: {
      opacity: 0.6,
      pointerEvents: "none",
    },

    colorPreview: {
      position: "absolute",
      top: "50%",
      transform: "translateY(-50%)",
      right: "1px",
      width: "34px",
      height: "34px",
      borderRadius: "4px",
      border: `1px solid ${theme.other["card-border-color"]}`,
      overflow: "hidden",
      pointerEvents: "none",
    },
  })
);

// Helper function to parse RGBA string and convert alpha to percentage
const parseRgba = (rgba: string): { hex: string; alpha: number } => {
  // Default values
  let hex = "#000000";
  let alpha = 1;

  try {
    // Extract RGBA values using regex
    const match = rgba.match(
      /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/
    );
    if (match) {
      const r = parseInt(match[1], 10);
      const g = parseInt(match[2], 10);
      const b = parseInt(match[3], 10);
      alpha = match[4] ? parseFloat(match[4]) : 1;

      // Convert RGB to HEX
      hex = `#${r.toString(16).padStart(2, "0")}${g
        .toString(16)
        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
    }
  } catch (error) {
    // Error parsing RGBA color
    return { hex: "#000000", alpha: 1 };
  }

  return { hex, alpha };
};

// Format color value according to spec: "hex | percentage | color preview"
const formatColorValue = (color: string, withAlpha: boolean): string => {
  if (!withAlpha) return color;

  try {
    const { hex, alpha } = parseRgba(color);
    const percentage = Math.round(alpha * 100);
    // Format with proper spacing for even distribution
    return `${hex}     |     ${percentage}%`;
  } catch (error) {
    return color;
  }
};

// This custom component replaces the plain input to properly style the formatted color value
const StyledColorInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement> & {
    displayValue: string;
    withAlpha: boolean;
    actualFormat: ColorFormat;
    theme: any;
    className: string;
    disabled: boolean;
    isFocused: boolean;
    isHovered: boolean;
    containerRef: React.RefObject<ContainerRefData>;
  }
>(
  (
    {
      displayValue,
      withAlpha,
      actualFormat,
      theme,
      className,
      disabled,
      isFocused,
      isHovered,
      containerRef,
      ...props
    },
    ref
  ) => {
    const isFormattedDisplay =
      withAlpha && (actualFormat === "rgba" || actualFormat === "hsla");

    const divRef = useRef<HTMLDivElement>(null);

    // Update the containerRef when the div is mounted or updated
    useEffect(() => {
      if (containerRef && containerRef.current && divRef.current) {
        containerRef.current.containerElement = divRef.current;
      }
    }, [containerRef, divRef.current]);

    if (!isFormattedDisplay) {
      return <input ref={ref} {...props} value={displayValue} />;
    }

    // Parse the display value to get parts for styled rendering
    let hexPart = "";
    let percentPart = "";

    try {
      const parts = displayValue.split("|");
      if (parts.length === 2) {
        hexPart = parts[0].trim();
        percentPart = parts[1].trim();
      }
    } catch (error) {
      return <input ref={ref} {...props} value={displayValue} />;
    }

    const textColor = theme.other["btn-secondary-text-color"];

    // Determine border color based on state
    let borderColor = theme.other["textbox-border-color"];
    if (isFocused) {
      borderColor = theme.other["textbox-focus-border-color"];
    } else if (isHovered && !disabled) {
      borderColor = theme.other["textbox-hover-border-color"];
    }

    // Create styled container to replace the input
    return (
      <div
        ref={divRef}
        className={className}
        style={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          height: "36px",
          backgroundColor: theme.other["textbox-bg-color"],
          border: `1px solid ${borderColor}`,
          borderRadius: "4px",
          color: `${theme.other["textbox-text-color"]} !important`,
          fontSize: "16px",
          lineHeight: "19px",
          fontFamily: "SF Pro Display, sans-serif",
          padding: "8px 12px",
          paddingRight: "46px",
          opacity: disabled ? 0.6 : 1,
          transition: "border-color 0.2s ease",
          cursor: disabled ? "not-allowed" : "text",
        }}
        onClick={props.onFocus as any}
      >
        <div
          style={{
            flex: 1,
            color: textColor,
            display: "flex",
            alignItems: "center",
            height: "100%",
            lineHeight: "normal",
            paddingBottom: "1px",
          }}
        >
          {hexPart}
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            minWidth: "60px", // Fixed width for percentage section
            justifyContent: "flex-start",
            height: "100%",
          }}
        >
          <div
            style={{
              color: "#B3B3B3",
              marginRight: "8px",
              display: "flex",
              alignItems: "center",
              height: "100%",
              lineHeight: "normal",
              paddingBottom: "1px", // Fine-tune vertical alignment
            }}
          >
            |
          </div>
          <div
            style={{
              color: textColor,
              minWidth: "40px", // Ensure space for "100%"
              display: "flex",
              alignItems: "center",
              height: "100%",
              lineHeight: "normal",
              paddingBottom: "1px",
            }}
          >
            {percentPart}
          </div>
        </div>
        <input ref={ref} {...props} style={{ display: "none" }} />
      </div>
    );
  }
);

export const VFColorPicker: React.FC<VFColorPickerProps> = ({
  value = "#ffffff",
  onChange,
  format = "hex",
  swatches = [
    "#ffffff",
    "#000000",
    "#FFE100",
    "#00ff00",
    "#00ffff",
    "#0000ff",
    "#ff00ff",
    "#ff0000",
  ],
  withAlpha = false,
  size = "sm",
  disabled = false,
  placeholder = "Pick a color",
  className = "",
  width,
}) => {
  const [color, setColor] = useState(value);
  const [displayValue, setDisplayValue] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [pickerPosition, setPickerPosition] = useState<"top" | "bottom">(
    "bottom"
  );
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<ContainerRefData>({ containerElement: null });
  const pickerRef = useRef<HTMLDivElement>(null);
  const { classes, cx } = useStyles({ disabled, width });
  const theme = useMantineTheme();
  const [dropdownPosition, setDropdownPosition] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  // Determine the actual format based on withAlpha prop
  const actualFormat = withAlpha
    ? format === "hex"
      ? ("rgba" as ColorFormat)
      : format.includes("a")
      ? format
      : (`${format}a` as ColorFormat)
    : format === "rgba"
    ? ("rgb" as ColorFormat)
    : format === "hsla"
    ? ("hsl" as ColorFormat)
    : format;

  // Determine if we're using the formatted display style
  const isFormattedDisplay =
    withAlpha && (actualFormat === "rgba" || actualFormat === "hsla");

  // Update display value when color changes
  useEffect(() => {
    if (withAlpha && (actualFormat === "rgba" || actualFormat === "hsla")) {
      setDisplayValue(formatColorValue(color, withAlpha));
    } else {
      setDisplayValue(color);
    }
  }, [color, withAlpha, actualFormat]);

  // Add this effect to sync color state with value prop
  useEffect(() => {
    setColor(value);
  }, [value]);

  // Initialize display value
  useEffect(() => {
    if (withAlpha && (actualFormat === "rgba" || actualFormat === "hsla")) {
      setDisplayValue(formatColorValue(value, withAlpha));
    } else {
      setDisplayValue(value);
    }
  }, [value, withAlpha, actualFormat]);

  const handleColorChange = (newColor: string) => {
    setColor(newColor);
    if (onChange) {
      onChange(newColor);
    }
  };

  // Check if picker should be positioned above or below input
  useEffect(() => {
    if (isFocused && inputRef.current && pickerRef.current) {
      const inputRect = inputRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - inputRect.bottom;
      const pickerHeight = pickerRef.current.offsetHeight;

      if (spaceBelow < pickerHeight && inputRect.top > pickerHeight) {
        setPickerPosition("top");
      } else {
        setPickerPosition("bottom");
      }
    }
  }, [isFocused, inputRef.current, pickerRef.current]);

  // Calculate dropdown position when focused
  useEffect(() => {
    if (isFocused) {
      let rect;

      // If we're using the styled input, use the container element
      if (
        isFormattedDisplay &&
        containerRef.current &&
        containerRef.current.containerElement
      ) {
        rect = containerRef.current.containerElement.getBoundingClientRect();
      } else if (inputRef.current) {
        // Otherwise use the standard input element
        rect = inputRef.current.getBoundingClientRect();
      } else {
        // If neither are available, bail out
        return;
      }

      setDropdownPosition({
        top: rect.bottom + window.scrollY + 4, // 4px margin
        left: rect.left + window.scrollX,
        width: rect.width,
      });
    } else {
      setDropdownPosition(null);
    }
  }, [isFocused, isFormattedDisplay]);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if clicked element is outside both the input/container and the picker
      const isOutsideInput =
        isFormattedDisplay && containerRef.current?.containerElement
          ? !containerRef.current.containerElement.contains(target)
          : inputRef.current
          ? !inputRef.current.contains(target)
          : true;

      const isOutsidePicker = pickerRef.current
        ? !pickerRef.current.contains(target)
        : true;

      if (isOutsideInput && isOutsidePicker) {
        setIsFocused(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isFormattedDisplay]);

  // Event handlers for hover state
  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  return (
    <div
      className={cx(classes.root, className, { [classes.disabled]: disabled })}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className={classes.inputWrapper}>
        {isFormattedDisplay ? (
          <StyledColorInput
            ref={inputRef}
            type="text"
            className={classes.input}
            displayValue={displayValue}
            withAlpha={withAlpha}
            actualFormat={actualFormat}
            theme={theme}
            containerRef={containerRef}
            onChange={(e) => handleColorChange(e.target.value)}
            onFocus={() => !disabled && setIsFocused(true)}
            placeholder={placeholder}
            disabled={disabled}
            isFocused={isFocused}
            isHovered={isHovered}
            readOnly={true}
          />
        ) : (
          <input
            ref={inputRef}
            type="text"
            className={classes.input}
            value={displayValue}
            onChange={(e) => handleColorChange(e.target.value)}
            onFocus={() => !disabled && setIsFocused(true)}
            placeholder={placeholder}
            disabled={disabled}
          />
        )}
        <div
          className={classes.colorPreview}
          style={{ backgroundColor: color }}
        />
      </div>

      {/* Render dropdown in a Portal at the calculated position */}
      {isFocused && !disabled && dropdownPosition && (
        <Portal>
          <div
            ref={pickerRef}
            className={cx(classes.pickerWrapper)}
            style={{
              position: "absolute",
              top: dropdownPosition.top,
              left: dropdownPosition.left,
              width: dropdownPosition.width,
              zIndex: 1000,
            }}
          >
            <div className={classes.colorPicker}>
              <ColorPicker
                value={color}
                onChange={handleColorChange}
                format={actualFormat}
                swatches={swatches}
                swatchesPerRow={8}
                size={size}
              />
            </div>
          </div>
        </Portal>
      )}
    </div>
  );
};

// Example usage component to demonstrate the VFColorPicker
export const ColorPickerExample: React.FC = () => {
  const [color, setColor] = useState("#4caf50");
  const [rgbaColor, setRgbaColor] = useState("rgba(33, 150, 243, 0.75)");

  return (
    <div style={{ padding: "20px", maxWidth: "300px" }}>
      {/* Default state */}
      <div style={{ marginBottom: "50px" }}>
        <h3>Default ColorPicker</h3>
        <VFColorPicker value={color} onChange={setColor} />
      </div>

      {/* With Alpha */}
      <div style={{ marginBottom: "50px" }}>
        <h3>With Alpha</h3>
        <VFColorPicker value={color} onChange={setColor} withAlpha />
      </div>

      {/* RGBA with Hex | Percentage format */}
      <div style={{ marginBottom: "50px" }}>
        <h3>RGBA with Hex | Percentage format</h3>
        <VFColorPicker
          value={rgbaColor}
          onChange={setRgbaColor}
          format="rgba"
          withAlpha={true}
          width="200px"
        />
      </div>

      {/* Disabled state */}
      <div style={{ marginBottom: "20px" }}>
        <h3>Disabled</h3>
        <VFColorPicker value={color} onChange={setColor} disabled />
      </div>

      {/* Custom Width */}
      <div style={{ marginBottom: "20px" }}>
        <h3>Custom Width</h3>
        <VFColorPicker value={color} onChange={setColor} width="200px" />
      </div>
    </div>
  );
};
