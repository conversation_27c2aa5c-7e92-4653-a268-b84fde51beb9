import { createStyles, useMantineTheme } from "@mantine/core";
import React, { ReactNode, useState } from "react";
import { VFButton } from "./Button";
import { IconType } from "../icons/types";

// Table Props
interface VFTableProps {
  data: Array<Record<string, any>>;
  columns: VFTableColumn[];
  width?: number | string;
  height?: number | string;
  emptyStateMessage?: string;
  onRowClick?: (row: Record<string, any>, index: number) => void;
  selectable?: boolean;
  hoverable?: boolean;
  selectedRowId?: string | number | null;
  rowIdField?: string;
  showHeader?: boolean;
}

// Column definition
interface VFTableColumn {
  key: string;
  header: string;
  width?: number | string;
  render?: (value: any, row: Record<string, any>, index: number) => ReactNode;
}

// Styling
const useStyles = createStyles((theme) => ({
  tableContainer: {
    borderRadius: "4px",
    border: `1px solid ${theme.other["table-border-color"]}`,
    overflow: "auto",
  },
  table: {
    width: "100%",
    borderCollapse: "collapse",
  },
  headerRow: {
    borderBottom: `1px solid ${theme.other["table-border-color"]}`,
  },
  headerCell: {
    padding: "16px 16px",
    textTransform: "uppercase",
    color: theme.other["table-header-text-color"],
    fontSize: "16px",
    fontWeight: 600,
    fontFamily: "SF Pro Display, sans-serif",
    lineHeight: "1.25em",
    textAlign: "left",
  },
  row: {
    borderBottom: `1px solid ${theme.other["table-border-color"]}`,
    transition: "background-color 0.2s ease",
    "&:hover": {
      backgroundColor: theme.other["surface-bg-color"],
      cursor: "pointer",
    },
    "&:last-child": {
      borderBottom: "none",
    },
  },
  rowSelected: {
    backgroundColor: `${theme.other["surface-bg-color"]}`,
    borderLeft: `3px solid ${theme.other["btn-primary-bg-color"]}`,
  },
  cell: {
    padding: "16px 16px",
    color: theme.other["table-text-color"],

    textAlign: "left",
    verticalAlign: "middle",
  },
  actionButtons: {
    display: "flex",
    flexDirection: "row",
    gap: "8px",
    justifyContent: "flex-start",
  },
  emptyState: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: "24px",
    color: theme.other["table-text-color"],
    fontSize: "16px",
    fontFamily: "SF Pro Display, sans-serif",
    lineHeight: "1.5em",
  },
}));

// Main Table Component
export const VFTable: React.FC<VFTableProps> = ({
  data,
  columns,
  width,
  height,
  emptyStateMessage = "No data to display",
  onRowClick,
  selectable = false,
  hoverable = true,
  selectedRowId = null,
  rowIdField = "id",
  showHeader = true,
}) => {
  const { classes, cx } = useStyles();
  const theme = useMantineTheme();
  const [hoveredRowIndex, setHoveredRowIndex] = useState<number | null>(null);

  // Handle empty data state
  if (!data || data.length === 0) {
    return (
      <div
        className={classes.tableContainer}
        style={{
          width: typeof width === "number" ? `${width}px` : width || "100%",
          height: typeof height === "number" ? `${height}px` : height || "auto",
        }}
      >
        <div className={classes.emptyState}>{emptyStateMessage}</div>
      </div>
    );
  }

  // Calculate column widths
  const getColumnWidth = (column: VFTableColumn) => {
    if (column.width) {
      return typeof column.width === "number"
        ? `${column.width}px`
        : column.width;
    }
    return "auto";
  };

  return (
    <div
      className={classes.tableContainer}
      style={{
        width: typeof width === "number" ? `${width}px` : width || "100%",
        height: typeof height === "number" ? `${height}px` : height || "auto",
      }}
    >
      <table className={classes.table}>
        {showHeader && (
          <thead>
            <tr className={classes.headerRow}>
              {columns.map((column) => (
                <th
                  key={`header-${column.key}`}
                  className={classes.headerCell}
                  style={{ width: getColumnWidth(column) }}
                >
                  {column.header}
                </th>
              ))}
            </tr>
          </thead>
        )}
        <tbody>
          {data.map((row, rowIndex) => {
            const isSelected =
              selectable &&
              selectedRowId !== null &&
              row[rowIdField] === selectedRowId;
            const isHovered = hoverable && hoveredRowIndex === rowIndex;

            return (
              <tr
                key={`row-${rowIndex}`}
                className={cx(classes.row, isSelected && classes.rowSelected)}
                onClick={() => onRowClick?.(row, rowIndex)}
                onMouseEnter={() => setHoveredRowIndex(rowIndex)}
                onMouseLeave={() => setHoveredRowIndex(null)}
              >
                {columns.map((column) => (
                  <td
                    key={`cell-${rowIndex}-${column.key}`}
                    className={classes.cell}
                    style={{
                      width: getColumnWidth(column),
                      paddingLeft:
                        isSelected && column === columns[0] ? "13px" : "16px",
                    }}
                  >
                    <p className="p-heavy">
                      {column.render
                        ? column.render(row[column.key], row, rowIndex)
                        : row[column.key]}
                    </p>
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
};

// Usage example
export const TableExample: React.FC = () => {
  const [selectedId, setSelectedId] = useState<number | null>(null);

  const exampleData = [
    { id: 1, name: "John Doe", email: "<EMAIL>", role: "Admin" },
    { id: 2, name: "Jane Smith", email: "<EMAIL>", role: "User" },
    { id: 3, name: "Bob Johnson", email: "<EMAIL>", role: "User" },
  ];

  const columns = [
    { key: "id", header: "ID", width: 100 },
    { key: "name", header: "Name", width: 200 },
    { key: "email", header: "Email", width: 250 },
    {
      key: "role",
      header: "Role",
      width: 150,
      render: (value: string) => (
        <span style={{ fontWeight: value === "Admin" ? 700 : 500 }}>
          {value}
        </span>
      ),
    },
  ];

  const handleRowClick = (row: Record<string, any>) => {
    setSelectedId(row.id === selectedId ? null : row.id);
  };

  // Brand presets data for the Figma example
  const brandPresetData = [
    { id: 1, name: "Custom Brand Preset 1" },
    { id: 2, name: "Custom Brand Preset 2" },
    { id: 3, name: "Custom Brand Preset 3" },
    { id: 4, name: "Custom Brand Preset 4" },
  ];

  // Columns for brand presets
  const brandPresetColumns = [
    {
      key: "name",
      header: "PRESET NAME",
      width: 300,
    },
    {
      key: "actions",
      header: "ACTIONS",
      width: 200,
      render: (value: any, row: Record<string, any>, index: number) => (
        <div
          className={useStyles().classes.actionButtons}
          onClick={(e) => e.stopPropagation()}
        >
          <VFButton
            variant="secondary"
            buttonDisplay="withIcon"
            iconName="edit"
            size="small"
            onClick={() => {}}
          >
            Edit
          </VFButton>
          <VFButton
            variant="secondary"
            buttonDisplay="withIcon"
            iconName="delete-outline"
            size="small"
            onClick={() => {}}
          >
            Delete
          </VFButton>
        </div>
      ),
    },
  ];

  return (
    <div>
      <h3>Table with selection</h3>
      <VFTable
        data={exampleData}
        columns={columns}
        onRowClick={handleRowClick}
        selectable={true}
        selectedRowId={selectedId}
      />

      <div style={{ height: "20px" }} />

      <h3>Table without hover effect</h3>
      <VFTable data={exampleData} columns={columns} hoverable={false} />

      <div style={{ height: "20px" }} />

      <h3>Empty state</h3>
      <VFTable data={[]} columns={columns} emptyStateMessage="No users found" />

      <div style={{ height: "40px" }} />

      <h3>Brand presets (Figma design example)</h3>
      <div style={{ maxWidth: "500px" }}>
        <div style={{ marginBottom: "8px" }}>
          <h4 style={{ margin: "0 0 8px 0" }}>Brand presets</h4>
          <p style={{ margin: 0, fontSize: "12px", color: "#B3B3B3" }}>
            Custom presets you've saved to your account can be selected while
            configuring video captions.
          </p>
        </div>
        <VFTable
          data={brandPresetData}
          columns={brandPresetColumns}
          hoverable={true}
        />
      </div>

      <div style={{ height: "40px" }} />

      <h3>Table without header</h3>
      <div style={{ maxWidth: "500px" }}>
        <VFTable
          data={brandPresetData}
          columns={brandPresetColumns}
          showHeader={false}
          hoverable={true}
        />
      </div>
    </div>
  );
};
