import React, { useCallback, useState, useRef } from "react";
import { createStyles, useMantineTheme } from "@mantine/core";
import { VFTextbox } from "./Textbox";
import { VFButton } from "./Button";
import {
  changePassword,
  updateUserData,
  deleteAccount,
} from "../../features/auth/authActions";
import { useDispatch } from "react-redux";
import { UpdateUserDataArgs } from "../../features/auth/types";
import { openModal, closeModal } from "../../features/modal/modalSlice";
import { ModalConfig, ModalType } from "../../features/modal/types";
import { useErrorHandler } from "../../hooks/useErrorHandler";
import { AppDispatch } from "../../store";
import { ErrorResponse } from "../../types";

interface ProfileDetailsFormProps {
  initialUsername?: string;
  initialEmail?: string;
  onUpdateSuccess?: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: "48px",
    padding: "0 32px",

    // Make container responsive
    [`@media (max-width: 992px)`]: {
      padding: "0 16px",
    },

    [`@media (max-width: 576px)`]: {
      padding: "0",
    },
  },
  formContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "48px",
    alignSelf: "stretch",
    padding: "16px 16px 32px",
    border: `1px solid ${theme.other["card-border-color"]}`,
    borderRadius: "4px",
    backgroundColor: theme.other["main-bg-color"],
  },
  title: {
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 600,
    fontSize: "22px",
    lineHeight: "1.19em",
    textAlign: "left",
    color: theme.other["surface-text-color"],
  },
  fieldsContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "32px",
    alignSelf: "stretch",
  },
  fieldRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    gap: "24px",
    alignItems: "center",

    // Make field rows responsive for tablet and mobile
    [`@media (max-width: 768px)`]: {
      flexDirection: "column",
      alignItems: "flex-start",
      gap: "16px",
    },
  },
  fieldColumn: {
    display: "flex",
    flexDirection: "column",
    gap: "4px",
    width: "auto",
    flexGrow: 1,

    // Make field columns responsive for tablet and mobile
    [`@media (max-width: 768px)`]: {
      width: "100%",
    },
  },
  fieldLabel: {
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 500,
    fontSize: "16px",
    lineHeight: "1.5em",
    color: theme.other["surface-text-color"],
  },
  passwordLabel: {
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 400,
    fontSize: "16px",
    lineHeight: "1.19em",
    color: theme.other["surface-text-color"],
  },
  divider: {
    height: "1px",
    width: "100%",
    backgroundColor: theme.other["card-border-color"],
  },
  placeholderField: {
    display: "flex",
    alignItems: "center",
    gap: "10px",
    padding: "8px 16px",
    width: "340px",
    height: "36px",
    backgroundColor: theme.other["textbox-bg-color"],
    border: `1px solid ${theme.other["textbox-border-color"]}`,
    borderRadius: "4px",
    color: theme.other["textbox-placeholder-text-color"],
    fontFamily: "SF Pro Display, sans-serif",
    fontWeight: 500,
    fontSize: "16px",
    lineHeight: "1.5em",

    // Make placeholder field responsive
    [`@media (max-width: 992px)`]: {
      width: "100%",
    },
  },
  inputWrapper: {
    width: "340px",

    // Make input wrapper responsive
    [`@media (max-width: 992px)`]: {
      width: "100%",
    },
  },
  passwordFieldsContainer: {
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    marginTop: "4px",
  },
  deleteButton: {
    alignSelf: "flex-start",
  },
  actionButton: {
    alignSelf: "flex-start",
    marginTop: "8px",

    // Center the action button on mobile
    [`@media (max-width: 768px)`]: {
      alignSelf: "flex-start",
      marginTop: "0",
    },
  },
}));

// DeleteAccountModalBody component for the confirmation modal
const DeleteAccountModalBody = React.memo(
  React.forwardRef(function DeleteAccountModalBody(
    props,
    ref: React.Ref<{ getValue: () => string }>
  ) {
    const [password, setPassword] = useState("");

    React.useImperativeHandle(
      ref,
      () => ({
        getValue: () => password,
      }),
      [password]
    );

    return (
      <VFTextbox
        type="password"
        placeholder="Enter your password to confirm"
        value={password}
        onChange={setPassword}
        width="100%"
      />
    );
  })
);

export const ProfileDetailsForm: React.FC<ProfileDetailsFormProps> = ({
  initialUsername = "",
  initialEmail = "",
  onUpdateSuccess,
}) => {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const { handleError } = useErrorHandler();

  // Field edit states
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [isEditingPassword, setIsEditingPassword] = useState(false);

  // Field values
  const [username, setUsername] = useState(initialUsername);
  const [email, setEmail] = useState(initialEmail);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const dispatch = useDispatch<AppDispatch>();

  const handleSavePassword = useCallback(async () => {
    setIsLoading(true);

    try {
      const resultAction = await dispatch(
        changePassword({
          old_password: currentPassword,
          new_password: newPassword,
        })
      );

      if (changePassword.fulfilled.match(resultAction)) {
        setIsEditingPassword(false);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        onUpdateSuccess?.();
      } else if (resultAction.payload) {
        const errorPayload = resultAction.payload as {
          error: ErrorResponse;
          statusCode: number;
        };
        handleError(errorPayload.error, errorPayload.statusCode);
      }
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
    } finally {
      setIsLoading(false);
    }
  }, [
    currentPassword,
    newPassword,
    confirmPassword,
    dispatch,
    onUpdateSuccess,
    handleError,
  ]);

  const handleDeleteAccount = useCallback(async () => {
    // Create a ref to access the password input value
    const modalBodyRef = React.createRef<{ getValue: () => string }>();

    // Create the modal body with the password input
    const modalBody: React.ReactNode = (
      <>
        <p
          style={{
            color: theme.other["surface-subtext-color"],
            marginBottom: "16px",
            fontSize: "14px",
            lineHeight: "20px",
          }}
        >
          This action cannot be undone. All your data, including projects,
          presets, and account information will be permanently deleted.
        </p>
        <DeleteAccountModalBody ref={modalBodyRef} />
      </>
    );

    // Configure the modal
    const modalConfig: ModalConfig = {
      type: ModalType.BASIC,
      title: "Delete Account",
      body: modalBody,
      actions: [
        {
          label: "Cancel",
          variant: "secondary",
          fullWidth: true,
          buttonDisplay: "onlyText",
          onClick: () => dispatch(closeModal()),
        },
        {
          label: "Delete Account",
          variant: "destructive",
          fullWidth: true,
          onClick: async () => {
            const password = modalBodyRef.current?.getValue() ?? "";

            if (password) {
              try {
                const resultAction = await dispatch(
                  deleteAccount({ password })
                );

                if (
                  !deleteAccount.fulfilled.match(resultAction) &&
                  resultAction.payload
                ) {
                  const errorPayload = resultAction.payload as {
                    error: ErrorResponse;
                    statusCode: number;
                  };
                  handleError(errorPayload.error, errorPayload.statusCode);
                }
              } catch (errorResponse: any) {
                handleError(errorResponse.error, errorResponse.statusCode);
              }
            }

            dispatch(closeModal());
          },
        },
      ],
    };

    // Open the modal
    dispatch(openModal(modalConfig));
  }, [dispatch, theme, handleError]);

  const handleSaveName = async () => {
    const updateUserDataPayload: UpdateUserDataArgs = {
      full_name: username,
    };

    try {
      const resultAction = await dispatch(
        updateUserData(updateUserDataPayload)
      );

      if (updateUserData.fulfilled.match(resultAction)) {
        setIsEditingName(false);
        onUpdateSuccess?.();
      } else if (resultAction.payload) {
        const errorPayload = resultAction.payload as {
          error: ErrorResponse;
          statusCode: number;
        };
        handleError(errorPayload.error, errorPayload.statusCode);
      }
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
    }
  };

  const handleSaveEmail = async () => {
    const updateUserDataPayload: UpdateUserDataArgs = {
      email: email,
    };

    try {
      const resultAction = await dispatch(
        updateUserData(updateUserDataPayload)
      );

      if (updateUserData.fulfilled.match(resultAction)) {
        setIsEditingEmail(false);
        onUpdateSuccess?.();
      } else if (resultAction.payload) {
        const errorPayload = resultAction.payload as {
          error: ErrorResponse;
          statusCode: number;
        };
        handleError(errorPayload.error, errorPayload.statusCode);
      }
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
    }
  };

  return (
    <div className={classes.container}>
      <div className={classes.formContainer}>
        <div className={classes.title}>Profile details</div>

        <div className={classes.fieldsContainer}>
          {/* Name Field */}
          <div className={classes.fieldRow}>
            <div className={classes.fieldColumn}>
              <div className={classes.fieldLabel}>Name</div>
              <div className={classes.inputWrapper}>
                {isEditingName ? (
                  <VFTextbox
                    value={username}
                    onChange={setUsername}
                    width="100%"
                    useFixedWidth={false}
                    placeholder="Enter your name"
                  />
                ) : (
                  <div className={classes.placeholderField}>{username}</div>
                )}
              </div>
            </div>
            <VFButton
              variant={isEditingName ? "primary" : "secondary"}
              label={isEditingName ? "Save Changes" : "Change name"}
              onClick={
                isEditingName ? handleSaveName : () => setIsEditingName(true)
              }
              className={classes.actionButton}
            />
          </div>

          <div className={classes.divider} />

          {/* Email Field */}
          <div className={classes.fieldRow}>
            <div className={classes.fieldColumn}>
              <div className={classes.fieldLabel}>E-mail</div>
              <div className={classes.inputWrapper}>
                {isEditingEmail ? (
                  <VFTextbox
                    value={email}
                    onChange={setEmail}
                    width="100%"
                    useFixedWidth={false}
                    placeholder="Enter your email"
                  />
                ) : (
                  <div className={classes.placeholderField}>{email}</div>
                )}
              </div>
            </div>
            <VFButton
              variant={isEditingEmail ? "primary" : "secondary"}
              label={isEditingEmail ? "Save Changes" : "Change e-mail"}
              onClick={
                isEditingEmail ? handleSaveEmail : () => setIsEditingEmail(true)
              }
              className={classes.actionButton}
            />
          </div>

          <div className={classes.divider} />

          {/* Password Field */}
          <div className={classes.fieldRow}>
            <div className={classes.fieldColumn}>
              <div className={classes.passwordLabel}>Password</div>
              {isEditingPassword && (
                <div className={classes.passwordFieldsContainer}>
                  <div className={classes.inputWrapper}>
                    <VFTextbox
                      type="password"
                      value={currentPassword}
                      onChange={setCurrentPassword}
                      width="100%"
                      useFixedWidth={false}
                      placeholder="Current password"
                    />
                  </div>
                  <div className={classes.inputWrapper}>
                    <VFTextbox
                      type="password"
                      value={newPassword}
                      onChange={setNewPassword}
                      width="100%"
                      useFixedWidth={false}
                      placeholder="New password"
                    />
                  </div>
                  <div className={classes.inputWrapper}>
                    <VFTextbox
                      type="password"
                      value={confirmPassword}
                      onChange={setConfirmPassword}
                      width="100%"
                      useFixedWidth={false}
                      placeholder="Confirm new password"
                    />
                  </div>
                </div>
              )}
            </div>
            {isEditingPassword ? (
              <VFButton
                variant="primary"
                label="Save Changes"
                loading={isLoading}
                onClick={handleSavePassword}
                className={classes.actionButton}
              />
            ) : (
              <VFButton
                variant="secondary"
                label="Change password"
                onClick={() => setIsEditingPassword(true)}
                className={classes.actionButton}
              />
            )}
          </div>
        </div>
      </div>

      <VFButton
        variant="destructive"
        label="Delete account"
        onClick={handleDeleteAccount}
        className={classes.deleteButton}
      />
    </div>
  );
};
