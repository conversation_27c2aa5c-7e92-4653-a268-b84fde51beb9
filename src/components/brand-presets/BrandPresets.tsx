import React, { useState, useEffect, useRef } from "react";
import { createStyles, useMantineTheme } from "@mantine/core";
import { VFTable } from "../ui/Table";
import FileUpload from "../ui/FileUpload";
import { VFButton } from "../ui/Button";
import { PresetSettings } from "../editor/PresetSettings";
import { DEFAULT_SUBTITLES_WORDS_PER_LINE } from "../../constants";
import { PresetConfig } from "../../remotion/types";
import { openModal } from "../../features/modal/modalSlice";
import { ModalType } from "../../features/modal/types";
import { closeModal } from "../../features/modal/modalSlice";
import { ModalConfig } from "../../features/modal/types";
import { AppDispatch, RootState } from "../../store";
import { useDispatch, useSelector } from "react-redux";
import {
  uploadFont,
  deleteFont,
  deletePreset,
  createPreset,
  updatePreset,
} from "../../features/presets/presetsSlice";
import VFIconComponent from "../icon/vf-icon";
import { ProgressBar } from "../ui/Progress";
import { useErrorHandler } from "../../hooks/useErrorHandler";
import { loadFont } from "../../features/presets/fontUtils";
import { getCssFontFamily } from "../../features/presets/fontUtils";
import { ErrorResponse } from "../../types";

const useStyles = createStyles((theme) => ({
  container: {
    width: "100%",
  },
  section: {
    display: "flex",
    flexDirection: "column",
    gap: "48px",
    width: "100%",
    padding: "16px 16px 32px",
    marginBottom: "48px",
    border: `1px solid ${theme.other["surface-border-color"]}`,
    borderRadius: "4px",
  },
  sectionHeader: {
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  },
  sectionTitle: {
    color: theme.other["surface-text-color"],
  },
  sectionSubtitle: {
    color: theme.other["surface-text-color"],
  },
  actionButtons: {
    display: "flex",
    flexDirection: "row",
    gap: "8px",
    justifyContent: "flex-start",
  },
  fileUploadContainer: {
    minHeight: 96,
  },
  tableHeader: {
    color: theme.other["table-header-text-color"],
    textTransform: "uppercase",
  },
  headerButtons: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
  },
  presetEditTitle: {
    color: theme.other["surface-text-color"],
    margin: 0,
  },
  titleContainer: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  inputGroup: {
    display: "flex",
    alignItems: "flex-start",
    gap: 8,
    width: "100%",
  },
  inputWrapper: {
    flex: "1 1 auto",
    minWidth: 0,
  },
  fileNameHolder: {
    display: "flex",
    gap: 10,
    minWidth: 200,
    width: "100%",
    padding: "8px 0",

    "& span": {
      color: theme.other["textbox-text-color"],
      whiteSpace: "nowrap",
      overflow: "hidden",
      textOverflow: "ellipsis",
    },

    "& > div": {
      alignItems: "center",
    },
  },
  uploadProgress: {
    marginTop: 16,
    marginBottom: 8,
  },
  loadingContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
    marginTop: 16,
    marginBottom: 8,
  },
  loadingText: {
    color: theme.other["uploader-text-color"],
  },
  usedInText: {
    whiteSpace: "pre-line",
  },
  previewContainer: {
    width: "100%",
    backgroundColor: theme.other["surface-bg-color"],
    border: `1px solid ${theme.other["surface-border-color"]}`,
    borderRadius: "4px",
    display: "flex",
    flexDirection: "column",
    alignItems: "stretch",
    position: "relative",
    marginBottom: "24px",
    padding: "34px",
  },
  previewHeader: {
    padding: "16px",
    display: "flex",
    alignItems: "center",
    borderBottom: `1px solid ${theme.other["surface-border-color"]}`,
  },
  previewHeaderText: {
    margin: 0,
    color: theme.other["surface-text-color"],
    position: "absolute",
    top: "5px",
    left: "5px",
  },
  previewContent: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100%",
    width: "100%",
    flex: 1,
  },
  previewText: {
    textAlign: "center",
    textTransform: "uppercase",
  },
}));

// Preview component for preset
const PresetPreview: React.FC<{ preset: PresetConfig }> = ({ preset }) => {
  const { classes, cx } = useStyles();
  const mantineTheme = useMantineTheme();

  // Style for text based on preset settings
  const getShadowStyle = () => {
    if (!preset.shadow) return {};

    const { color, opacity, offset, blur } = preset.shadow;
    const adjustedOpacity = Math.min(opacity, 70);
    const opacityHex = Math.floor((adjustedOpacity / 100) * 255)
      .toString(16)
      .padStart(2, "0");
    const shadowColor = `${color}${opacityHex}`;

    const offsetX = offset ? offset.x : 0;
    const offsetY = offset ? offset.y : 0;
    const blurRadius = blur || 0;

    return {
      textShadow: `${offsetX}px ${offsetY}px ${blurRadius}px ${shadowColor}`,
    };
  };

  const getStrokeStyle = () => {
    if (!preset.stroke) return {};

    const { color, weight, opacity } = preset.stroke;
    const adjustedOpacity = Math.min(opacity, 85);
    const opacityHex = Math.floor((adjustedOpacity / 100) * 255)
      .toString(16)
      .padStart(2, "0");
    const strokeColor = `${color}${opacityHex}`;

    // Make stroke weight proportional to font size (reduces heaviness for smaller text)
    const strokeRatio = 0.08; // 8% of font size
    const strokeWeight = Math.min(
      weight,
      Math.max(1, preset.size * strokeRatio)
    );

    return {
      WebkitTextStroke: `${strokeWeight}px ${strokeColor}`,
      textStroke: `${strokeWeight}px ${strokeColor}`,
    };
  };

  // Get the font CSS name
  const fontFamily = preset.font
    ? getCssFontFamily(preset.font.family, preset.font.sub_family)
    : "Inter";

  // Function to brighten a color for highlighted word
  const brightenColor = (color: string, percent: number): string => {
    if (color.startsWith("#")) {
      const hex = color.slice(1);
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);

      r = Math.min(255, Math.floor(r * (1 + percent / 100)));
      g = Math.min(255, Math.floor(g * (1 + percent / 100)));
      b = Math.min(255, Math.floor(b * (1 + percent / 100)));

      return `#${r.toString(16).padStart(2, "0")}${g
        .toString(16)
        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
    }
    return color;
  };

  // Base text style
  const baseTextStyle = {
    color: preset.color || "#FFFFFF",
    fontSize: `${preset.size}px` || "26px",
    fontFamily: `"${fontFamily}", Arial, sans-serif`,
    fontWeight: "normal",
    lineHeight: "1.2",
    letterSpacing: "0.02em",
    fontSmooth: "always",
    WebkitFontSmoothing: "antialiased",
    MozOsxFontSmoothing: "grayscale",
    display: "inline-block",
    margin: "0 2px",
    ...getShadowStyle(),
    ...getStrokeStyle(),
  };

  // Current word style - applied to first word
  const currentWordStyle = {
    ...baseTextStyle,
    color:
      preset.current_word_color || brightenColor(preset.color || "#FFFFFF", 15),
    backgroundColor: preset.current_word_bg_color || "transparent",
    borderRadius: "15px",
    padding: "0 4px",
    transform: "scale(1.05)",
  };

  // Adjust stroke for current word if needed
  if (preset.stroke) {
    const originalWeight = parseFloat((preset.stroke.weight / 2).toString());
    currentWordStyle.WebkitTextStroke = `${originalWeight}px ${preset.stroke.color}`;
  }

  return (
    <div className={classes.previewContainer}>
      <p className={cx(classes.previewHeaderText, "small-p-regular")}>
        Preview
      </p>
      <div className={classes.previewContent}>
        <div className={classes.previewText}>
          <span style={currentWordStyle}>LOREM</span>{" "}
          <span style={baseTextStyle}>IPSUM</span>{" "}
          <span style={baseTextStyle}>DOLOR</span>{" "}
          <span style={baseTextStyle}>SIT</span>{" "}
          <span style={baseTextStyle}>AMET</span>
        </div>
      </div>
    </div>
  );
};

interface BrandPresetsProps {
  key?: string;
}

export const BrandPresets: React.FC<BrandPresetsProps> = () => {
  const { classes, cx } = useStyles();
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFont, setUploadedFont] = useState<File | null>(null);
  const [isEditingPreset, setIsEditingPreset] = useState(false);
  const [currentPresetId, setCurrentPresetId] = useState<string | null>(null);
  const [currentPreset, setCurrentPreset] = useState<PresetConfig | null>(null);
  const [wordsPerLine, setWordsPerLine] = useState(
    DEFAULT_SUBTITLES_WORDS_PER_LINE
  );
  const [showDropzone, setShowDropzone] = useState(true);
  const dispatch = useDispatch<AppDispatch>();
  const { handleError } = useErrorHandler();
  const mantineTheme = useMantineTheme();

  // Get fonts and presets from Redux store
  const presets = useSelector((state: RootState) => state.presets.presets);
  const fonts = useSelector((state: RootState) => state.presets.fonts);
  const loadingStatus = useSelector(
    (state: RootState) => state.presets.loadingStatus
  );

  // Use a ref to track if we've already fetched data
  const dataRequestedRef = useRef(false);

  // Fetch fonts and presets once on component mount
  useEffect(() => {
    // Cleanup function
    return () => {
      setIsEditingPreset(false);
      setCurrentPresetId(null);
      setCurrentPreset(null);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run on mount and cleanup on unmount

  // Simulate upload progress
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isUploading && uploadProgress < 100) {
      intervalId = setInterval(() => {
        setUploadProgress((prev) => {
          const increment = Math.random() * 10;
          const newProgress = Math.min(prev + increment, 98);
          return newProgress;
        });
      }, 300);
    }

    return () => {
      clearInterval(intervalId);
    };
  }, [isUploading, uploadProgress]);

  // Handler for font file upload
  const handleFontUpload = (files: File[]) => {
    if (files.length === 0) return;

    const fontFile = files[0];
    setUploadedFont(fontFile);
    setShowDropzone(false);
    setUploadProgress(0);
  };

  // Handler for rejected files
  const handleReject = () => {
    setShowDropzone(true);
  };

  // Handler for remove font button
  const handleRemoveFont = () => {
    setUploadedFont(null);
    setShowDropzone(true);
    setUploadProgress(0);
  };

  // Handler for upload font button
  const handleUploadFont = async () => {
    if (!uploadedFont) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Upload the font file using the Redux thunk
      const resultAction = await dispatch(uploadFont({ file: uploadedFont }));

      if (!uploadFont.fulfilled.match(resultAction)) {
        if (resultAction.payload) {
          const errorPayload = resultAction.payload as {
            error: ErrorResponse;
            statusCode: number;
          };
          handleError(errorPayload.error, errorPayload.statusCode);
        }
        setIsUploading(false);
        setUploadProgress(0);
        return;
      }

      const response = resultAction.payload;

      // Complete the progress bar
      setUploadProgress(100);

      // Load the newly uploaded font using the FontFace API
      if (response && response.user && response.user.length > 0) {
        // Find the newly added font (should be the last one)
        const newFont = response.user[response.user.length - 1];

        // Load each sub-family
        for (const subFamily of newFont.sub_families) {
          try {
            await loadFont(
              newFont.family,
              subFamily.file_path,
              subFamily.sub_family
            );
          } catch (err) {
            console.error(
              `Failed to load font: ${newFont.family}-${subFamily.sub_family}`,
              err
            );
          }
        }
      }

      // Reset upload state
      setTimeout(() => {
        setUploadedFont(null);
        setShowDropzone(true);
        setIsUploading(false);
        setUploadProgress(0);
      }, 500);
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  // Handler for deleting a font
  const handleDeleteFont = (family: string) => {
    const modalConfig: ModalConfig = {
      type: ModalType.BASIC,
      title: "Delete font",
      subtitle:
        "The font will be permanently removed from your account and replaced with the default font in all associated presets.",
      actions: [
        {
          label: "Cancel",
          variant: "secondary",
          fullWidth: true,
          buttonDisplay: "onlyText",
          onClick: () => dispatch(closeModal()),
        },
        {
          label: "Delete",
          variant: "destructive",
          fullWidth: true,
          buttonDisplay: "withIcon",
          iconName: "trash-can",
          onClick: async () => {
            try {
              const resultAction = await dispatch(deleteFont({ family }));

              if (
                !deleteFont.fulfilled.match(resultAction) &&
                resultAction.payload
              ) {
                const errorPayload = resultAction.payload as {
                  error: ErrorResponse;
                  statusCode: number;
                };
                handleError(errorPayload.error, errorPayload.statusCode);
              }

              dispatch(closeModal());
            } catch (errorResponse: any) {
              handleError(errorResponse.error, errorResponse.statusCode);
              dispatch(closeModal());
            }
          },
        },
      ],
    };

    dispatch(openModal(modalConfig));
  };

  // Use presets from Redux store instead of dummy data
  const brandPresetData = presets?.user || [];

  // Font assets table columns
  const fontAssetsColumns = [
    {
      key: "family",
      header: "FONT NAME",
      width: "35%",
    },
    {
      key: "created_at",
      header: "DATE ADDED",
      width: "30%",
      render: (value: string) => {
        const date = new Date(value);
        return date.toLocaleDateString();
      },
    },
    {
      key: "presets",
      header: "USED IN PRESETS",
      width: "25%",
      render: (value: string[]) => (
        <div className={classes.usedInText}>
          {value.join("\n") || "Not used"}
        </div>
      ),
    },
    // {
    //   key: "actions",
    //   header: "ACTIONS",
    //   width: "10%",
    //   render: (value: any, row: Record<string, any>) => (
    //     <div
    //       className={classes.actionButtons}
    //       onClick={(e) => e.stopPropagation()}
    //     >
    //       <VFButton
    //         variant="primary"
    //         buttonDisplay="onlyIcon"
    //         iconName="trash-can"
    //         size="small"
    //         onClick={() => handleDeleteFont(row.family)}
    //       />
    //     </div>
    //   ),
    // },
  ];

  // Brand presets table columns
  const brandPresetColumns = [
    {
      key: "name",
      header: "PRESET NAME",
      width: "75%",
    },
    {
      key: "actions",
      header: "ACTIONS",
      width: "25%",
      render: (value: any, row: Record<string, any>) => (
        <div
          className={classes.actionButtons}
          onClick={(e) => e.stopPropagation()}
        >
          <VFButton
            variant="secondary"
            buttonDisplay="withIcon"
            iconName="edit"
            size="small"
            onClick={() => handleEditPreset(row.id)}
          >
            Edit
          </VFButton>
          <VFButton
            variant="primary"
            buttonDisplay="withIcon"
            iconName="trash-can"
            size="small"
            onClick={() => handleDeletePreset(row.id)}
          >
            Delete
          </VFButton>
        </div>
      ),
    },
  ];

  // Handler for edit preset button
  const handleEditPreset = (presetId: string) => {
    if (!presets) return;

    // Find the preset in user presets
    const selectedPreset = presets.user.find(
      (preset) => preset.id === presetId
    );

    if (selectedPreset) {
      setCurrentPresetId(presetId);
      setCurrentPreset({ ...selectedPreset.config });
      setWordsPerLine(
        selectedPreset.config.words_per_line || DEFAULT_SUBTITLES_WORDS_PER_LINE
      );
      setIsEditingPreset(true);
    }
  };

  // Handler for delete preset button
  const handleDeletePreset = (presetId: string) => {
    const modalConfig: ModalConfig = {
      type: ModalType.BASIC,
      title: "Delete brand preset",
      subtitle:
        "Тhe preset will be permanently removed from your account and replaced with the default caption preset in all associated projects and clips.",
      actions: [
        {
          label: "Cancel",
          variant: "secondary",
          fullWidth: true,
          buttonDisplay: "onlyText",
          onClick: () => dispatch(closeModal()),
        },
        {
          label: "Delete",
          variant: "destructive",
          fullWidth: true,
          buttonDisplay: "withIcon",
          iconName: "trash-can",
          onClick: async () => {
            try {
              console.warn("Deleting preset", presetId);

              const resultAction = await dispatch(
                deletePreset({ id: presetId })
              );

              if (
                !deletePreset.fulfilled.match(resultAction) &&
                resultAction.payload
              ) {
                const errorPayload = resultAction.payload as {
                  error: ErrorResponse;
                  statusCode: number;
                };
                handleError(errorPayload.error, errorPayload.statusCode);
              }

              dispatch(closeModal());
            } catch (errorResponse: any) {
              handleError(errorResponse.error, errorResponse.statusCode);
              dispatch(closeModal());
            }
          },
        },
      ],
    };

    dispatch(openModal(modalConfig));
  };

  // Handler for back button to return to presets list
  const handleBackToPresets = () => {
    setIsEditingPreset(false);
    setCurrentPresetId(null);
    setCurrentPreset(null);
  };

  // Handler for save changes button
  const handleSaveChanges = async () => {
    if (!currentPreset) return;

    try {
      // If the preset has an id, update it, otherwise create a new one
      if (currentPreset && currentPresetId) {
        const resultAction = await dispatch(
          updatePreset({
            presetConfig: currentPreset,
            id: currentPresetId.toString(),
          })
        );

        if (
          !updatePreset.fulfilled.match(resultAction) &&
          resultAction.payload
        ) {
          const errorPayload = resultAction.payload as {
            error: ErrorResponse;
            statusCode: number;
          };
          handleError(errorPayload.error, errorPayload.statusCode);
          return;
        }
      } else {
        const resultAction = await dispatch(
          createPreset({ config: currentPreset, name: "" })
        );

        if (
          !createPreset.fulfilled.match(resultAction) &&
          resultAction.payload
        ) {
          const errorPayload = resultAction.payload as {
            error: ErrorResponse;
            statusCode: number;
          };
          handleError(errorPayload.error, errorPayload.statusCode);
          return;
        }
      }

      // Return to the presets list
      setIsEditingPreset(false);
      setCurrentPresetId(null);
      setCurrentPreset(null);
    } catch (errorResponse: any) {
      handleError(errorResponse.error, errorResponse.statusCode);
    }
  };

  // Handler for preset changes
  const handlePresetChange = (updatedPreset: PresetConfig) => {
    setCurrentPreset(updatedPreset);
  };

  // Handler for words per line changes
  const handleWordsPerLineChange = (value: number) => {
    setWordsPerLine(value);
    if (currentPreset) {
      setCurrentPreset({
        ...currentPreset,
        words_per_line: value,
      });
    }
  };

  // Find preset name based on id
  const getPresetName = (id: string | null) => {
    if (!id || !presets) return "";
    const preset = presets.user.find((p) => p.id === id);
    return preset ? preset.name : "";
  };

  // Render the uploaded font form
  const renderUploadedFontForm = () => {
    if (!uploadedFont) return null;

    return (
      <div>
        <div className={classes.inputGroup}>
          <div className={classes.inputWrapper}>
            <div className={classes.fileNameHolder}>
              <span className="p-heavy">{uploadedFont.name}</span>
              <VFIconComponent
                type="check"
                color={mantineTheme.other["textbox-valid-color"]}
                dimensions={{ width: 16, height: 16 }}
              />
            </div>
          </div>
          <VFButton
            variant="secondary"
            buttonDisplay="withIcon"
            iconName="trash-can"
            onClick={handleRemoveFont}
            disabled={isUploading}
          >
            Remove
          </VFButton>
        </div>

        {isUploading && (
          <>
            <div className={classes.uploadProgress}>
              <ProgressBar value={uploadProgress} />
            </div>
            <div className={classes.loadingContainer}>
              <VFIconComponent
                type="upload-new"
                color={mantineTheme.other["uploader-text-color"]}
                dimensions={{ width: 24, height: 24 }}
              />
              <span className={`p-heavy ${classes.loadingText}`}>
                Uploading...
              </span>
            </div>
          </>
        )}

        {!isUploading && (
          <VFButton
            variant="primary"
            onClick={handleUploadFont}
            fullWidth
            style={{ marginTop: 16 }}
          >
            Add custom font to account
          </VFButton>
        )}
      </div>
    );
  };

  if (isEditingPreset && currentPreset) {
    return (
      <div className={classes.container}>
        <div className={classes.section}>
          <div className={classes.headerButtons}>
            <div className={classes.titleContainer}>
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName="arrow-left"
                onClick={handleBackToPresets}
              />
              <h3 className={classes.presetEditTitle}>
                Edit {getPresetName(currentPresetId)}
              </h3>
            </div>
            <VFButton variant="primary" onClick={handleSaveChanges}>
              Save changes
            </VFButton>
          </div>

          {/* Add PresetPreview component */}
          <PresetPreview preset={currentPreset} />

          <PresetSettings
            preset={currentPreset}
            onChange={handlePresetChange}
            wordsPerLine={wordsPerLine}
            onWordsPerLineChange={handleWordsPerLineChange}
            isFetchingSubtitles={false}
            usePercentageLayout={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={classes.container}>
      {/* Custom Assets Section */}
      <div className={classes.section}>
        <div className={classes.sectionHeader}>
          <h3 className={classes.sectionTitle}>Custom assets</h3>
          <p className={cx(classes.sectionSubtitle, "small-p-regular")}>
            Custom fonts you've added to your account can be selected while
            configuring video captions.
          </p>
        </div>

        <div className={classes.fileUploadContainer}>
          {showDropzone ? (
            <FileUpload
              variant="font"
              onDrop={handleFontUpload}
              onReject={handleReject}
              loading={isUploading}
              style={{ minHeight: "96px" }}
              activateOnClick={true}
            />
          ) : (
            renderUploadedFontForm()
          )}
        </div>

        {/* Custom Assets Table - only show if fonts exist */}
        {fonts && fonts.user && fonts.user.length > 0 && (
          <VFTable
            data={fonts.user}
            columns={fontAssetsColumns}
            hoverable={false}
            showHeader={true}
          />
        )}
      </div>

      {/* Brand Presets Section */}
      <div className={classes.section}>
        <div className={classes.sectionHeader}>
          <h3 className={classes.sectionTitle}>Brand presets</h3>
          <p className={cx(classes.sectionSubtitle, "small-p-regular")}>
            Custom presets you've saved to your account can be selected while
            configuring video captions.
          </p>
        </div>

        {brandPresetData && brandPresetData.length > 0 && (
          <VFTable
            data={brandPresetData}
            columns={brandPresetColumns}
            hoverable={false}
            showHeader={false}
          />
        )}
      </div>
    </div>
  );
};

export default BrandPresets;
