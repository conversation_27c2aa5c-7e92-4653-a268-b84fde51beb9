import React, { FC, useEffect } from "react";
import { Navigate } from "react-router-dom";
import useAuth from "../../hooks/useAuthCheck";
import { LoadingOverlay } from "@mantine/core";
import { useDispatch } from "react-redux";
import { resetAuthFailure } from "../../features/auth/authSlice";

interface ProtectedRouteProps {
  element: React.ReactElement;
}

const ProtectedRoute: FC<ProtectedRouteProps> = ({ element }) => {
  const { isAuthenticated, loading, isAllDataLoaded, authenticationFailed } =
    useAuth();
  const dispatch = useDispatch();

  // Reset the authentication failure state when component unmounts or redirects to login
  useEffect(() => {
    return () => {
      if (!isAuthenticated || authenticationFailed) {
        dispatch(resetAuthFailure());
      }
    };
  }, [isAuthenticated, authenticationFailed, dispatch]);

  // Redirect immediately if authentication has failed, regardless of loading state
  if (authenticationFailed) {
    return <Navigate to="/login" />;
  }

  if (loading || !isAllDataLoaded) {
    return (
      <LoadingOverlay
        visible={true}
        loaderProps={{ size: "lg", variant: "dots" }}
      />
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  return element;
};

export default ProtectedRoute;
