import React, { useMemo, useState, memo, useEffect } from "react";
import { createStyles } from "@mantine/core";
import { Skeleton } from "@mantine/core";
import { MenuItemProps, VFMenu } from "../ui/Menu";
import { VFButton } from "../ui/Button";
import { VFLabel, VFLabelType } from "../ui/Label";
import { ProcessingStatus } from "../../types";
import {
  useNoRenderProcessingSimulation,
  SimulationStatus,
} from "../../hooks/useDownloadSimulation";
import { selectVideoById } from "@/features/videos/videosSlice";
import { useSelector } from "react-redux";
import { RootState } from "@/store";

export type ProjectCardAction = "view-clips" | "delete";

interface ProjectCardProps {
  thumbnail: string;
  title: string;
  createdAt: string;
  onActionClick: (action: ProjectCardAction) => void;
  processingStatus: ProcessingStatus;
  videoId: string;
}

// Separate memoized component for the processing label to prevent re-renders of the entire card
const ProcessingLabel = memo(
  ({
    videoId,
    simulationTimeMs,
  }: {
    videoId: string;
    simulationTimeMs?: number;
  }) => {
    // Use local state for UI updates
    const [percentage, setPercentage] = useState(0);
    const [eta, setEta] = useState("5m");

    // Use our non-rendering simulation hook
    const simulation = useNoRenderProcessingSimulation(
      videoId,
      () => {}, // No-op complete handler
      simulationTimeMs
    );

    // Update the state periodically
    useEffect(() => {
      // Set initial values
      setPercentage(simulation.percentage);
      setEta(simulation.eta);

      // Start the simulation if it's not already running
      if (simulation.status === SimulationStatus.IDLE && simulationTimeMs) {
        simulation.startSimulation();
      }

      // Update periodically to reflect current simulation values
      const intervalId = setInterval(() => {
        setPercentage(simulation.percentage);
        setEta(simulation.eta);
      }, 3000); // Update every 3 seconds

      return () => clearInterval(intervalId);
    }, [simulation, simulationTimeMs]);

    if (!simulationTimeMs) {
      return <VFLabel type={VFLabelType.LOADING} percentage={0} eta={"..."} />;
    }

    return (
      <VFLabel type={VFLabelType.LOADING} percentage={percentage} eta={eta} />
    );
  }
);

const useStyles = createStyles((theme, _params, getRef) => {
  const titleRef = getRef("title");

  return {
    wrapper: {
      display: "flex",
      flexDirection: "column",
      gap: "12px",
      backgroundColor: theme.other["main-bg-color"],
      "&:hover": {
        cursor: "pointer",
        backgroundColor: theme.other["surface-bg-color"],
        [`.${titleRef}`]: {
          textDecoration: "underline",
        },
      },
    },

    card: {
      border: `1px solid ${theme.other["card-border-color"]}`,
      borderRadius: "8px",
      overflow: "hidden",
      background: "transparent",

      "@media (min-width: 1728px)": {
        width: "226px",
        height: "126px",
      },
      "@media (min-width: 1280px) and (max-width: 1727px)": {
        width: "238px",
        height: "140px",
      },
      "@media (min-width: 744px) and (max-width: 1279px)": {
        width: "296px",
        height: "172px",
      },
      "@media (max-width: 743px)": {
        width: "312px",
        height: "172px",
      },
    },

    thumbnailContainer: {
      width: "100%",
      height: "100%",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      overflow: "hidden",
      position: "relative",
    },

    thumbnail: {
      width: "auto",
      height: "100%",
      objectFit: "contain",
    },

    titleContainer: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      maxWidth: "inherit",
      "@media (min-width: 1728px)": {
        width: "224px",
      },
      "@media (min-width: 1280px) and (max-width: 1727px)": {
        width: "238px",
      },
      "@media (min-width: 744px) and (max-width: 1279px)": {
        width: "296px",
      },
      "@media (max-width: 743px)": {
        width: "312px",
      },
    },

    title: {
      ref: titleRef,
      color: theme.other["card-text-color"],
      margin: 0,
      minWidth: 0,
      flex: 1,
      overflow: "hidden",
      textOverflow: "ellipsis",
      whiteSpace: "nowrap",
      fontSize: "14px",
      lineHeight: "20px",
      marginRight: "8px",
    },

    menuButton: {
      position: "relative",
      flexShrink: 0,
    },
  };
});

export const ProjectCard: React.FC<ProjectCardProps> = ({
  thumbnail,
  title,
  processingStatus,
  createdAt,
  onActionClick,
  videoId,
}) => {
  const { classes } = useStyles();
  const [menuOpen, setMenuOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const video = useSelector((state: RootState) =>
    selectVideoById(state, videoId)
  );

  const isWithinFourHours = (created_at: string): boolean => {
    const createdDate = new Date(created_at);
    const now = new Date();
    const fourHoursInMs = 4 * 60 * 60 * 1000;

    return now.getTime() - createdDate.getTime() <= fourHoursInMs;
  };

  const isRecent = useMemo(() => isWithinFourHours(createdAt), [createdAt]);

  const videoDurationInSeconds = useMemo(() => {
    return video?.media_metadata?.duration_seconds;
  }, [video]);

  // Estimate processing time: 1s video = 3.13s processing, add 20% buffer
  const estimatedProcessingTimeMs = useMemo(() => {
    if (
      typeof videoDurationInSeconds === "number" &&
      !isNaN(videoDurationInSeconds) &&
      videoDurationInSeconds > 0
    ) {
      const renderTime = videoDurationInSeconds * 0.287; // ratio based on real data
      const bufferedTime = renderTime * 1.4; // (20% buffer)

      return Math.ceil(bufferedTime * 1000);
    }
    // Fallback to 5 minutes
    return 5 * 60 * 1000;
  }, [videoDurationInSeconds]);

  // Calculate simulation time based on real duration if available

  const menuItems: MenuItemProps[] = [
    {
      label: "View clips",
      onClick: () => {
        onActionClick("view-clips");
        setMenuOpen(false);
      },
      disabled: processingStatus === ProcessingStatus.PROCESSING,
    },
    {
      label: "Delete",
      leftIcon: "trash-can",
      isDestructive: true,
      onClick: () => {
        onActionClick("delete");
        setMenuOpen(false);
      },
    },
  ];

  if (isLoading) {
    return (
      <div className={classes.wrapper}>
        <div className={classes.card}>
          <Skeleton visible height="100%" radius={4} animate={true} />
        </div>
        <Skeleton visible height={20} width="80%" radius={4} animate={true} />
      </div>
    );
  }

  return (
    <div
      className={classes.wrapper}
      onClick={(e) => {
        const isMenuButtonClicked: boolean =
          (e.target as HTMLElement).tagName === "BUTTON" ||
          (e.target as HTMLElement).tagName === "svg" ||
          (e.target as HTMLElement).tagName === "path";

        // Prevent triggering view-clips if menu is open and the menu item is disabled
        if (menuOpen || processingStatus === ProcessingStatus.PROCESSING) {
          return;
        }

        if (!isMenuButtonClicked) {
          onActionClick("view-clips");
        }
      }}
    >
      <div className={classes.card}>
        <div className={classes.thumbnailContainer}>
          <img
            src={thumbnail}
            alt={title}
            className={classes.thumbnail}
            onLoad={() => setIsLoading(false)}
          />
          {processingStatus === ProcessingStatus.ERROR && (
            <VFLabel type={VFLabelType.ERROR} />
          )}
          {processingStatus === ProcessingStatus.PROCESSING && (
            <ProcessingLabel
              videoId={videoId}
              simulationTimeMs={estimatedProcessingTimeMs}
            />
          )}
          {isRecent && <VFLabel type={VFLabelType.NEW} />}
        </div>
      </div>
      <div className={classes.titleContainer}>
        <h3 className={`${classes.title} p-heavy`}>{title}</h3>
        <div className={classes.menuButton}>
          <VFButton
            variant="secondary"
            buttonDisplay="onlyIcon"
            iconName="more"
            onClick={() => setMenuOpen(!menuOpen)}
          />
          <VFMenu
            items={menuItems}
            isOpen={menuOpen}
            position="right"
            onClose={() => setMenuOpen(false)}
          />
        </div>
      </div>
    </div>
  );
};
