import { useNavigate, useLocation, useParams } from "react-router-dom";
import LogoImage from "./../../assets/images/boostcast-logo.svg";
import { createStyles } from "@mantine/core";
import { VFMenu } from "../ui/Menu";
import { VFButton } from "../ui/Button";
import { useCallback, useState } from "react";
import { MenuItemProps } from "../ui/Menu";
import { logout } from "../../features/auth/authSlice";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import VFIconComponent from "../icon/vf-icon";
import {
  useClipConfigAPI,
  useClipSegments,
  useClipSubtitles,
} from "../../features/clipConfig/ClipConfigApi";
import { ICreateClipPayload, ProcessingStatus } from "../../types";
import {
  renderClip,
  selectClipById,
  updateClip,
  updateClipState,
} from "../../features/clips/clipsSlice";
import { openModal } from "../../features/modal/modalSlice";
import { ModalType } from "../../features/modal/types";
import { ModalConfig } from "../../features/modal/types";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { pushToDataLayer } from "@/utils";

const useStyles = createStyles((theme) => ({
  root: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    height: "64px",
    padding: "16px 24px",
    backgroundColor: theme.other["surface-bg-color"],
  },
  logo: {
    cursor: "pointer",

    img: {
      width: "158px",
      height: "auto",
    },
  },
  actionsContainer: {
    display: "flex",
    alignItems: "center",
    gap: "16px",
  },
  userMenu: {
    position: "relative",
    cursor: "pointer",
  },
  boostContainer: {
    display: "flex",
    alignItems: "center",
    gap: "4px",
  },
  boost: {
    color: "#ffffff",
  },
  editorButtons: {
    display: "flex",
    alignItems: "center",
    gap: "16px",
  },
}));

type HeaderProps = {};

const Header: React.FC<HeaderProps> = ({}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { classes, cx } = useStyles();
  const { userData } = useSelector((state: RootState) => state.auth);
  const boost = userData?.boost || 0; // Default to 0 if userData or boost is undefined
  const subtitles = useClipSubtitles();
  const segments = useClipSegments();
  const { undo, redo, canUndo, canRedo } = useClipConfigAPI();
  const { projectId, clipId: clipId } = useParams();
  const clip = useSelector((state: RootState) =>
    selectClipById(state, projectId as string, clipId as string)
  );
  const { handleError } = useErrorHandler();
  const [exportButtonLoading, setExportButtonLoading] = useState(false);

  const exportButtonDisabled =
    !clip || clip.rendering_status === ProcessingStatus.PROCESSING;

  // Editor routes are: /projects/:projectId/edit and /projects/:projectId/clips/:clipId/edit
  const isEditorPath =
    location.pathname.includes("/projects/") &&
    location.pathname.endsWith("/edit");

  const menuItems: MenuItemProps[] = [
    {
      label: "Account settings",
      leftIcon: "settings",
      onClick: () => {
        setIsMenuOpen(false);
        navigate("/account-settings");
      },
    },
    {
      label: "Logout",
      leftIcon: "logout",
      onClick: () => {
        setIsMenuOpen(false);
        dispatch(logout());
      },
    },
  ];

  const exportClipHandler = useCallback(async () => {
    const payload: ICreateClipPayload = {
      segments,
      subtitles,
    };

    if (!projectId) {
      // No projectId found, cannot export clip.
      return;
    }

    if (userData?.subscription_level === 0) {
      // Configure the modal
      const pricingModalConfig: ModalConfig = {
        type: ModalType.PRICING,
        title: "Upgrade your subscription",
        subtitle:
          "You're using a trial version of Boostcast and downloading your clips is currently unavailable for you. Upgrade to unlock your full experience.",
      };

      dispatch(openModal(pricingModalConfig));
    } else {
      pushToDataLayer("clip_exported", {
        email: userData?.email,
      });

      setExportButtonLoading(true);

      // update the clip config and then render it
      try {
        await dispatch(
          updateClip({
            clipId: clipId as string,
            updatePayload: payload,
          }) as any
        ).unwrap?.();

        // render the clip
        try {
          const renderedClip = await dispatch(
            renderClip({
              clipId: clipId as string,
            }) as any
          ).unwrap?.();

          await dispatch(
            updateClipState({
              videoId: projectId as string,
              data: renderedClip,
            }) as any
          ).unwrap?.();

          setExportButtonLoading(false);

          // Return from the function
          return;
        } catch (error: any) {
          // Failed to export clip
          handleError(error.error, error.statusCode);

          setExportButtonLoading(false);
        }
      } catch (error: any) {
        // Failed to update clip
        handleError(error.error, error.statusCode);

        setExportButtonLoading(false);
      }
    }
  }, [dispatch, segments, subtitles, projectId, clipId, userData]);

  // If userData is not yet loaded, don't render the header
  if (!userData) {
    return null;
  }

  return (
    <header className="header">
      <div className={classes.root}>
        <div className={classes.logo} onClick={() => navigate("/")}>
          <img src={LogoImage} alt="Logo" />
        </div>

        <div className={classes.actionsContainer}>
          <div className={classes.boostContainer}>
            <VFIconComponent type="boost" size={24} />
            <span className={cx(classes.boost, "p-heavy")}>{boost}</span>
          </div>

          {/* Editor buttons - only shown when in editor */}
          {isEditorPath && (
            <div className={classes.editorButtons}>
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName="undo"
                onClick={undo}
                disabled={!canUndo}
              />
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName="redo"
                onClick={redo}
                disabled={!canRedo}
              />
              <VFButton
                variant="primary"
                disabled={exportButtonDisabled}
                onClick={exportClipHandler}
                loading={exportButtonLoading}
              >
                Export clip
              </VFButton>
            </div>
          )}

          <div className={classes.userMenu}>
            <VFButton
              variant="secondary"
              buttonDisplay="onlyIcon"
              iconName="user"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            />
            <VFMenu
              width={170}
              size="small"
              items={menuItems}
              isOpen={isMenuOpen}
              position="right"
              onClose={() => setIsMenuOpen(false)}
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
