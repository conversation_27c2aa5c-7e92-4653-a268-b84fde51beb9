import { IconComponentProps } from "./types";

export default function HeartFilledIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M16 4.75125V4.85437C16 6.16375 15.4563 7.41375 14.4969 8.30437L9.02188 13.3887L8.94375 13.4606C8.6875 13.6981 8.35 13.8325 8 13.8325C7.65 13.8325 7.3125 13.7012 7.05625 13.4606L6.97813 13.3887L1.50312 8.30437C0.54375 7.41375 0 6.16375 0 4.85437V4.75125C0 2.55125 1.5625 0.663746 3.725 0.251246C4.95625 0.0137457 6.21562 0.298121 7.21875 1.00437C7.5 1.20437 7.7625 1.43562 8 1.70125C8.13125 1.55125 8.27188 1.41375 8.42188 1.28562C8.5375 1.18562 8.65625 1.09187 8.78125 1.00437C9.55281 0.461246 10.4759 0.167496 11.4213 0.167496C11.705 0.167496 11.9906 0.194058 12.275 0.248121C14.4375 0.660621 16 2.55125 16 4.75125Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
