import { IconComponentProps } from "./types";

export default function TimeIcon({
  color = "#E1E1E1",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16" // Match the path's natural dimensions
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M13.6569 2.34313C12.1459 0.832188 10.1369 0 8 0C7.60687 0 7.28813 0.31875 7.28813 0.711875V3.74094C7.28813 4.13406 7.60687 4.45281 8 4.45281C8.39312 4.45281 8.71187 4.13406 8.71187 3.74094V1.46125C10.1994 1.62031 11.5775 2.27688 12.6503 3.34969C13.8925 4.59188 14.5766 6.24344 14.5766 8C14.5766 9.75656 13.8925 11.4081 12.6503 12.6503C11.4081 13.8925 9.75656 14.5766 8 14.5766C6.24344 14.5766 4.59188 13.8925 3.34969 12.6503C2.1075 11.4081 1.42344 9.75656 1.42344 8C1.42344 5.95438 2.35125 4.06 3.96906 2.80312C4.27938 2.56187 4.33563 2.11469 4.09438 1.80438C3.85313 1.49406 3.40594 1.43781 3.09562 1.67906C2.15375 2.41094 1.37438 3.36 0.841875 4.42375C0.283125 5.54031 0 6.74344 0 8C0 10.1369 0.832188 12.1459 2.34313 13.6569C3.85406 15.1678 5.86312 16 8 16C10.1369 16 12.1459 15.1678 13.6569 13.6569C15.1678 12.1459 16 10.1369 16 8C16 5.86312 15.1678 3.85406 13.6569 2.34313Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M7.45531 8.48906C7.60781 8.64156 7.80781 8.71781 8.00781 8.71781C8.20781 8.71781 8.40781 8.64156 8.56031 8.48906C8.86531 8.18406 8.86531 7.68937 8.56031 7.38406L5.96844 4.79219C5.66344 4.48719 5.16875 4.48719 4.86344 4.79219C4.55844 5.09719 4.55844 5.59187 4.86344 5.89719L7.45531 8.48906Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
