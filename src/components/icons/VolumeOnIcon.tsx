import { IconComponentProps } from "./types";

export default function VolumeOnIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 13"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M8.28531 0.0534833C8.06031 -0.0462042 7.7975 -0.00432914 7.615 0.160671L4.05656 3.37661H1.63656C0.734062 3.37661 0 4.11067 0 5.01286V6.97786C0 7.88005 0.734062 8.61442 1.63656 8.61442H4.24L7.59219 11.9663C7.71125 12.0857 7.87062 12.1488 8.03281 12.1488C8.11281 12.1488 8.19375 12.1332 8.27094 12.1013C8.50375 12.005 8.65531 11.7779 8.65531 11.526V0.622858C8.65531 0.376608 8.51031 0.153483 8.28531 0.0534833ZM7.40969 10.0222L4.93844 7.55098C4.82156 7.43442 4.66313 7.3688 4.49813 7.3688H1.63656C1.42094 7.3688 1.24563 7.19317 1.24563 6.97786V5.01286C1.24563 4.79755 1.42094 4.62223 1.63656 4.62223H4.29625C4.45062 4.62223 4.59937 4.56505 4.71375 4.46161L7.40969 2.02505V10.0222Z"
        fill={color}
        fillRule="evenodd"
      />
      <path
        d="M14.0288 10.3735C13.2459 10.9775 12.4263 10.1647 12.9709 9.4563C13.0859 9.3063 13.2916 9.18755 13.4012 9.06536C14.8244 7.48411 15.0694 5.17848 13.7188 3.45567C13.3572 2.99442 12.4222 2.50317 12.9487 1.83317C13.515 1.11286 14.3241 2.01755 14.7084 2.46598C16.6287 4.70442 16.4122 8.5338 14.0288 10.3735Z"
        fill={color}
        fillRule="evenodd"
      />
      <path
        d="M12.525 7.9313C12.1734 8.3538 11.6144 8.69067 11.1878 8.14473C10.6731 7.48598 11.4122 7.18911 11.6431 6.72098C11.8269 6.34817 11.8306 5.84661 11.6578 5.46942C11.46 5.03723 10.9219 4.87223 11.0456 4.29348C11.0994 4.04192 11.3663 3.77786 11.6237 3.74255C12.2131 3.66161 12.8463 4.6088 13.0178 5.09473C13.3547 6.04911 13.1734 7.15348 12.525 7.9313Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
