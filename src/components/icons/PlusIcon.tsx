import { IconComponentProps } from "./types";

export default function PlusIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M7.82031 0.820312C7.82031 0.367266 7.45305 0 7 0C6.54695 0 6.17969 0.367266 6.17969 0.820312V13.1797C6.17969 13.6327 6.54695 14 7 14C7.45305 14 7.82031 13.6327 7.82031 13.1797V0.820312Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M0.820312 6.17941C0.367266 6.17941 0 6.54668 0 6.99973C0 7.45277 0.367266 7.82004 0.820312 7.82004H13.1797C13.6327 7.82004 14 7.45277 14 6.99973C14 6.54668 13.6327 6.17941 13.1797 6.17941H0.820312Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
