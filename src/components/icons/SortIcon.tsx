import { IconComponentProps } from "./types";

export default function SortIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M13.5599 6.30125L7.44182 0.183125C7.32463 0.0659375 7.16557 0 6.99994 0C6.83432 0 6.67525 0.0659375 6.55807 0.183125L0.43994 6.30125C0.26119 6.48 0.207753 6.74875 0.304315 6.98219C0.40119 7.21594 0.629003 7.36812 0.881815 7.36812H13.1181C13.3709 7.36812 13.5987 7.21594 13.6956 6.98219C13.7921 6.74875 13.7387 6.48 13.5599 6.30125ZM2.39057 6.11812L6.99994 1.50875L11.6093 6.11812H2.39057Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M13.6956 9.01781C13.5987 8.78406 13.3709 8.63188 13.1181 8.63188H0.881815C0.629003 8.63188 0.40119 8.78406 0.304315 9.01781C0.207753 9.25125 0.26119 9.52 0.43994 9.69875L6.55807 15.8169C6.67994 15.9391 6.83994 16 6.99994 16C7.15994 16 7.31994 15.9391 7.44182 15.8169L13.5599 9.69875C13.7387 9.52 13.7921 9.25125 13.6956 9.01781ZM6.99994 14.4913L2.39057 9.88188H11.6093L6.99994 14.4913Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
