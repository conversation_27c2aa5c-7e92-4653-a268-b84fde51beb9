export interface IconComponentProps {
  dimensions?: {
    width: number;
    height: number;
  };
  size?: number;
  color?: string;
}

export type IconType =
  | "success"
  | "error"
  | "upload"
  | "arrow-down"
  | "arrow-up"
  | "text"
  | "clock"
  | "aspect-ratio"
  | "subtitles"
  | "info"
  | "add-video"
  | "close"
  | "locker"
  | "rocket"
  | "warning"
  | "warning-new"
  | "youtube-text"
  | "upload-2"
  | "video-file"
  | "delete-filled"
  | "delete-outline"
  | "more"
  | "bell"
  | "check"
  | "upload-new"
  | "time"
  | "link"
  | "x-mark"
  | "image"
  | "trash-can"
  | "user"
  | "heart-filled"
  | "heart-outlined"
  | "play"
  | "pause"
  | "volume-on"
  | "volume-off"
  | "edit"
  | "filter"
  | "sort"
  | "download"
  | "align-left"
  | "align-center"
  | "align-right"
  | "plus"
  | "minus"
  | "stroke"
  | "x"
  | "y"
  | "blur"
  | "snip"
  | "prev"
  | "next"
  | "crop"
  | "captions"
  | "search"
  | "merge"
  | "eye"
  | "eye-slash"
  | "boost"
  | "settings"
  | "logout"
  | "arrow-left"
  | "undo"
  | "redo";
