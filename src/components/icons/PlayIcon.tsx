import { IconComponentProps } from "./types";

export default function PlayIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M14.5 8C14.5 6.27609 13.8152 4.62279 12.5962 3.40381C11.3772 2.18482 9.72391 1.5 8 1.5C6.27609 1.5 4.62279 2.18482 3.40381 3.40381C2.18482 4.62279 1.5 6.27609 1.5 8C1.5 9.72391 2.18482 11.3772 3.40381 12.5962C4.62279 13.8152 6.27609 14.5 8 14.5C9.72391 14.5 11.3772 13.8152 12.5962 12.5962C13.8152 11.3772 14.5 9.72391 14.5 8ZM0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8ZM5.88438 4.59688C6.12188 4.46563 6.40938 4.46875 6.64375 4.6125L11.1438 7.3625C11.3656 7.5 11.5031 7.74063 11.5031 8.00313C11.5031 8.26562 11.3656 8.50625 11.1438 8.64375L6.64375 11.3938C6.4125 11.5344 6.12188 11.5406 5.88438 11.4094C5.64687 11.2781 5.5 11.0281 5.5 10.7563V5.25625C5.5 4.98438 5.64687 4.73438 5.88438 4.60313V4.59688Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
