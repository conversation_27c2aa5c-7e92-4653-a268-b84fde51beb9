import { IconComponentProps } from "./types";

export default function BellIcon({
  color = "#9f4c4c",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        fillRule="evenodd"
        fill={color}
        d="M6.98847 0C6.4362 0 5.99002 0.446875 5.99002 1V1.6C3.7123 2.0625 1.99621 4.08125 1.99621 6.5V7.29375C1.99621 8.7125 1.51258 10.0906 0.629576 11.1969L0.164671 11.7812C-0.0162985 12.0063 -0.0506203 12.3156 0.0741863 12.575C0.198993 12.8344 0.461087 13 0.748142 13H13.2288C13.5159 13 13.778 12.8344 13.9028 12.575C14.0276 12.3156 13.9932 12.0063 13.8123 11.7812L13.3474 11.2C12.4644 10.0906 11.9807 8.7125 11.9807 7.29375V6.5C11.9807 4.08125 10.2646 2.0625 7.98692 1.6V1C7.98692 0.446875 7.54074 0 6.98847 0ZM6.98847 3C8.91985 3 10.4831 4.56563 10.4831 6.5V7.29375C10.4831 8.79062 10.9168 10.25 11.7218 11.5H2.25518C3.06018 10.25 3.49389 8.79062 3.49389 7.29375V6.5C3.49389 4.56563 5.05709 3 6.98847 3ZM8.98538 14H6.98847H4.99157C4.99157 14.5312 5.20062 15.0406 5.57504 15.4156C5.94946 15.7906 6.45804 16 6.98847 16C7.5189 16 8.02749 15.7906 8.40191 15.4156C8.77633 15.0406 8.98538 14.5312 8.98538 14Z"
      />
    </svg>
  );
}
