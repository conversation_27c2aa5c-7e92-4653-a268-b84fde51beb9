import { IconComponentProps } from "./types";

export default function DownloadIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 12 12"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M10.6739 7.17361H8.94727L7.98375 8.1383H10.6739C10.8792 8.1383 11.0463 8.30728 11.0463 8.51494V10.6581C11.0463 10.8657 10.8792 11.0347 10.6739 11.0347H1.32609C1.12078 11.0347 0.953672 10.8657 0.953672 10.6581V8.51494C0.953672 8.30728 1.12078 8.1383 1.32609 8.1383H4.01625L3.05273 7.17361H1.32609C0.594844 7.17361 0 7.77525 0 8.51494V10.6581C0 11.3977 0.594844 11.9994 1.32609 11.9994H10.6739C11.4052 11.9994 12 11.3977 12 10.6581V8.51494C12 7.77525 11.4052 7.17361 10.6739 7.17361Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M5.58914 8.64994L5.65969 8.72048C5.75133 8.81189 5.87133 8.85783 5.99133 8.85783C6.03305 8.85783 6.07477 8.8522 6.11508 8.84119C6.11859 8.84025 6.12187 8.83931 6.12516 8.83837C6.195 8.81915 6.25852 8.784 6.31125 8.73689C6.31336 8.73501 6.31523 8.73314 6.31711 8.73126L6.34687 8.70173L6.34828 8.70009L9.48375 5.56064C9.6668 5.37736 9.6668 5.08017 9.48375 4.89712C9.3007 4.71408 9.00398 4.71384 8.8207 4.89712L6.46875 7.25189V0.468843C6.46875 0.209858 6.25898 -0.000610352 6 -0.000610352C5.74102 -0.000610352 5.53125 0.209624 5.53125 0.468608V7.26408L3.15609 4.88611C2.97305 4.70283 2.67633 4.70283 2.49328 4.88611C2.31023 5.06939 2.31023 5.36658 2.49328 5.54962L5.58914 8.64947V8.64994Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M9.5182 10.1298C9.81786 10.1298 10.0608 9.88654 10.0608 9.5865C10.0608 9.28645 9.81786 9.04322 9.5182 9.04322C9.21854 9.04322 8.97563 9.28645 8.97563 9.5865C8.97563 9.88654 9.21854 10.1298 9.5182 10.1298Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
