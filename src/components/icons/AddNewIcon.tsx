interface AddNewIconProps {
  fill: string;
}

export default function AddNewIcon({ fill }: AddNewIconProps) {
  return (
    <svg
      width="17"
      height="17"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.5 4.75V12.25M4.75 8.5H12.25"
        stroke={fill}
        strokeWidth={"1.2"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.5 16C12.6421 16 16 12.6421 16 8.5C16 4.35786 12.6421 1 8.5 1C4.35786 1 1 4.35786 1 8.5C1 12.6421 4.35786 16 8.5 16Z"
        stroke={fill}
        strokeWidth={"1.2"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
