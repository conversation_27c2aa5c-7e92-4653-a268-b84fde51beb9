import { IconComponentProps } from "./types";

export default function XMarkIcon({
  color = "#E1E1E1",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 10 10"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.27986 5L9.73505 1.54481C10.0883 1.19154 10.0883 0.618575 9.73505 0.264949C9.38179 -0.0883162 8.80882 -0.0883162 8.45519 0.264949L5 3.72014L1.54481 0.264949C1.19154 -0.0883162 0.618575 -0.0883162 0.264949 0.264949C-0.0883162 0.618213 -0.0883162 1.19118 0.264949 1.54481L3.72014 5L0.264949 8.45519C-0.0883162 8.80845 -0.0883162 9.38142 0.264949 9.73505C0.441581 9.91168 0.67323 10 0.904879 10C1.13653 10 1.36818 9.91168 1.54481 9.73505L5 6.27986L8.45519 9.73505C8.63182 9.91168 8.86347 10 9.09512 10C9.32677 10 9.55842 9.91168 9.73505 9.73505C10.0883 9.38179 10.0883 8.80882 9.73505 8.45519L6.27986 5Z"
        fill={color}
      />
    </svg>
  );
}
