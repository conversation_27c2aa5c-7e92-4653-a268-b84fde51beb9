import { IconComponentProps } from "./types";

export default function BlurIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 12 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M5.80101 0.0158013C6.24408 -0.0513792 6.50287 0.099025 6.79389 0.402842C7.2873 0.918227 7.83812 1.74445 8.26608 2.33905C9.71915 4.35747 12.1681 7.95113 11.9909 10.4849C11.6153 15.8604 4.64198 17.9711 1.29479 13.7868C-1.419 10.3927 0.668456 6.88126 2.67838 3.811C3.34399 2.79427 4.34089 1.29223 5.15856 0.427909C5.35089 0.224362 5.50496 0.0609226 5.8 0.0158013H5.80101ZM9.21163 13.1942C11.5015 10.896 10.2488 8.28403 8.86725 5.94173C8.02038 4.50487 7.00938 3.12315 5.98931 1.80461C4.87157 3.21941 3.78605 4.75554 2.89186 6.32275C1.758 8.30809 0.838634 10.0899 2.08829 12.2938C3.52323 14.8236 7.16345 15.2487 9.21163 13.1932V13.1942Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M3.64407 9.02C3.99852 8.98591 4.34694 9.2065 4.43555 9.55645C4.59465 10.1811 4.4859 10.5992 5.05987 11.0856C5.54121 11.4937 5.83222 11.4064 6.36894 11.5318C7.28126 11.7463 7.12317 12.9967 6.1897 13.0147C5.06189 13.0368 3.81324 12.2627 3.32788 11.259C3.03485 10.6534 2.62199 9.11827 3.64306 9.019L3.64407 9.02Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}

<svg
  width="12"
  height="16"
  viewBox="0 0 12 16"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
></svg>;
