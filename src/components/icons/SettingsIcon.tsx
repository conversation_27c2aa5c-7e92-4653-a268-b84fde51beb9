import { IconComponentProps } from "./types";

export default function SettingsIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M15.5476 9.10227C15.1967 8.74632 14.1797 8.22293 14.6615 7.65341C14.9621 7.29947 15.5612 6.98062 15.8262 6.56752C16.2871 5.84961 15.6607 4.68751 15.269 4.01573C14.8532 3.30283 14.1598 2.21192 13.1952 2.24501C12.6097 2.26607 11.3654 3.04213 11.0532 2.34828C10.7799 1.74167 10.9035 0.815203 10.2394 0.376034C9.69687 0.0170778 8.17606 -0.0280424 7.51515 0.0120644C7.00507 0.0431472 6.10851 0.123361 5.72202 0.44622C5.13024 0.939534 5.24546 1.77175 4.96894 2.37636C4.68824 2.98999 3.46594 2.32121 2.94015 2.26105C1.77754 2.1287 0.81499 3.83825 0.409649 4.71559C0.0137354 5.57588 -0.266965 6.13737 0.495536 6.90241C0.844317 7.25234 1.7964 7.74465 1.38896 8.30414C0.971051 8.87767 -0.0124494 9.21457 0.000119342 10.0749C0.0116406 10.891 1.4518 13.3636 2.27191 13.6564C3.04384 13.9311 3.61048 13.4699 4.3248 13.3656C4.60864 13.3245 4.84535 13.3556 4.96894 13.6253C5.2486 14.2359 5.14281 15.1875 5.80476 15.6266C6.56098 16.127 9.57642 16.137 10.297 15.5805C10.9097 15.1073 10.783 14.254 11.0532 13.6534C11.3538 12.9876 12.5144 13.6734 13.0527 13.7406C14.2865 13.896 15.2365 12.1775 15.6502 11.251C16.0116 10.4428 16.2514 9.81717 15.5476 9.10227ZM13.014 8.3262C13.104 9.17346 13.9461 9.6016 14.4154 10.2172C14.2342 10.6043 14.0655 11.0033 13.8393 11.3683C13.7597 11.4946 13.3062 12.1394 13.2213 12.1805C13.0611 12.2577 12.1132 11.8997 11.8231 11.8666C10.9338 11.7684 10.0205 12.2346 9.65916 13.0197C9.45492 13.4619 9.44026 13.9803 9.22031 14.4004C8.91761 14.5478 6.86053 14.5377 6.75265 14.3934C6.57041 13.3436 6.3934 12.4412 5.26221 11.994C4.3625 11.639 3.73512 12.0591 2.86055 12.1955C2.61231 12.1333 1.57644 10.3837 1.61729 10.135L2.43111 9.4512C3.60524 8.21591 2.96738 6.76505 1.66861 5.92782C1.62043 5.88671 1.62358 5.84459 1.62986 5.78844C1.64557 5.63905 1.93779 5.08056 2.02996 4.91612C2.13051 4.73564 2.68458 3.88438 2.82389 3.82321C2.98728 3.75102 3.99487 4.12802 4.31328 4.14808C5.07054 4.19821 5.96606 3.7801 6.33474 3.13438C6.60183 2.66713 6.60287 2.0906 6.7736 1.59327C7.34233 1.50203 7.97077 1.46092 8.54683 1.49602C8.73222 1.50705 9.21088 1.53412 9.29153 1.68452C9.42455 1.93318 9.47377 2.46259 9.57851 2.75938C9.90949 3.69788 10.8322 4.27241 11.8754 4.13605C12.154 4.09995 13.0066 3.75704 13.1847 3.80617C13.7513 4.37167 14.1252 5.08958 14.4154 5.81752C13.5638 6.63069 12.8757 7.02975 13.014 8.3262Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M7.72463 5.01438C3.67332 5.3964 4.19702 11.253 8.23052 11.0013C12.3604 10.7446 11.8671 4.62334 7.72463 5.01438ZM8.22633 9.49632C6.11898 9.72392 5.85818 6.79412 7.72149 6.51237C9.90425 6.18249 10.231 9.27974 8.22633 9.49632Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
