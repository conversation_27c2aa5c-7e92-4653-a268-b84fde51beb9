import { IconComponentProps } from "./types";

export default function PauseIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M14.5 8C14.5 6.27609 13.8152 4.62279 12.5962 3.40381C11.3772 2.18482 9.72391 1.5 8 1.5C6.27609 1.5 4.62279 2.18482 3.40381 3.40381C2.18482 4.62279 1.5 6.27609 1.5 8C1.5 9.72391 2.18482 11.3772 3.40381 12.5962C4.62279 13.8152 6.27609 14.5 8 14.5C9.72391 14.5 11.3772 13.8152 12.5962 12.5962C13.8152 11.3772 14.5 9.72391 14.5 8ZM0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8ZM7 5.75V10.25C7 10.6656 6.66563 11 6.25 11C5.83437 11 5.5 10.6656 5.5 10.25V5.75C5.5 5.33437 5.83437 5 6.25 5C6.66563 5 7 5.33437 7 5.75ZM10.5 5.75V10.25C10.5 10.6656 10.1656 11 9.75 11C9.33438 11 9 10.6656 9 10.25V5.75C9 5.33437 9.33438 5 9.75 5C10.1656 5 10.5 5.33437 10.5 5.75Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
