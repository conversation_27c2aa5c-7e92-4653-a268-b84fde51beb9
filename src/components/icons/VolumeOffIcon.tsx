import { IconComponentProps } from "./types";

export default function VolumeOffIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 13"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M8.28531 0.0534833C8.06031 -0.0462042 7.7975 -0.00432914 7.615 0.160671L4.05656 3.37661H1.63656C0.734062 3.37661 0 4.11067 0 5.01286V6.97786C0 7.88005 0.734062 8.61442 1.63656 8.61442H4.24L7.59219 11.9663C7.71125 12.0857 7.87062 12.1488 8.03281 12.1488C8.11281 12.1488 8.19375 12.1332 8.27094 12.1013C8.50375 12.005 8.65531 11.7779 8.65531 11.526V0.622858C8.65531 0.376608 8.51031 0.153483 8.28531 0.0534833ZM7.40969 10.0222L4.93844 7.55098C4.82156 7.43442 4.66313 7.3688 4.49813 7.3688H1.63656C1.42094 7.3688 1.24563 7.19317 1.24563 6.97786V5.01286C1.24563 4.79755 1.42094 4.62223 1.63656 4.62223H4.29625C4.45062 4.62223 4.59937 4.56505 4.71375 4.46161L7.40969 2.02505V10.0222Z"
        fill={color}
        fillRule="evenodd"
      />
      <path
        d="M15.8175 7.58598C16.0609 7.82942 16.0609 8.2238 15.8175 8.46692C15.6959 8.58848 15.5366 8.64942 15.3772 8.64942C15.2178 8.64942 15.0584 8.58848 14.9369 8.46692L13.3813 6.9113L11.8256 8.46692C11.7041 8.58848 11.5447 8.64942 11.3853 8.64942C11.2259 8.64942 11.0666 8.58848 10.9447 8.46692C10.7016 8.2238 10.7016 7.82942 10.9447 7.58598L12.5003 6.03067L10.9447 4.47505C10.7016 4.23161 10.7016 3.83755 10.9447 3.59411C11.1881 3.35098 11.5825 3.35098 11.8256 3.59411L13.3813 5.14973L14.9369 3.59411C15.18 3.35098 15.5744 3.35098 15.8175 3.59411C16.0609 3.83755 16.0609 4.23161 15.8175 4.47505L14.2619 6.03067L15.8175 7.58598Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
