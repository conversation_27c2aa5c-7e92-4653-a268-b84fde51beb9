import { IconComponentProps } from "./types";

export default function XIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 10 12"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.187493 11.8165C-0.0754737 11.568 -0.0386248 11.2147 0.154831 10.9327L4.18224 6.01124L0.154831 1.05973C-0.307454 0.463037 0.373412 -0.31335 0.968018 0.138038L4.99961 5.00872L8.95249 0.187046C9.57306 -0.370095 10.3611 0.433804 9.81844 1.07692L5.81699 6.01209L9.80085 10.8922C10.3511 11.501 9.58394 12.3754 8.95165 11.8071L4.99878 6.98537L1.0727 11.7924C0.82146 12.059 0.453809 12.0701 0.186655 11.8182L0.187493 11.8165Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
