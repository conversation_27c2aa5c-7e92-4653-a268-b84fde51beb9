import { IconComponentProps } from "./types";

export default function EditIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      {/*
       */}
      <path
        d="M15.597 2.24994L13.7145 0.407488C13.132 -0.16281 12.1911 -0.15531 11.6177 0.42405L1.92998 10.2097C1.69968 10.4426 1.52155 10.7297 1.41468 11.04L0.0978249 14.8621C-0.010611 15.1762 0.0700128 15.5149 0.308447 15.7462C0.469382 15.9021 0.677191 15.9846 0.890313 15.9846C0.990936 15.9846 1.0925 15.9662 1.19125 15.9287L5.34962 14.3481C5.63899 14.2381 5.8968 14.0712 6.1168 13.8522L15.6067 4.40083C15.8948 4.11396 16.0523 3.73116 16.0504 3.32335C16.0488 2.91555 15.8879 2.53431 15.5973 2.25025L15.597 2.24994ZM5.22744 12.9584C5.134 13.0519 5.02431 13.1228 4.90181 13.169L1.57874 14.4325L2.60591 11.4513C2.65154 11.3185 2.72716 11.1963 2.82528 11.0972L3.08278 10.8375L3.24184 11.5603C3.35528 12.0756 3.73871 12.4913 4.24245 12.6459L5.23587 12.9506L5.22744 12.9587L5.22744 12.9584ZM6.24836 11.9416L4.61182 11.44C4.54182 11.4182 4.48838 11.3607 4.47245 11.2888L4.13839 9.77069L9.78549 4.06646L11.9651 6.24766L6.24805 11.9413L6.24836 11.9416Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M10.3264 6.53515L6.58054 10.2832C6.45773 10.4063 6.29648 10.4679 6.13492 10.4679C5.97336 10.4679 5.81243 10.4063 5.6893 10.2832C5.44337 10.0369 5.44337 9.63788 5.6893 9.39164L9.43487 5.6433C9.68081 5.39705 10.0802 5.39705 10.3261 5.6433C10.572 5.88954 10.572 6.2889 10.3261 6.53515L10.3264 6.53515Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
