import { IconComponentProps } from "./types";

export default function ArrowDownIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 10 6"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M9.40698 5.18436e-08C9.55879 3.85716e-08 9.71061 0.0619707 9.82636 0.185913C10.0579 0.433796 10.0579 0.835845 9.82636 1.08398L5.40859 5.81409C5.17708 6.06197 4.80158 6.06197 4.56982 5.81409L0.173636 1.1071C-0.0578789 0.859212 -0.057879 0.457163 0.173636 0.209026C0.405152 -0.0391117 0.780652 -0.0388581 1.0124 0.209026L4.98921 4.46698L8.98759 0.185913C9.10335 0.0619708 9.25516 6.51155e-08 9.40698 5.18436e-08Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
