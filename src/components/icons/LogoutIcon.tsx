import { IconComponentProps } from "./types";

export default function LogoutIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M13.4456 7.74718H5.68404C5.33794 7.74718 5.0645 7.35315 5.05685 7.02765C5.04882 6.68235 5.31155 6.25673 5.68404 6.25673H13.4456L10.7242 3.52557C10.1589 2.79463 10.99 1.91787 11.7453 2.45961L15.767 6.45507C16.0775 6.8472 16.0779 7.15671 15.767 7.54883C14.6858 8.91365 12.9205 10.1643 11.7732 11.5226C11.045 12.0834 10.2052 11.2934 10.6786 10.5328L13.4456 7.74718Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M2.55381 0.0611874C3.18712 -0.00315125 4.71954 -0.0317039 5.32991 0.0509084C6.21524 0.170829 6.24699 1.34415 5.41213 1.51509C4.58989 1.68336 2.92553 1.32702 2.24518 1.64186C1.84171 1.82879 1.51702 2.35834 1.51435 2.80224C1.66082 5.52959 1.32581 8.44767 1.51396 11.1526C1.64973 13.1052 3.95352 12.3175 5.28478 12.4667C6.24469 12.5745 6.24966 13.7992 5.37274 13.9454C4.78417 14.0436 2.75535 14.0029 2.1729 13.875C1.10361 13.6397 0.241602 12.7134 0.0825087 11.6334C0.214066 8.6738 -0.140451 5.48809 0.0664464 2.55364C0.159378 1.23337 1.24397 0.194433 2.55381 0.0611874Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
