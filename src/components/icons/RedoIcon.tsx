import { IconComponentProps } from "./types";

export default function RedoIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 15"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M2.25098 2.32566C3.70257 0.923134 5.63261 0.150885 7.68515 0.150885C9.30456 0.150885 10.8533 0.631812 12.1643 1.54173C13.2984 2.32873 14.1768 3.37909 14.7287 4.60153V0.613988C14.7287 0.274728 15.0134 0 15.3644 0C15.7153 0 16 0.275035 16 0.613988V5.68078C16 6.02004 15.7153 6.29476 15.3644 6.29476H10.1955C9.84457 6.29476 9.55987 6.01973 9.55987 5.68078C9.55987 5.34151 9.84457 5.06679 10.1955 5.06679H13.5515C12.5321 2.84315 10.2324 1.37917 7.68484 1.37917C4.14821 1.37917 1.27096 4.15902 1.27096 7.5756C1.27096 10.9922 4.14821 13.772 7.68484 13.772C9.1533 13.772 10.5349 13.3055 11.6805 12.4233C11.9549 12.2119 12.3549 12.2555 12.5737 12.5204C12.7924 12.7856 12.7474 13.1719 12.4731 13.3833C11.1002 14.441 9.44425 15 7.68484 15C5.63199 15 3.70195 14.2278 2.25036 12.8252C0.799391 11.4233 0 9.55861 0 7.5756C0 5.59227 0.799391 3.72787 2.25098 2.32566Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
