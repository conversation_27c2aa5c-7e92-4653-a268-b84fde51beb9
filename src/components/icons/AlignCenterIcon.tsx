import { IconComponentProps } from "./types";

export default function AlignCenterIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 13"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.65168 3.86135L13.2632 3.84873C14.261 3.95653 14.2301 5.2 13.2632 5.29387L0.739454 5.29308C-0.188323 5.25522 -0.271448 3.99098 0.65168 3.86135Z"
        fill={color}
      />
      <path
        d="M0.65168 11.5675L13.2632 11.5549C14.261 11.6627 14.2301 12.9061 13.2632 13L0.739454 12.9992C-0.188323 12.9613 -0.271448 11.6971 0.65168 11.5675Z"
        fill={color}
      />
      <path
        d="M3.65759 0.00841424L10.303 0C11.257 0.124899 11.1982 1.36968 10.2573 1.44094L3.69997 1.43542C2.80856 1.3589 2.75442 0.135417 3.65732 0.00841424H3.65759Z"
        fill={color}
      />
      <path
        d="M3.65759 7.71454L10.303 7.70613C11.257 7.83103 11.1982 9.07581 10.2573 9.14707L3.69997 9.14155C2.80856 9.06503 2.75442 7.84155 3.65732 7.71454H3.65759Z"
        fill={color}
      />
    </svg>
  );
}
