import { IconComponentProps } from "./types";

export default function FilterIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 15"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M1.10807 0.0147029L14.7653 1.52588e-05C15.7138 0.0815782 16.2113 0.911895 15.9144 1.80346C14.2019 4.1166 12.285 6.29005 10.4928 8.54818L10.4841 13.1954C10.3306 13.8951 9.63437 14.2235 8.99561 13.8804C7.99592 13.3435 6.91717 12.072 5.90216 11.4666C5.17591 10.7366 5.71935 9.40506 5.46997 8.49443C3.90965 6.5113 2.21964 4.62066 0.670884 2.6294C0.484633 2.38971 0.106818 1.98409 0.0433804 1.70502C-0.140371 0.896895 0.268069 0.142204 1.10839 0.0153279L1.10807 0.0147029ZM14.2878 1.50377H1.7212L6.8881 7.87693C6.94185 7.95912 6.96748 8.03818 6.97935 8.13631C7.02842 8.54475 6.89092 10.2113 7.00373 10.4151L9.03093 11.9676V8.03756C9.03093 7.93381 9.39374 7.50349 9.4828 7.38787L14.2881 1.50409L14.2878 1.50377Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
