import { IconComponentProps } from "./types";

export default function HeartOutlinedIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M7.05625 13.6228L6.97813 13.551L1.50312 8.46982C0.54375 7.57976 0 6.33056 0 5.02202V4.91896C0 2.72036 1.5625 0.834059 3.725 0.421821C4.95625 0.184473 6.21562 0.468666 7.21875 1.17447C7.5 1.37434 7.7625 1.60544 8 1.8709C8.13125 1.72099 8.27188 1.58358 8.42188 1.45554C8.5375 1.3556 8.65625 1.26191 8.78125 1.17447C9.78438 0.468666 11.0437 0.184473 12.275 0.418698C14.4375 0.830936 16 2.72036 16 4.91896V5.02202C16 6.33056 15.4563 7.57976 14.4969 8.46982L9.02188 13.551L8.94375 13.6228C8.6875 13.8601 8.35 13.9944 8 13.9944C7.65 13.9944 7.3125 13.8633 7.05625 13.6228ZM7.47188 3.52922C7.45938 3.51985 7.45 3.50736 7.44063 3.49486L6.88438 2.87026L6.88125 2.86714C6.15937 2.05828 5.06875 1.68976 4.00625 1.89276C2.55 2.17071 1.5 3.43865 1.5 4.91896V5.02202C1.5 5.91207 1.87188 6.76466 2.525 7.37052L8 12.4517L13.475 7.37052C14.1281 6.76466 14.5 5.91207 14.5 5.02202V4.91896C14.5 3.44177 13.45 2.17071 11.9969 1.89276C10.9344 1.68976 9.84062 2.0614 9.12187 2.86714C9.12187 2.86714 9.12187 2.86714 9.11875 2.87026C9.11562 2.87338 9.11875 2.87026 9.11563 2.87338L8.55937 3.49799C8.55 3.51048 8.5375 3.51985 8.52812 3.53234C8.3875 3.67288 8.19687 3.75095 8 3.75095C7.80312 3.75095 7.6125 3.67288 7.47188 3.53234V3.52922Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
