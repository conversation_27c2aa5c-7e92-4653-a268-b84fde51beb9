import { IconComponentProps } from "./types";

export default function BoostIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 40 68"
      preserveAspectRatio="xMidYMid meet"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <g fillRule="nonzero" stroke="none" strokeWidth="1">
        {/* Main arrow shape */}
        <path
          fill="#7852FF"
          d="M29.9070282,26 L27.9977363,66.09513 C27.9469564,67.16151 27.0675853,68 26,68 L14,68 C12.9324147,68 12.0530436,67.16151 12.0022637,66.09513 L10.0929718,26 L2,26 C0.312746915,26 -0.616346954,24.03939 0.452085446,22.73352 L18.4520854,0.733523615 C19.2522939,-0.244507872 20.7477061,-0.244507872 21.5479146,0.733523615 L39.5479146,22.73352 C40.6163470,24.03939 39.6872531,26 38,26 L29.9070282,26 Z"
        />
        {/* Right side element */}
        <path
          fill="#00D6FD"
          d="M37.9958506,66.12876 C37.9247359,67.23104 36.9735133,68.06697 35.8712354,67.99585 C34.7689576,67.92474 33.9330347,66.97351 34.0041494,65.87124 L36.0041494,34.87124 C36.0752641,33.76896 37.0264867,32.93303 38.1287646,33.00415 C39.2310424,33.07526 40.0669653,34.02649 39.9958506,35.12876 L37.9958506,66.12876 Z"
        />
        {/* Left side element */}
        <path
          fill="#00D6FD"
          d="M5.99585064,65.87124 C6.06696534,66.97351 5.23104243,67.92474 4.12876459,67.99585 C3.02648674,68.06697 2.07526412,67.23104 2.00414942,66.12876 L0.00414942308,35.12876 C-0.0669652769,34.02649 0.768957629,33.07526 1.87123547,33.00415 C2.97351331,32.93303 3.92473593,33.76896 3.99585064,34.87124 L5.99585064,65.87124 Z"
        />
      </g>
    </svg>
  );
}