import { IconComponentProps } from "./types";

export default function MergeIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.5 2.61875C0.19375 2.34375 0 1.94375 0 1.5C0 0.671875 0.671875 0 1.5 0C1.94375 0 2.34375 0.19375 2.61875 0.5H13.3813C13.6562 0.19375 14.0562 0 14.5 0C15.3281 0 16 0.671875 16 1.5C16 1.94375 15.8062 2.34375 15.5 2.61875V11.3813C15.8062 11.6562 16 12.0562 16 12.5C16 13.3281 15.3281 14 14.5 14C14.0562 14 13.6562 13.8062 13.3813 13.5H2.61875C2.34375 13.8062 1.94375 14 1.5 14C0.671875 14 0 13.3281 0 12.5C0 12.0562 0.19375 11.6562 0.5 11.3813V2.61875ZM2.91563 2C2.76562 2.425 2.42812 2.7625 2 2.91563V11.0875C2.425 11.2375 2.7625 11.575 2.91563 12.0031H13.0875C13.2375 11.5781 13.575 11.2406 14.0031 11.0875V2.91563C13.5781 2.76562 13.2406 2.42812 13.0875 2H2.91563ZM3 4C3 3.44688 3.44688 3 4 3H8C8.55313 3 9 3.44688 9 4V7C9 7.55312 8.55313 8 8 8H4C3.44688 8 3 7.55312 3 7V4ZM7 9H8C9.10312 9 10 8.10312 10 7V6H12C12.5531 6 13 6.44688 13 7V10C13 10.5531 12.5531 11 12 11H8C7.44688 11 7 10.5531 7 10V9Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
