import { IconComponentProps } from "./types";

export default function CropIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M2.49684 0.662517C2.62117 -0.201439 3.87351 -0.258635 3.99182 0.717706L4.0229 11.8528C4.05098 11.8829 4.15826 12.0043 4.1723 12.0043H11.0005V13.5065H4.07304C3.36214 13.5065 2.49684 12.6415 2.49684 11.9291V3.99191H0.670973C0.330064 3.99191 0.00920938 3.58853 0.000185337 3.26141C-0.00883871 2.93429 0.313019 2.48977 0.670973 2.48977H2.49684C2.55499 1.92484 2.41863 1.20838 2.49684 0.662517Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M13.5032 12.0043H15.329C15.6699 12.0043 15.9908 12.4077 15.9998 12.7348C16.0088 13.0619 15.687 13.5065 15.329 13.5065H13.5032V15.3337C13.5032 15.41 13.3508 15.7271 13.2856 15.7923C12.8424 16.2368 12.0192 15.9227 12.0072 15.2775L11.9761 4.14242C11.948 4.11232 11.8407 3.9909 11.8267 3.9909H4.9985V2.48877H11.927C12.6389 2.48877 13.5032 3.35473 13.5032 4.06616V12.0043Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
