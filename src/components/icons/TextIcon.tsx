import { IconComponentProps } from "./types";

export default function TextIcon({
  color = "#000000",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions?.width || size}
      height={dimensions?.height || size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 4V20M17 12V20M6 20H10M15 20H19M13 7V4H3V7M21 14V12H13V14"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
