import { IconComponentProps } from "./types";

export default function AlignRightIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 13"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.651667 3.8645L13.2629 3.85186C14.2607 3.95957 14.2298 5.20253 13.2629 5.29654L0.739439 5.29575C-0.188319 5.25783 -0.271443 3.99406 0.651667 3.8645Z"
        fill={color}
      />
      <path
        d="M0.651667 11.568L13.2629 11.5553C14.2607 11.663 14.2298 12.906 13.2629 13L0.739439 12.9992C-0.188319 12.9613 -0.271443 11.6975 0.651667 11.568Z"
        fill={color}
      />
      <path
        d="M5.66151 0.0126403L13.2632 0C14.261 0.107706 14.2301 1.35067 13.2632 1.44468L5.74928 1.44389C4.82152 1.40597 4.7384 0.142203 5.66151 0.0126403Z"
        fill={color}
      />
      <path
        d="M5.66151 7.71636L13.2632 7.70372C14.261 7.81143 14.2301 9.05439 13.2632 9.1484L5.74928 9.14761C4.82152 9.10969 4.7384 7.84593 5.66151 7.71636Z"
        fill={color}
      />
    </svg>
  );
}
