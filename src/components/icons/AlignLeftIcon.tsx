import { IconComponentProps } from "./types";

export default function AlignLeftIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 13"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.65168 3.86445L13.2632 3.85183C14.261 3.95957 14.2301 5.20252 13.2632 5.2966L0.739454 5.29581C-0.188323 5.25797 -0.271448 3.99426 0.65168 3.86471V3.86445Z"
        fill={color}
      />
      <path
        d="M0.65168 11.5678L13.2632 11.5552C14.261 11.663 14.2301 12.9059 13.2632 13L0.739454 12.9992C-0.188323 12.9614 -0.271448 11.6977 0.65168 11.5681V11.5678Z"
        fill={color}
      />
      <path
        d="M0.65168 0.0126135L8.25354 0C9.25132 0.10774 9.22042 1.35069 8.25354 1.44477L0.739454 1.44398C-0.188323 1.40614 -0.271448 0.142427 0.65168 0.0126135Z"
        fill={color}
      />
      <path
        d="M0.65168 7.71602L8.25354 7.7034C9.25132 7.81114 9.22042 9.05409 8.25354 9.14817L0.739454 9.14738C-0.188323 9.10954 -0.271448 7.84583 0.65168 7.71628V7.71602Z"
        fill={color}
      />
    </svg>
  );
}
