import { IconComponentProps } from "./types";

export default function NextIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M9.95625 7.3625L5.45625 4.6125C5.22188 4.46875 4.93438 4.46563 4.69688 4.59688V4.60313C4.45937 4.73438 4.3125 4.98438 4.3125 5.25625V10.7563C4.3125 11.0281 4.45937 11.2781 4.69688 11.4094C4.93438 11.5406 5.225 11.5344 5.45625 11.3938L9.95625 8.64375C10.1781 8.50625 10.3156 8.26562 10.3156 8.00313C10.3156 7.74063 10.1781 7.5 9.95625 7.3625Z"
        fill={color}
        fillRule="evenodd"
      />
      <path
        d="M8 0C3.58187 0 0 3.58187 0 8C0 12.4181 3.58187 16 8 16C12.4181 16 16 12.4181 16 8C16 3.58187 12.4181 0 8 0ZM8 14.5C4.41 14.5 1.5 11.59 1.5 8C1.5 4.41 4.41 1.5 8 1.5C11.59 1.5 14.5 4.41 14.5 8C14.5 11.59 11.59 14.5 8 14.5Z"
        fill={color}
        fillRule="evenodd"
      />
      <path
        d="M10.9478 4.50156C10.6025 4.50156 10.3228 4.73906 10.3228 5.03188V10.9734C10.3228 11.2662 10.6025 11.5037 10.9478 11.5037C11.2931 11.5037 11.5728 11.2662 11.5728 10.9734V5.03188C11.5728 4.73906 11.2931 4.50156 10.9478 4.50156Z"
        fill={color}
        fillRule="evenodd"
      />
    </svg>
  );
}
