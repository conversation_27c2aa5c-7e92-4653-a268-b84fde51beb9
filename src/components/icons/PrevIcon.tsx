import { IconComponentProps } from "./types";

export default function PrevIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M6.04375 7.3625L10.5437 4.6125C10.7781 4.46875 11.0656 4.46563 11.3031 4.59688V4.60313C11.5406 4.73438 11.6875 4.98438 11.6875 5.25625V10.7563C11.6875 11.0281 11.5406 11.2781 11.3031 11.4094C11.0656 11.5406 10.775 11.5344 10.5437 11.3938L6.04375 8.64375C5.82187 8.50625 5.68437 8.26562 5.68437 8.00313C5.68437 7.74063 5.82187 7.5 6.04375 7.3625Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M0 8C0 12.4181 3.58187 16 8 16C12.4181 16 16 12.4181 16 8C16 3.58187 12.4181 0 8 0C3.58187 0 0 3.58187 0 8ZM1.5 8C1.5 4.41 4.41031 1.5 8 1.5C11.5897 1.5 14.5 4.41 14.5 8C14.5 11.59 11.5897 14.5 8 14.5C4.41031 14.5 1.5 11.59 1.5 8Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M5.05219 4.50156C5.3975 4.50156 5.67719 4.73906 5.67719 5.03188V10.9734C5.67719 11.2662 5.3975 11.5037 5.05219 11.5037C4.70687 11.5037 4.42719 11.2662 4.42719 10.9734V5.03188C4.42719 4.73906 4.70687 4.50156 5.05219 4.50156Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
