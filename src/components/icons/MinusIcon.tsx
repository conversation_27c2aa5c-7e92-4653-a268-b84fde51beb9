import { IconComponentProps } from "./types";

export default function MinusIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 14 2"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0 1C0 0.447667 0.367227 0 0.820312 0H13.1797C13.6328 0 14 0.447667 14 1C14 1.55233 13.6328 2 13.1797 2H0.820312C0.367227 2 0 1.55233 0 1Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
