import { IconComponentProps } from "./types";

export default function MoreIcon({
  color = "#E1E1E1",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 4 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M1.875 9.875C2.91053 9.875 3.75 9.03553 3.75 8C3.75 6.96447 2.91053 6.125 1.875 6.125C0.839466 6.125 0 6.96447 0 8C0 9.03553 0.839466 9.875 1.875 9.875Z"
        fill={color}
      />
      <path
        d="M1.875 3.75C2.91053 3.75 3.75 2.91053 3.75 1.875C3.75 0.839466 2.91053 0 1.875 0C0.839466 0 0 0.839466 0 1.875C0 2.91053 0.839466 3.75 1.875 3.75Z"
        fill={color}
      />
      <path
        d="M1.875 16C2.91053 16 3.75 15.1605 3.75 14.125C3.75 13.0895 2.91053 12.25 1.875 12.25C0.839466 12.25 0 13.0895 0 14.125C0 15.1605 0.839466 16 1.875 16Z"
        fill={color}
      />
    </svg>
  );
}
