import { IconComponentProps } from "./types";

export default function StrokeIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 10"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M15.2625 1.43478H0.7375C0.330313 1.43478 0 1.11354 0 0.717238C0 0.320934 0.330313 0 0.7375 0H15.2622C15.6697 0 15.9997 0.321238 15.9997 0.717238C15.9997 1.11324 15.6694 1.43448 15.2622 1.43448L15.2625 1.43478Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M15.0378 5.44919H0.962188C0.430625 5.44919 0 5.03009 0 4.51343C0 3.99678 0.430938 3.57768 0.962188 3.57768H15.0378C15.5694 3.57768 16 3.99678 16 4.51343C16 5.03009 15.5691 5.44919 15.0378 5.44919Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M14.7928 10H1.20719C0.540313 10 0 9.47423 0 8.82598C0 8.17773 0.540625 7.65196 1.20719 7.65196H14.7925C15.4594 7.65196 15.9997 8.17773 15.9997 8.82598C15.9997 9.47423 15.4591 10 14.7925 10H14.7928Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
