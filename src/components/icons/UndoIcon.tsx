import { IconComponentProps } from "./types";

export default function UndoIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M13.7488 3.32566C12.2973 1.92313 10.3673 1.15089 8.31484 1.15089C6.69549 1.15089 5.14677 1.63181 3.83586 2.54173C2.70182 3.32873 1.8234 4.37909 1.27154 5.60153V1.61399C1.27123 1.27503 0.986543 1 0.635613 1C0.284682 1 0 1.27503 0 1.61399V6.68078C0 7.02004 0.284682 7.29476 0.635613 7.29476H5.80426C6.15519 7.29476 6.43987 7.01973 6.43987 6.68078C6.43987 6.34182 6.15519 6.06679 5.80426 6.06679H2.44839C3.46774 3.84315 5.76739 2.37917 8.31484 2.37917C11.8513 2.37917 14.7285 5.15902 14.7285 8.5756C14.7285 11.9922 11.8513 14.772 8.31484 14.772C6.84643 14.772 5.46489 14.3055 4.31929 13.4233C4.04492 13.2119 3.64493 13.2555 3.42618 13.5204C3.20744 13.7856 3.25244 14.1719 3.52681 14.3833C4.89959 15.441 6.5555 16 8.31484 16C10.3676 16 12.2976 15.2278 13.7491 13.8252C15.2006 12.423 16 10.5586 16 8.57529C16 6.59197 15.2006 4.72757 13.7491 3.32535L13.7488 3.32566Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
