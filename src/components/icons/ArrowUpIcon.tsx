import { IconComponentProps } from "./types";

export default function ArrowUpIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 10 6"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.593021 6C0.441208 6 0.289394 5.93803 0.173637 5.81409C-0.0578789 5.5662 -0.0578789 5.16416 0.173637 4.91602L4.59141 0.185913C4.82292 -0.0619709 5.19842 -0.0619709 5.43018 0.185913L9.82636 4.89291C10.0579 5.14079 10.0579 5.54284 9.82636 5.79098C9.59485 6.03911 9.21935 6.03886 8.98759 5.79098L5.01079 1.53302L1.01241 5.81409C0.896648 5.93803 0.744835 6 0.593021 6Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
