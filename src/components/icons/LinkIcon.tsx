import { IconComponentProps } from "./types";

export default function LinkIcon({
  color = "#E1E1E1",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14" // Match the path's natural dimensions
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M6.36248 2.60155C9.17029 2.33155 11.3403 4.96561 10.4009 7.6553C10.26 8.05873 9.71123 9.04061 9.32685 9.20936C8.81779 9.4328 8.34529 9.01123 8.45904 8.46936C8.50404 8.2553 8.84654 7.92592 8.97592 7.71467C10.1384 5.81561 8.51748 3.53748 6.32654 3.87998C5.25217 4.0478 4.49935 5.11655 3.78529 5.83311C3.19435 6.42592 2.44873 7.07311 1.91685 7.69592C-0.0484576 9.99873 2.75436 13.1525 5.29904 11.4578C5.4531 11.3553 5.68529 11.1047 5.83092 11.0569C6.54092 10.8244 6.92404 11.5353 6.45654 12.1062C5.99342 12.6719 4.90217 13.1284 4.18592 13.1856C0.74498 13.4603 -1.2894 9.55061 0.920605 6.91248C2.50373 5.57748 4.15373 2.81405 6.36248 2.60155Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M11.7487 0.821235C15.2822 0.47811 17.3431 4.5228 15.0025 7.16748C12.8937 8.96155 11.2072 12.3775 7.88748 11.1828C5.78498 10.4262 4.77779 7.93936 5.78248 5.92155C6.0256 5.43342 6.82217 4.21905 7.42529 4.9478C7.88342 5.50155 7.13217 6.02155 6.89967 6.48686C5.66311 8.96124 8.85123 11.4734 11.1022 9.33061C11.9965 8.47905 13.2881 7.24311 14.0694 6.31967C15.9781 4.06342 13.4275 1.01811 10.9509 2.39749C10.725 2.52342 10.3044 2.92123 10.115 2.96061C9.49623 3.08936 9.1106 2.45999 9.46748 1.97467C9.89685 1.39092 11.0397 0.889985 11.7487 0.821235Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
