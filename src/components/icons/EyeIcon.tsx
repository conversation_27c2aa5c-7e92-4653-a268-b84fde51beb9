import { IconComponentProps } from "./types";

export default function EyeIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M7.99861 2.11384C6.18766 2.11384 4.6989 2.9356 3.55733 3.99335C2.48798 4.98724 1.74915 6.16713 1.37141 7C1.74915 7.83287 2.48798 9.01276 3.55455 10.0067C4.6989 11.0644 6.18766 11.8862 7.99861 11.8862C9.80957 11.8862 11.2983 11.0644 12.4399 10.0067C13.5092 9.01276 14.2481 7.83287 14.6258 7C14.2481 6.16713 13.5092 4.98724 12.4427 3.99335C11.2983 2.9356 9.80957 2.11384 7.99861 2.11384ZM2.64908 3.01889C3.9573 1.8029 5.75436 0.78125 7.99861 0.78125C10.2429 0.78125 12.0399 1.8029 13.3481 3.01889C14.648 4.22655 15.5174 5.66741 15.9313 6.65852C16.0229 6.87785 16.0229 7.12215 15.9313 7.34148C15.5174 8.33259 14.648 9.77623 13.3481 10.9811C12.0399 12.1971 10.2429 13.2188 7.99861 13.2188C5.75436 13.2188 3.9573 12.1971 2.64908 10.9811C1.34919 9.77623 0.479819 8.33259 0.068744 7.34148C-0.0229147 7.12215 -0.0229147 6.87785 0.068744 6.65852C0.479819 5.66741 1.34919 4.22377 2.64908 3.01889ZM7.99861 9.22098C9.22628 9.22098 10.2206 8.22709 10.2206 7C10.2206 5.77291 9.22628 4.77902 7.99861 4.77902C7.97917 4.77902 7.9625 4.77902 7.94306 4.77902C7.97917 4.92061 7.99861 5.07052 7.99861 5.22321C7.99861 6.20322 7.20146 7 6.22099 7C6.06822 7 5.91824 6.98057 5.77658 6.94448C5.77658 6.96391 5.77658 6.98057 5.77658 7C5.77658 8.22709 6.77094 9.22098 7.99861 9.22098ZM7.99861 3.44643C8.94152 3.44643 9.84581 3.82082 10.5126 4.48725C11.1793 5.15367 11.5539 6.05753 11.5539 7C11.5539 7.94247 11.1793 8.84633 10.5126 9.51275C9.84581 10.1792 8.94152 10.5536 7.99861 10.5536C7.0557 10.5536 6.15141 10.1792 5.48467 9.51275C4.81793 8.84633 4.44336 7.94247 4.44336 7C4.44336 6.05753 4.81793 5.15367 5.48467 4.48725C6.15141 3.82082 7.0557 3.44643 7.99861 3.44643Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
