import { IconComponentProps } from "./types";

export default function YIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 10 12"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M1.0327 0.178831L4.99804 5.74384L8.92735 0.205499C9.48365 -0.376035 10.3349 0.396477 9.86234 1.03651L5.62388 7.07294L5.60126 11.4714C5.51161 12.1493 4.53808 12.1872 4.39482 11.5153L4.36968 7.07724C3.15486 5.28619 1.85542 3.55363 0.637254 1.76601C0.496503 1.55955 0.0868155 1.04081 0.0306825 0.843811C-0.155311 0.191735 0.545096 -0.267643 1.0327 0.178831Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}

<svg
  width="10"
  height="12"
  viewBox="0 0 10 12"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
></svg>;
