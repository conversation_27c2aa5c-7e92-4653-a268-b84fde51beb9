import { IconComponentProps } from "./types";

export default function EyeSlashIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M0.970124 0.752451C0.710128 0.547756 0.332635 0.595185 0.127638 0.854798C-0.0773581 1.11441 -0.0298589 1.49135 0.230136 1.69605L15.0299 13.2788C15.2899 13.4835 15.6674 13.4361 15.8724 13.1765C16.0774 12.9168 16.0299 12.5399 15.7699 12.3352L13.1399 10.2783C14.1299 9.26478 14.7999 8.12897 15.1374 7.32267C15.2199 7.12546 15.2199 6.90579 15.1374 6.70858C14.7649 5.81741 13.9824 4.51934 12.8124 3.43596C11.6374 2.34258 10.02 1.42395 8 1.42395C6.29503 1.42395 4.87505 2.08047 3.76757 2.94169L0.970124 0.752451ZM4.74506 3.70555C5.65004 3.06401 6.73752 2.62217 8 2.62217C9.62997 2.62217 10.9699 3.36107 11.9974 4.31215C12.9599 5.20582 13.6249 6.26674 13.9649 7.01562C13.6499 7.71458 13.0499 8.68314 12.1924 9.53437L10.8474 8.48094C11.0749 8.0416 11.2024 7.54484 11.2024 7.01562C11.2024 5.25075 9.76997 3.82038 8.0025 3.82038C7.19751 3.82038 6.46003 4.11744 5.89754 4.60671L4.74506 3.70555ZM9.87247 7.71958L7.835 6.12445C7.94 5.91227 8 5.67013 8 5.418C8 5.28071 7.9825 5.14591 7.95 5.0186C7.9675 5.0186 7.9825 5.0186 8 5.0186C9.10498 5.0186 9.99996 5.91227 9.99996 7.01562C9.99996 7.26276 9.95497 7.4999 9.87247 7.71958ZM10.1075 10.9722C9.46997 11.2443 8.76749 11.4091 8 11.4091C6.37003 11.4091 5.03005 10.6702 4.00257 9.7191C3.04009 8.82543 2.3751 7.76451 2.0351 7.01562C2.2426 6.55631 2.5726 5.97967 3.02009 5.39803L2.0776 4.65664C1.50761 5.39803 1.10012 6.13693 0.862625 6.70858C0.780127 6.90579 0.780127 7.12546 0.862625 7.32267C1.23512 8.21384 2.01761 9.51191 3.18758 10.5953C4.36256 11.6887 5.98004 12.6073 8 12.6073C9.19498 12.6073 10.2475 12.2853 11.1549 11.796L10.1075 10.9722ZM4.80006 7.01562C4.80006 8.7805 6.23253 10.2109 8 10.2109C8.33249 10.2109 8.65249 10.1609 8.95498 10.0661L7.55001 8.96273C6.96252 8.82793 6.47253 8.43351 6.20753 7.9068L4.80506 6.80344C4.80006 6.87334 4.79756 6.94323 4.79756 7.01562H4.80006Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
