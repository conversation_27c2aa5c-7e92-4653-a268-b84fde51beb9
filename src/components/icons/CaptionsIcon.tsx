import { IconComponentProps } from "./types";

export default function CaptionsIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M14.2222 2.11384C14.4667 2.11384 14.6667 2.31373 14.6667 2.55804V11.442C14.6667 11.6863 14.4667 11.8862 14.2222 11.8862H1.77778C1.53333 11.8862 1.33333 11.6863 1.33333 11.442V2.55804C1.33333 2.31373 1.53333 2.11384 1.77778 2.11384H14.2222ZM1.77778 0.78125C0.797222 0.78125 0 1.57803 0 2.55804V11.442C0 12.422 0.797222 13.2188 1.77778 13.2188H14.2222C15.2028 13.2188 16 12.422 16 11.442V2.55804C16 1.57803 15.2028 0.78125 14.2222 0.78125H1.77778ZM5.55556 5.66741C5.95 5.66741 6.30556 5.83676 6.55 6.11161C6.79444 6.38645 7.21667 6.40866 7.49167 6.16436C7.76667 5.92005 7.78889 5.49806 7.54444 5.22321C7.05833 4.67907 6.34722 4.33482 5.55833 4.33482C4.08611 4.33482 2.89167 5.5286 2.89167 7C2.89167 8.4714 4.08611 9.66518 5.55833 9.66518C6.34722 9.66518 7.05833 9.32093 7.54444 8.77679C7.78889 8.50194 7.76667 8.08273 7.49167 7.83564C7.21667 7.58856 6.79722 7.61355 6.55 7.88839C6.30556 8.16324 5.95 8.33259 5.55556 8.33259C4.81944 8.33259 4.22222 7.7357 4.22222 7C4.22222 6.2643 4.81944 5.66741 5.55556 5.66741ZM9.55556 7C9.55556 6.2643 10.1528 5.66741 10.8889 5.66741C11.2833 5.66741 11.6389 5.83676 11.8833 6.11161C12.1278 6.38645 12.55 6.40866 12.825 6.16436C13.1 5.92005 13.1222 5.49806 12.8778 5.22321C12.3917 4.67907 11.6806 4.33482 10.8917 4.33482C9.41945 4.33482 8.225 5.5286 8.225 7C8.225 8.4714 9.41945 9.66518 10.8917 9.66518C11.6806 9.66518 12.3917 9.32093 12.8778 8.77679C13.1222 8.50194 13.1 8.08273 12.825 7.83564C12.55 7.58856 12.1306 7.61355 11.8833 7.88839C11.6389 8.16324 11.2833 8.33259 10.8889 8.33259C10.1528 8.33259 9.55556 7.7357 9.55556 7Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
