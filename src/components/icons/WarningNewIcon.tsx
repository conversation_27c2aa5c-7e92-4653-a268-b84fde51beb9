import { IconComponentProps } from "./types";

export default function WarningNewIcon({
  color = "#FF6B6B",
  size = 30,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16" // Match the path's natural dimensions
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 0C3.58187 0 0 3.58187 0 8C0 12.4181 3.58187 16 8 16C12.4181 16 16 12.4181 16 8C16 3.58187 12.4181 0 8 0ZM8 14.5C4.41 14.5 1.5 11.59 1.5 8C1.5 4.41 4.41 1.5 8 1.5C11.59 1.5 14.5 4.41 14.5 8C14.5 11.59 11.59 14.5 8 14.5Z"
        fill={color}
      />
      <path
        d="M8 12.0369C8.54089 12.0369 8.97937 11.5984 8.97937 11.0575C8.97937 10.5166 8.54089 10.0781 8 10.0781C7.45911 10.0781 7.02063 10.5166 7.02063 11.0575C7.02063 11.5984 7.45911 12.0369 8 12.0369Z"
        fill={color}
      />
      <path
        d="M8 9.02781C8.43156 9.02781 8.78125 8.67813 8.78125 8.24656V4.93094C8.78125 4.49937 8.43156 4.14969 8 4.14969C7.56844 4.14969 7.21875 4.49937 7.21875 4.93094V8.24656C7.21875 8.67813 7.56844 9.02781 8 9.02781Z"
        fill={color}
      />
    </svg>
  );
}
