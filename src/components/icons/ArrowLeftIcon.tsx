import { IconComponentProps } from "./types";

export default function ArrowLeftIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 14"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M3.03184 7.87661L7.77515 12.5804C8.45424 13.5441 7.3125 14.4855 6.49141 13.7186C4.54118 11.7197 2.45468 9.85905 0.518188 7.84733C0.265105 7.58387 -0.014319 7.45272 0.000568729 6.97379C0.0154555 6.49487 0.275411 6.40704 0.518188 6.15412C2.46384 4.13186 4.55836 2.25948 6.52348 0.257129C7.42931 -0.519222 8.51493 0.624812 7.71789 1.53699L3.03184 6.12367L15.2337 6.12367C15.6413 6.12367 16.0101 6.62367 15.9998 7.02414C15.9895 7.42461 15.623 7.87661 15.2337 7.87661L3.03184 7.87661Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
