import { IconComponentProps } from "./types";

export default function SnipIcon({
  color = "#E1E1E1",
  size = 16,
  dimensions,
}: IconComponentProps) {
  return (
    <svg
      width={dimensions ? dimensions.width : size}
      height={dimensions ? dimensions.height : size}
      viewBox="0 0 16 16"
      preserveAspectRatio="xMidYMid meet" // Centers the content and maintains aspect ratio
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
    >
      <path
        d="M6.7767 10.841L15.7789 3.21919C16.0422 2.99605 16.0751 2.60166 15.852 2.33822C15.6289 2.07477 15.2346 2.04195 14.9712 2.26509L9.00123 7.31966L6.53237 5.26552C6.83512 4.74769 7.00852 4.14547 7.00852 3.50389C7.00852 1.57162 5.43667 0 3.50426 0C1.57186 0 0 1.57162 0 3.50357C0 5.43552 1.57186 7.00715 3.50426 7.00715C4.34409 7.00715 5.11611 6.70995 5.72036 6.21556L8.03268 8.13969L5.96937 9.88664C5.94656 9.90601 5.92625 9.92695 5.90688 9.94851C5.2792 9.35662 4.43344 8.99285 3.50426 8.99285C1.57186 8.99285 0 10.5645 0 12.4964C0 14.4284 1.57186 16 3.50426 16C5.43667 16 7.00852 14.4284 7.00852 12.4964C7.00852 11.9336 6.8748 11.4014 6.63798 10.9295C6.68672 10.9067 6.73358 10.877 6.7767 10.8404V10.841ZM3.50426 5.75741C2.2614 5.75741 1.25036 4.74644 1.25036 3.50389C1.25036 2.26134 2.2614 1.25005 3.50426 1.25005C4.74713 1.25005 5.75816 2.26103 5.75816 3.50357C5.75816 4.74612 4.74713 5.7571 3.50426 5.7571V5.75741ZM3.50426 14.7506C2.2614 14.7506 1.25036 13.7396 1.25036 12.4971C1.25036 11.2545 2.2614 10.2435 3.50426 10.2435C4.74713 10.2435 5.75816 11.2545 5.75816 12.4971C5.75816 13.7396 4.74713 14.7506 3.50426 14.7506Z"
        fillRule="evenodd"
        fill={color}
      />
      <path
        d="M15.7714 12.9527L10.804 8.75815L9.89698 9.586L14.9715 13.9137C15.0884 14.0109 15.2302 14.0584 15.3711 14.0584C15.5505 14.0584 15.7283 13.9815 15.852 13.833C16.0729 13.5677 16.0366 13.1736 15.7714 12.9527Z"
        fillRule="evenodd"
        fill={color}
      />
    </svg>
  );
}
