import React from "react";
import ErrorIcon from "../icons/ErrorIcon";
import SuccessIcon from "../icons/SuccessIcon";
import UploadIcon from "../icons/UploadIcon";
import TextIcon from "../icons/TextIcon";
import ClockIcon from "../icons/ClockIcon";
import AspectRatioIcon from "../icons/AspectRatioIcon";
import SubtitlesIcon from "../icons/SubtitlesIcon";
import InfoIcon from "../icons/InfoIcon";
import AddVideoIcon from "../icons/AddVideoIcon";
import CloseIcon from "../icons/CloseIcon";
import LockerIcon from "../icons/LockerIcon";
import RocketIcon from "../icons/RocketIcon";
import WarningIcon from "../icons/WarningIcon";
import YoutubeTextIcon from "../icons/YoutubeTextIcon";
import UploadIcon2 from "../icons/UploadIcon2";
import VideoFileIcon from "../icons/VideoFileIcon";
import DeleteFilledIcon from "../icons/DeleteFilledIcon";
import DeleteOutlineIcon from "../icons/DeleteOutlineIcon";

import { IconType } from "../icons/types";
import MoreIcon from "../icons/MoreIcon";
import BellIcon from "../icons/BellIcon";
import WarningNewIcon from "../icons/WarningNewIcon";
import CheckIcon from "../icons/CheckIcon";
import UploadIconNew from "../icons/UploadIconNew";
import TimeIcon from "../icons/TimeIcon";
import LinkIcon from "../icons/LinkIcon";
import ImageIcon from "../icons/ImageIcon";
import TrashCanIcon from "../icons/TrashCanIcon";
import UserIcon from "../icons/UserIcon";
import HeartFilledIcon from "../icons/HeartFilledIcon";
import HeartOutlinedIcon from "../icons/HeartOutlinedIcon";
import PlayIcon from "../icons/PlayIcon";
import PauseIcon from "../icons/PauseIcon";
import VolumeOffIcon from "../icons/VolumeOffIcon";
import VolumeOnIcon from "../icons/VolumeOnIcon";
import EditIcon from "../icons/EditIcon";
import FilterIcon from "../icons/FilterIcon";
import SortIcon from "../icons/SortIcon";
import DownloadIcon from "../icons/DownloadIcon";
import ArrowDownIcon from "../icons/ArrowDownIcon";
import ArrowUpIcon from "../icons/ArrowUpIcon";
import AlignLeftIcon from "../icons/AlignLeftIcon";
import AlignCenterIcon from "../icons/AlignCenterIcon";
import AlignRightIcon from "../icons/AlignRightIcon";
import MinusIcon from "../icons/MinusIcon";
import PlusIcon from "../icons/PlusIcon";
import StrokeIcon from "../icons/StrokeIcon";
import XMarkIcon from "../icons/XMarkIcon";
import XIcon from "../icons/XIcon";
import YIcon from "../icons/YIcon";
import BlurIcon from "../icons/BlurIcon";
import SnipIcon from "../icons/SnipIcon";
import PrevIcon from "../icons/PrevIcon";
import NextIcon from "../icons/NextIcon";
import CropIcon from "../icons/CropIcon";
import CaptionsIcon from "../icons/CaptionsIcon";
import SearchIcon from "../icons/SearchIcon";
import MergeIcon from "../icons/MergeIcon";
import EyeIcon from "../icons/EyeIcon";
import EyeSlashIcon from "../icons/EyeSlashIcon";
import BoostIcon from "../icons/BoostIcon";
import SettingsIcon from "../icons/SettingsIcon";
import LogoutIcon from "../icons/LogoutIcon";
import ArrowLeftIcon from "../icons/ArrowLeftIcon";
import UndoIcon from "../icons/UndoIcon";
import RedoIcon from "../icons/RedoIcon";

const iconMap: Record<IconType, React.ComponentType<any>> = {
  success: SuccessIcon,
  error: ErrorIcon,
  upload: UploadIcon,
  text: TextIcon,
  clock: ClockIcon,
  "aspect-ratio": AspectRatioIcon,
  subtitles: SubtitlesIcon,
  info: InfoIcon,
  "add-video": AddVideoIcon,
  close: CloseIcon,
  locker: LockerIcon,
  rocket: RocketIcon,
  warning: WarningIcon,
  "warning-new": WarningNewIcon,
  "youtube-text": YoutubeTextIcon,
  "upload-2": UploadIcon2,
  "video-file": VideoFileIcon,
  "delete-filled": DeleteFilledIcon,
  "delete-outline": DeleteOutlineIcon,
  more: MoreIcon,
  bell: BellIcon,
  check: CheckIcon,
  "upload-new": UploadIconNew,
  time: TimeIcon,
  link: LinkIcon,
  "x-mark": XMarkIcon,
  image: ImageIcon,
  "trash-can": TrashCanIcon,
  user: UserIcon,
  "heart-filled": HeartFilledIcon,
  "heart-outlined": HeartOutlinedIcon,
  play: PlayIcon,
  pause: PauseIcon,
  "volume-on": VolumeOnIcon,
  "volume-off": VolumeOffIcon,
  edit: EditIcon,
  filter: FilterIcon,
  sort: SortIcon,
  download: DownloadIcon,
  "arrow-down": ArrowDownIcon,
  "arrow-up": ArrowUpIcon,
  "align-left": AlignLeftIcon,
  "align-center": AlignCenterIcon,
  "align-right": AlignRightIcon,
  plus: PlusIcon,
  minus: MinusIcon,
  stroke: StrokeIcon,
  x: XIcon,
  y: YIcon,
  blur: BlurIcon,
  snip: SnipIcon,
  prev: PrevIcon,
  next: NextIcon,
  crop: CropIcon,
  captions: CaptionsIcon,
  search: SearchIcon,
  merge: MergeIcon,
  eye: EyeIcon,
  "eye-slash": EyeSlashIcon,
  boost: BoostIcon,
  settings: SettingsIcon,
  logout: LogoutIcon,
  "arrow-left": ArrowLeftIcon,
  undo: UndoIcon,
  redo: RedoIcon,
};

interface VFIconComponentProps {
  type: IconType;
  backgroundColor?: string;
  color?: string;
  className?: string;
  size?: number;
  dimensions?: { width: number; height: number };
  onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
  style?: React.CSSProperties;
}

const VFIconComponent: React.FC<VFIconComponentProps> = ({
  type,
  backgroundColor = "transparent",
  color,
  className,
  size = 30,
  dimensions = null,
  onClick,
  style,
  ...rest
}) => {
  const IconComponent = iconMap[type];

  return (
    <div
      className={className}
      style={{
        backgroundColor: backgroundColor,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        lineHeight: 1,
        ...style,
      }}
      onClick={onClick}
    >
      <IconComponent
        color={color}
        size={size}
        dimensions={dimensions}
        {...rest}
      />
    </div>
  );
};

VFIconComponent.displayName = "VFIconComponent";

export default VFIconComponent;
