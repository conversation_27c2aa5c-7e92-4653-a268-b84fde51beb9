import React from "react";
import { useDispatch } from "react-redux";
import { openModal } from "../../features/modal/modalSlice";
import { ModalConfig, ModalType } from "../../features/modal/types";
import { VFButton } from "../ui/Button";

const WelcomeModalExample: React.FC = () => {
  const dispatch = useDispatch();

  const handleOpenWelcomeModal = () => {
    const modalConfig: ModalConfig = {
      type: ModalType.WELCOME,
      // Title and subtitle will use defaults from the WelcomeDialog component if not provided
      // title: "Your free trial starts now!",
      // subtitle: "Transform your ideas into stunning videos. Your free trial is active!",
    };

    dispatch(openModal(modalConfig));
  };

  return (
    <div style={{ padding: "2rem" }}>
      <h2>Welcome Modal Example</h2>
      <VFButton
        label="Open Welcome Modal"
        onClick={handleOpenWelcomeModal}
        variant="primary"
      />
    </div>
  );
};

export default WelcomeModalExample;
