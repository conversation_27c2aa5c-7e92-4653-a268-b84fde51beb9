import React from "react";
import { Modal, createStyles } from "@mantine/core";
import { useDispatch } from "react-redux";
import { closeModal } from "../../../features/modal/modalSlice";
import { ModalConfig } from "../../../features/modal/types";
import { VFButton } from "../../ui/Button";
import logoImage from "../../../assets/images/boostcast-logo.svg";

const useStyles = createStyles((theme) => ({
  modal: {
    ".mantine-Modal-inner": {
      padding: 0,
    },
    ".mantine-Modal-modal": {
      padding: 0,
      backgroundColor: theme.other["surface-bg-color"],
      width: "540px",
      minHeight: "338px",
      border: `1px solid ${theme.other["surface-border-color"]}`,
      borderRadius: "4px",
      display: "flex",
      flexDirection: "column",
    },
    ".mantine-Modal-overlay": {
      background: "#12121280",
      opacity: 1,
    },
  },
  container: {
    display: "flex",
    flexDirection: "column",
    padding: "40px",
    gap: "24px",
    height: "100%",
    justifyContent: "space-between",
  },
  contentWrapper: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "20px",
    width: "100%",
  },
  logo: {
    marginBottom: "0",
    display: "flex",
    justifyContent: "center",
  },
  logoImage: {
    width: "180px",
    height: "auto",
    objectFit: "contain",
  },
  title: {
    margin: 0,
    textAlign: "center",
    color: theme.other["surface-text-color"],
  },
  messageWrapper: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: "16px 0px",
  },
  message: {
    color: theme.other["surface-text-color"],
    textAlign: "center",
    margin: 0,
  },
  actionWrapper: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
}));

interface WelcomeDialogProps {
  config: ModalConfig;
  isLast: boolean;
}

export const WelcomeDialog: React.FC<WelcomeDialogProps> = ({
  config,
  isLast,
}) => {
  const { classes } = useStyles();
  const dispatch = useDispatch();

  const handleClose = () => {
    config.onClose?.();
    dispatch(closeModal());
  };

  return (
    <Modal
      opened={isLast}
      onClose={handleClose}
      withCloseButton={false}
      classNames={{ root: classes.modal }}
      centered
      size="auto"
      transition="fade"
      transitionDuration={200}
    >
      <div className={classes.container}>
        <div className={classes.contentWrapper}>
          {/* Logo */}
          <div className={classes.logo}>
            <img
              src={logoImage}
              alt="Boostcast Logo"
              className={classes.logoImage}
            />
          </div>

          {/* Title */}
          <h1 className={classes.title}>
            {config.title || "Your free trial starts now!"}
          </h1>

          {/* Message */}
          <div className={classes.messageWrapper}>
            <p className={`p-regular ${classes.message}`}>
              {config.subtitle ||
                "Transform your ideas into stunning videos. Your free trial is active!"}
            </p>
          </div>
        </div>

        {/* Action Button */}
        <div className={classes.actionWrapper}>
          <VFButton variant="primary" label="Let's go!" onClick={handleClose} />
        </div>
      </div>
    </Modal>
  );
};

export default WelcomeDialog;
