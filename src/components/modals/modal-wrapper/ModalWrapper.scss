.style-header {
  height: 50px;
  width: 100%;
  background-color: #29b1a1;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .close-button {
    cursor: pointer;
  }
}

.icon-holder {
  text-align: center;

  .modal-icon {
    display: block;
    height: auto;
    margin: 0 auto 20px auto;
  }
}

.content {
  padding: 40px;

  .modal-header {
    .title {
      margin: 0 0 20px 0;
    }

    .subtitle {
      color: #666666;
      margin-bottom: 30px;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;

    button {
      min-width: 120px;
    }
  }

  .modal-footer {
    margin-top: 30px;
    text-align: center;
  }
}
