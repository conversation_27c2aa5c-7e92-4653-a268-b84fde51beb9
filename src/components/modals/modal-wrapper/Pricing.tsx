import React from "react";
import { Modal, createStyles } from "@mantine/core";
import { useDispatch, useSelector } from "react-redux";
import { closeModal } from "../../../features/modal/modalSlice";
import { ModalAction, ModalConfig } from "../../../features/modal/types";
import {
  VFButton,
  ButtonVariant,
  ButtonSize,
  ButtonProps,
  ButtonDisplay,
} from "../../ui/Button";
import VFIconComponent from "../../icon/vf-icon";
import { IconType } from "../../icons/types";
import StripePricingTable from "../../stripe-pricing-table/StripePricingTable";
import envConfig from "../../../envConfig";
import { RootState } from "../../../store";

const useStyles = createStyles((theme) => ({
  modal: {
    ".mantine-Modal-inner": {
      padding: 0,
    },
    ".mantine-Modal-modal": {
      padding: 0,
      backgroundColor: theme.other["dialog-bg-color"],
      width: "90%",
      border: `1px solid ${theme.other["dialog-border-color"]}`,
    },
    ".mantine-Modal-overlay": {
      background: "#12121280",
      opacity: 1,
    },
  },
  header: {
    padding: "8px 16px",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: `1px solid ${theme.other["dialog-border-color"]}`,
  },
  title: {
    color: theme.other["dialog-text-color"],
    margin: 0,
    flex: 1,
    textAlign: "left",
  },
  subtitle: {
    color: theme.other["dialog-text-color"],
    marginBottom: "32px",
    flex: 1,
    textAlign: "left",
  },
  closeButton: {
    cursor: "pointer",
    padding: "4px",
    marginLeft: "auto",
    "&:hover": {
      opacity: 0.8,
    },
  },
  body: {
    padding: "32px 24px",
    color: theme.other["dialog-text-color"],
    textAlign: "left",
  },
  actions: {
    padding: "16px",
    display: "flex",
    justifyContent: "center",
    gap: "8px",
  },
}));

// Extended ModalAction type to include buttonDisplay and iconName with proper IconType
interface ExtendedModalAction extends ModalAction {
  buttonDisplay?: ButtonDisplay;
  iconName?: IconType;
  fullWidth?: boolean;
}

// Type guard to check if the action is a ModalAction
const isModalAction = (
  action: ExtendedModalAction | ButtonProps
): action is ExtendedModalAction => {
  return "label" in action && "onClick" in action;
};

// Map Mantine button variants to VFButton variants
const mapButtonVariant = (
  variant?: "filled" | "outline" | "destructive" | ButtonVariant
): ButtonVariant => {
  switch (variant) {
    case "outline":
    case "secondary":
      return "secondary";
    case "destructive":
      return "destructive";
    case "primary":
      return "primary";
    case "filled":
    default:
      return "primary";
  }
};

// Map Mantine sizes to VFButton sizes
const mapButtonSize = (size?: "sm" | "md" | "lg" | "xl"): ButtonSize => {
  switch (size) {
    case "sm":
      return "small";
    default:
      return "default";
  }
};

interface PricingDialogProps {
  config: ModalConfig;
  isLast: boolean;
}

export const PricingDialog: React.FC<PricingDialogProps> = ({
  config,
  isLast,
}) => {
  const { classes, cx } = useStyles();
  const dispatch = useDispatch();
  const { userData } = useSelector((state: RootState) => state.auth);
  const handleClose = () => {
    config.onClose?.();
    dispatch(closeModal());
  };

  const renderButton = (
    action: ExtendedModalAction | ButtonProps,
    index: number
  ) => {
    if (isModalAction(action)) {
      // For ModalAction type
      const buttonProps: ButtonProps = {
        onClick: action.onClick,
        variant: mapButtonVariant(
          action.variant as "filled" | "outline" | "destructive"
        ),
        size: mapButtonSize(action.size),
        buttonDisplay: action.buttonDisplay || "onlyText",
        iconName: action.iconName,
        fullWidth: action.fullWidth || false,
      };

      return (
        <VFButton key={index} {...buttonProps}>
          {action.label}
        </VFButton>
      );
    }

    // For ButtonProps type - pass through directly since it already matches VFButton props
    return (
      <VFButton key={index} {...action}>
        {action.children}
      </VFButton>
    );
  };

  return (
    <Modal
      opened={isLast}
      onClose={handleClose}
      withCloseButton={false}
      classNames={{ root: classes.modal }}
      centered
      size="auto"
      transition="fade"
      transitionDuration={200}
    >
      <div className={classes.header}>
        <h3 className={classes.title}>{config.title}</h3>
        <VFIconComponent
          type="x-mark"
          className={classes.closeButton}
          onClick={handleClose}
          size={10}
        />
      </div>

      <div className={classes.body}>
        <p className={cx(classes.subtitle)}>{config.subtitle}</p>
        <StripePricingTable
          pricingTableId={envConfig.stripePricingTableId}
          publishableKey={envConfig.stripePublishableKey}
          clientReferenceId={userData?.id}
          customerEmail={null}
        />
      </div>

      <div className={classes.actions}>
        {config.actions?.map((action, index) => renderButton(action, index))}
      </div>
    </Modal>
  );
};
