import { SkeletonProps } from "./skeleton.types";

export const Skeleton: React.FC<SkeletonProps> = ({
    width = '100%',
    height = '100%',
    borderRadius = '4px',
    bgColor,
    textColor,
    shimmerAngle,
    textSize,            // <-- grab the new prop
    children,
}) => {
    const style: React.CSSProperties = {
        width,
        height,
        borderRadius,

        // CSS variables for the shimmer/bg/text
        ...(bgColor ? { '--skeleton-bg': bgColor } as any : {}),
        ...(textColor ? { '--skeleton-text': textColor } as any : {}),
        ...(shimmerAngle ? { '--shimmer-angle': shimmerAngle } as any : {}),
    };

    // And apply `textSize` directly on the span
    const textStyle: React.CSSProperties = {
        fontSize: textSize,
        color: textColor
    };

    return (
        <div className="skeleton" style={style}>
            {children != null && (
                <span style={textStyle}>
                    {children}
                </span>
            )}
        </div>
    );
};

export default Skeleton;