import React from "react";
import { useSelector } from "react-redux";
import { ModalWrapper } from "../../components/modals/modal-wrapper/ModalWrapper";
import { RootState } from "../../store";
import { ModalConfig, ModalType } from "./types";
import { BasicDialog } from "../../components/modals/modal-wrapper/Basic";
import WelcomeDialog from "../../components/modals/modal-wrapper/WelcomeDialog";
import { PricingDialog } from "../../components/modals/modal-wrapper/Pricing";

// Type for a component that can render a modal with standard props
type ModalComponentType = React.ComponentType<{
  config: ModalConfig;
  isLast: boolean;
}>;

// Standard modal components that work with ModalConfig
const STANDARD_MODAL_COMPONENTS: Record<string, ModalComponentType> = {
  [ModalType.BASIC]: BasicDialog,
  [ModalType.DEFAULT]: ModalWrapper,
  [ModalType.ERROR]: BasicDialog,
  [ModalType.WELCOME]: WelcomeDialog,
  [ModalType.PRICING]: PricingDialog,
};

export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const modals = useSelector((state: RootState) => state.modal.modals);

  return (
    <>
      {children}
      {modals.map((modal: ModalConfig, index) => {
        const modalType = modal.type || ModalType.DEFAULT;

        // Use standard components when possible
        const ModalComponent = STANDARD_MODAL_COMPONENTS[modalType];
        if (ModalComponent) {
          return (
            <ModalComponent
              key={index}
              config={modal}
              isLast={index === modals.length - 1}
            />
          );
        }

        // For special modals that aren't in our standard components list,
        // log a warning and don't render them
        console.warn(
          `Modal type ${modalType} requires custom props and cannot be rendered with current implementation`
        );
        return null;
      })}
    </>
  );
};
