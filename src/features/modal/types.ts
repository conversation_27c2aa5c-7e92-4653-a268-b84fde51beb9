import { IconType } from "../../components/icons/types";
import { ButtonProps } from "../../components/ui/Button";

export enum ModalType {
  BASIC = "BASIC",
  DEFAULT = "DEFAULT",
  ERROR = "ERROR",
  CONFIRM_EMAIL = "CONFIRM_EMAIL",
  USAGE_QUOTA = "USAGE_QUOTA",
  SUBSCRIPTION = "SUBSCRIPTION",
  CHANGE_PASSWORD = "CHANGE_PASSWORD",
  WELCOME = "WELCOME",
  PRICING = "PRICING",
}

export type ModalAction = {
  label: string;
  onClick: () => void;
  variant?: "filled" | "outline";
  size?: "sm" | "md" | "lg" | "xl";
};

export type ModalIcon = {
  type: IconType;
  color?: string;
  size?: number;
  style?: React.CSSProperties;
};

export type ModalConfig = {
  type?: ModalType;
  iconConfig?: ModalIcon;
  title?: string;
  subtitle?: string;
  /**
   * Projected modal body. If provided, this ReactNode will be rendered as the modal body instead of subtitle.
   */
  body?: React.ReactNode;
  actions?: ModalAction[] | ButtonProps[];
  footer?: React.ReactNode;
  closable?: boolean;
  onClose?: () => void;
};
