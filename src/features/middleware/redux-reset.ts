import { Middleware } from "redux";
import { logout } from "../auth/authSlice";
import { getUserData } from "../auth/authActions";

// Action type for resetting all redux slices
export const RESET_STATE = "app/RESET_STATE";

// Action creator for resetting the state
export const resetState = () => ({ type: RESET_STATE });

// Middleware to listen for logout and getUserData.rejected actions and dispatch the reset action
export const resetStateMiddleware: Middleware =
  (store) => (next) => (action) => {
    const result = next(action);

    // If the action is logout or getUserData.rejected, dispatch the reset action
    if (logout.match(action) || action.type === getUserData.rejected.type) {
      store.dispatch(resetState());
    }

    return result;
  };

export default resetStateMiddleware;
