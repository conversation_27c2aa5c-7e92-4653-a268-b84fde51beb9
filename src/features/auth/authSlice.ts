import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import {
  getSubscriptionData,
  getUserData,
  registerUser,
  SubscriptionData,
  userLogin,
} from "./authActions";
import { Cookies } from "react-cookie";
import { IUserData } from "../../types";
import { clarity } from "react-microsoft-clarity";
import { RESET_STATE } from "../middleware/redux-reset";

interface AuthSliceState {
  userData: IUserData | null;
  userToken: string | null;
  isAuthenticated: boolean;
  emailConfirmed: boolean;
  subscriptionLevel: number | null;
  isLoading: boolean;
  error: string | null;
  subscriptionData: SubscriptionData | null;
  authenticationFailed: boolean;
}

const cookies = new Cookies();

let cookieValue = cookies.get("user_auth");

if (cookieValue === "null") {
  cookies.remove("user_auth");
  cookieValue = null;
}

const userToken = cookieValue ? cookieValue : null;

const initialState: AuthSliceState = {
  userData: null,
  userToken,
  isAuthenticated: false,
  emailConfirmed: false,
  subscriptionLevel: null,
  isLoading: false,
  error: null,
  subscriptionData: null,
  authenticationFailed: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      cookies.remove("user_auth");
      state.isLoading = false;
      state.userData = null;
      state.userToken = null;
      state.error = null;
      state.isAuthenticated = false;
      state.subscriptionData = null;
      state.subscriptionLevel = null;
      state.emailConfirmed = false;
      state.authenticationFailed = false;
    },
    setUserData: (state, { payload }) => {
      state.userData = payload;
    },
    resetAuthFailure: (state) => {
      state.authenticationFailed = false;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(userLogin.pending, (state) => {
      state.isLoading = true;
      state.error = null;
      state.authenticationFailed = false;
    });
    builder.addCase(userLogin.fulfilled, (state, { payload }) => {
      if (payload && payload.access_token) {
        state.isLoading = false;
        state.userToken = payload.access_token;
        state.isAuthenticated = true;
        state.authenticationFailed = false;
        cookies.set("user_auth", payload.access_token, { path: "/" });
      }
    });
    builder.addCase(userLogin.rejected, (state, { payload }) => {
      state.isLoading = false;
      state.error = payload as any;
      state.isAuthenticated = false;
      state.authenticationFailed = true;
    });
    builder.addCase(registerUser.pending, (state) => {
      state.isLoading = true;
      state.error = null;
      state.authenticationFailed = false;
    });
    builder.addCase(registerUser.fulfilled, (state, { payload }) => {
      if (payload && payload.access_token) {
        state.isLoading = false;
        state.userToken = payload.access_token;
        state.isAuthenticated = true;
        state.authenticationFailed = false;
        cookies.set("user_auth", payload.access_token, { path: "/" });
      }
    });
    builder.addCase(registerUser.rejected, (state, { payload }) => {
      state.isLoading = false;
      state.error = payload as any;
      state.isAuthenticated = false;
      state.authenticationFailed = true;
    });
    builder.addCase(getUserData.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(
      getUserData.fulfilled,
      (state, action: PayloadAction<IUserData>) => {
        const userData: IUserData = action.payload;

        state.userData = userData;
        state.emailConfirmed = userData.email_confirmed;
        state.subscriptionLevel = userData.subscription_level;
        state.isAuthenticated = true;
        state.isLoading = false;
        state.authenticationFailed = false;

        if (clarity.hasStarted()) {
          clarity.identify(userData.id, {
            id: userData.id,
            email: userData.email,
            subscription_level: userData.subscription_level,
            created_at: userData.created_at,
          });
        }
      }
    );
    builder.addCase(
      getUserData.rejected,
      (state, action: PayloadAction<any>) => {
        state.isLoading = false;
        state.error = action.payload;
        state.authenticationFailed = true;
      }
    );
    builder.addCase(getSubscriptionData.fulfilled, (state, { payload }) => {
      state.subscriptionData = payload;
    });
    // Handle the RESET_STATE action to reset to initial state
    builder.addCase(RESET_STATE, () => {
      // Don't use the initialState object directly since it contains the token from cookies
      const resetState = {
        ...initialState,
        userToken: null, // Explicitly set userToken to null to ensure it's cleared
      };
      return resetState;
    });
  },
});

export const { logout, setUserData, resetAuthFailure } = authSlice.actions;
export default authSlice.reducer;
