import { createAsyncThunk } from "@reduxjs/toolkit";
import envConfig from "../../envConfig";
import { ILoginPayload, IRegisterPayload, IUserData } from "../../types";
import { Cookies } from "react-cookie";
import { RootState } from "../../store";
import { authorizedFetch } from "../utils";
import { CustomerPortalUrlResponse, UpdateUserDataArgs } from "./types";

export interface IChangePasswordPayload {
  old_password: string;
  new_password: string;
}

/**
 * Subscription (plan + billing) data returned by the API.
 * All fields and nesting exactly match the JSON structure.
 */
export interface SubscriptionData {
  plan: {
    name: string;
    period: "monthly" | "yearly" | string; // add other periods as needed
    price: string; // e.g. "24,00"
    currency: string; // e.g. "usd"
    boost: number;
    renews_at: string; // ISO‑8601 datetime
  };

  billing_details: {
    address: {
      city: string | null;
      country: string; // ISO‑3166‑1 alpha‑2, e.g. "BG"
      line1: string | null;
      line2: string | null;
      postal_code: string | null;
      state: string | null;
    };

    full_name: string;
    email: string;

    payment_method: "card" | string; // extend with other methods if needed

    card_details: {
      last_4_digits: string; // always 4 characters
      type: "VISA" | "MASTERCARD" | "AMEX" | string;
      expiry: string; // MM/YY
    };
  };
}

export const registerUser = createAsyncThunk(
  "auth/register",
  async (payload: IRegisterPayload, { rejectWithValue }) => {
    try {
      const config = {
        headers: {
          "Content-Type": "application/json",
        },
      };

      const response = await fetch(`${envConfig.baseApi}/register`, {
        method: "POST",
        body: JSON.stringify(payload),
        headers: config.headers,
      });

      const data = await response.json();

      if (!response.ok)
        return rejectWithValue({
          statusCode: response.status,
          error: data,
        });

      return data;
    } catch (error: any) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);

const cookies = new Cookies();

export const userLogin = createAsyncThunk(
  "auth/login",
  async (payload: ILoginPayload, { rejectWithValue }) => {
    try {
      const response = await fetch(`${envConfig.baseApi}/login`, {
        method: "POST",
        body: new URLSearchParams({
          username: payload.username,
          password: payload.password,
        }),
      });

      const data = await response.json();

      if (!response.ok)
        return rejectWithValue({
          statusCode: response.status,
          error: data,
        });

      return data;
    } catch (error: any) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);

export const getUserData = createAsyncThunk(
  "auth/getUserData",
  async (_, { rejectWithValue }) => {
    try {
      const userToken = cookies.get("user_auth");

      const response = await fetch(`${envConfig.baseApi}/me`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
      });

      const data = await response.json();

      if (!response.ok) return rejectWithValue(data);

      return data;
    } catch (error: any) {
      // return custom error message from backend if present
      if (error.response && error.response.data.message) {
        return rejectWithValue(error.response.data.message);
      } else {
        return rejectWithValue(error.message);
      }
    }
  }
);

export const updateUserData = createAsyncThunk<
  IUserData,
  UpdateUserDataArgs,
  { state: RootState }
>(
  "auth/updateUserData",
  async (payload: UpdateUserDataArgs, { getState, rejectWithValue }) => {
    try {
      const response = await authorizedFetch(
        `${envConfig.baseApi}/me`,
        getState(),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue({
          statusCode: response.status,
          error: data,
        });
      }

      return data;
    } catch (error) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);
export const changePassword = createAsyncThunk(
  "auth/changePassword",
  async (payload: IChangePasswordPayload, { rejectWithValue }) => {
    try {
      const userToken = cookies.get("user_auth");

      const response = await fetch(`${envConfig.baseApi}/change_password`, {
        method: "POST",
        body: JSON.stringify(payload),
        headers: {
          Authorization: `Bearer ${userToken}`,
        },
      });

      const data = await response.json();

      if (!response.ok) return rejectWithValue(data);

      return data;
    } catch (error) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);

export const deleteAccount = createAsyncThunk<
  void,
  { password: string },
  { state: RootState }
>("auth/deleteAccount", async ({ password }, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/me`,
      getState(),
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ password }),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const getSubscriptionData = createAsyncThunk<
  SubscriptionData,
  void,
  { state: RootState }
>("auth/getSubscriptionData", async (_, { getState, rejectWithValue }) => {
  try {
    const userToken = cookies.get("user_auth");

    const response = await fetch(`${envConfig.baseApi}/subscription`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const getCustomerPortalUrl = createAsyncThunk<
  CustomerPortalUrlResponse,
  void,
  { state: RootState }
>("auth/getCustomerPortalUrl", async (_, { rejectWithValue }) => {
  try {
    const userToken = cookies.get("user_auth");

    const response = await fetch(`${envConfig.baseApi}/portal-url`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
    });

    const data = await response.json();

    if (!response.ok) return rejectWithValue(data);

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});
