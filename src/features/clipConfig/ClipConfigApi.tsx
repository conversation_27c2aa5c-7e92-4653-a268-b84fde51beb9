import {
  createContext,
  useContext,
  useMemo,
  useReducer,
  useCallback,
  useEffect,
} from "react";
import { IClipConfig, ISubtitles, IVideoSegment } from "../../remotion/types";

// Maximum number of history states to keep
const MAX_HISTORY_SIZE = 50;

// Local storage key for saving history
const HISTORY_STORAGE_KEY_PREFIX = "clip_config_history_";

type ClipConfigState = {
  subtitles: ISubtitles | null;
  segments: IVideoSegment[];
};

// Extended state with history
type ClipConfigHistoryState = {
  past: ClipConfigState[];
  present: ClipConfigState;
  future: ClipConfigState[];
  clipId: string | null; // To identify which clip this history belongs to
};

type ClipConfigApi = {
  onSubtitlesChange: (subtitles: ISubtitles | null) => void;
  onSegmentsChange: (segments: IVideoSegment[]) => void;
  onInitialConfigChange: (config: IClipConfig, clipId?: string) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  saveHistoryToStorage: () => void;
  restoreHistoryFromStorage: (clipId: string) => boolean;
  clearHistory: () => void;
};

const ClipSubtitlesContext = createContext<ClipConfigState["subtitles"] | null>(
  null
);

const ClipSegmentsContext = createContext<ClipConfigState["segments"]>(
  {} as ClipConfigState["segments"]
);

const ClipConfigAPIContext = createContext<ClipConfigApi>({} as ClipConfigApi);

type ClipConfigActions =
  | { type: "updateSubtitles"; payload: ISubtitles | null }
  | { type: "updateSegments"; payload: IVideoSegment[] }
  | {
      type: "setInitialConfig";
      payload: { state: ClipConfigState; clipId?: string };
    }
  | { type: "undo" }
  | { type: "redo" }
  | { type: "clearHistory" }
  | { type: "restoreHistory"; payload: ClipConfigHistoryState };

// Initialize with empty history
const initialHistoryState: ClipConfigHistoryState = {
  past: [],
  present: {
    subtitles: null,
    segments: [],
  },
  future: [],
  clipId: null,
};

// Helper to create a new history entry, limiting size
const pushToHistory = (
  history: ClipConfigState[],
  state: ClipConfigState
): ClipConfigState[] => {
  const newHistory = [...history, state];
  return newHistory.slice(-MAX_HISTORY_SIZE); // Keep only the most recent entries
};

const clipConfigReducer = (
  state: ClipConfigHistoryState,
  action: ClipConfigActions
): ClipConfigHistoryState => {
  switch (action.type) {
    case "updateSubtitles": {
      const newPresent = { ...state.present };

      if (action.payload === null) {
        newPresent.subtitles = null;
      } else if (
        action.payload &&
        action.payload.items &&
        action.payload.items.length > 0
      ) {
        // Items array exists and is non-empty - use it directly
        newPresent.subtitles = {
          ...action.payload,
          items: action.payload.items,
        };
      } else if (action.payload && state.present.subtitles?.items) {
        // Items are missing or empty in the payload but exist in current state
        // This happens during position updates - preserve the existing items
        newPresent.subtitles = {
          ...action.payload,
          items: state.present.subtitles.items,
        };
      } else {
        // Fallback - no items available anywhere
        newPresent.subtitles = action.payload;
      }

      const isWordsPerLineChange =
        newPresent.subtitles?.config?.words_per_line !==
        state.present.subtitles?.config?.words_per_line;

      // Special logging for words_per_line changes
      if (isWordsPerLineChange) {
        console.log(
          `Words per line history change: ${state.present.subtitles?.config?.words_per_line} -> ${newPresent.subtitles?.config?.words_per_line}`
        );
      }

      // For debugging - log position updates
      if (
        newPresent.subtitles?.position?.y !==
        state.present.subtitles?.position?.y
      ) {
        console.log(
          `Position Y changed: ${state.present.subtitles?.position?.y} -> ${newPresent.subtitles?.position?.y}`,
          `Items length: ${newPresent.subtitles?.items?.length || 0}`
        );
      }

      // Only add to history if there's an actual change
      if (JSON.stringify(newPresent) !== JSON.stringify(state.present)) {
        return {
          past: pushToHistory(state.past, state.present),
          present: newPresent,
          future: [], // Clear redo history
          clipId: state.clipId,
        };
      }
      return state;
    }

    case "updateSegments": {
      const newPresent = {
        ...state.present,
        segments: action.payload,
      };

      // Only add to history if there's an actual change
      if (
        JSON.stringify(newPresent.segments) !==
        JSON.stringify(state.present.segments)
      ) {
        return {
          past: pushToHistory(state.past, state.present),
          present: newPresent,
          future: [], // Clear redo history
          clipId: state.clipId,
        };
      }
      return state;
    }

    case "setInitialConfig": {
      return {
        past: [], // Clear history when setting initial config
        present: action.payload.state,
        future: [],
        clipId: action.payload.clipId || state.clipId,
      };
    }

    case "undo": {
      if (state.past.length === 0) return state;

      const previous = state.past[state.past.length - 1];
      const newPast = state.past.slice(0, state.past.length - 1);

      // Log specific words_per_line changes for debugging
      const prevWordsPerLine = previous.subtitles?.config?.words_per_line;
      const currentWordsPerLine =
        state.present.subtitles?.config?.words_per_line;

      if (prevWordsPerLine !== currentWordsPerLine) {
        console.log(
          `UNDO - Words per line reverting from ${currentWordsPerLine} to ${prevWordsPerLine}`
        );
      }

      return {
        past: newPast,
        present: previous,
        future: [state.present, ...state.future],
        clipId: state.clipId,
      };
    }

    case "redo": {
      if (state.future.length === 0) return state;

      const next = state.future[0];
      const newFuture = state.future.slice(1);

      // Log specific words_per_line changes for debugging
      const nextWordsPerLine = next.subtitles?.config?.words_per_line;
      const currentWordsPerLine =
        state.present.subtitles?.config?.words_per_line;

      if (nextWordsPerLine !== currentWordsPerLine) {
        console.log(
          `REDO - Words per line changing from ${currentWordsPerLine} to ${nextWordsPerLine}`
        );
      }

      return {
        past: [...state.past, state.present],
        present: next,
        future: newFuture,
        clipId: state.clipId,
      };
    }

    case "clearHistory": {
      return {
        ...state,
        past: [],
        future: [],
      };
    }

    case "restoreHistory": {
      return action.payload;
    }

    default:
      return state;
  }
};

export const ClipConfigProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [historyState, dispatch] = useReducer(
    clipConfigReducer,
    initialHistoryState
  );
  const { past, present, future, clipId } = historyState;

  // Memoized flags for undo/redo availability
  const canUndo = past.length > 0;
  const canRedo = future.length > 0;

  // Save history to localStorage
  const saveHistoryToStorage = useCallback(() => {
    if (!clipId) return;

    try {
      const storageKey = `${HISTORY_STORAGE_KEY_PREFIX}${clipId}`;
      localStorage.setItem(storageKey, JSON.stringify(historyState));
      console.log(`Saved clip config history for clip ${clipId}`);
    } catch (error) {
      console.error("Error saving clip config history:", error);
    }
  }, [historyState, clipId]);

  // Restore history from localStorage
  const restoreHistoryFromStorage = useCallback((targetClipId: string) => {
    try {
      const storageKey = `${HISTORY_STORAGE_KEY_PREFIX}${targetClipId}`;
      const savedHistory = localStorage.getItem(storageKey);

      if (savedHistory) {
        const parsedHistory = JSON.parse(
          savedHistory
        ) as ClipConfigHistoryState;
        dispatch({ type: "restoreHistory", payload: parsedHistory });
        console.log(`Restored clip config history for clip ${targetClipId}`);
        return true;
      }
    } catch (error) {
      console.error("Error restoring clip config history:", error);
    }
    return false;
  }, []);

  // Auto-save history changes to localStorage
  useEffect(() => {
    if (clipId) {
      saveHistoryToStorage();
    }
  }, [historyState, saveHistoryToStorage, clipId]);

  const api = useMemo(
    () => ({
      onSubtitlesChange: (subtitles: ISubtitles | null) => {
        return dispatch({
          type: "updateSubtitles",
          payload: subtitles,
        });
      },
      onSegmentsChange: (segments: IVideoSegment[]) => {
        return dispatch({ type: "updateSegments", payload: segments });
      },
      onInitialConfigChange: (config: IClipConfig, newClipId?: string) => {
        const calibratedSubtitles =
          config.subtitles && config.subtitles.items && config.segments
            ? {
                ...config.subtitles,
                items: config.subtitles.items,
              }
            : config.subtitles;

        const newState: ClipConfigState = {
          subtitles: calibratedSubtitles || null,
          segments: config.segments,
        };

        return dispatch({
          type: "setInitialConfig",
          payload: { state: newState, clipId: newClipId },
        });
      },
      undo: () => {
        if (canUndo) {
          console.log("Performing undo operation");
          dispatch({ type: "undo" });
        }
      },
      redo: () => {
        if (canRedo) {
          console.log("Performing redo operation");
          dispatch({ type: "redo" });
        }
      },
      canUndo,
      canRedo,
      saveHistoryToStorage,
      restoreHistoryFromStorage,
      clearHistory: () => dispatch({ type: "clearHistory" }),
    }),
    [canUndo, canRedo, saveHistoryToStorage, restoreHistoryFromStorage]
  );

  return (
    <ClipConfigAPIContext.Provider value={api}>
      <ClipSubtitlesContext.Provider value={present.subtitles || null}>
        <ClipSegmentsContext.Provider value={present.segments}>
          {children}
        </ClipSegmentsContext.Provider>
      </ClipSubtitlesContext.Provider>
    </ClipConfigAPIContext.Provider>
  );
};

export const useClipConfigAPI = () => {
  return useContext(ClipConfigAPIContext);
};

export const useClipSubtitles = () => {
  return useContext(ClipSubtitlesContext);
};

export const useClipSegments = () => {
  return useContext(ClipSegmentsContext);
};
