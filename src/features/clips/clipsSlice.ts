import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "../../store";
import { IVideoClip, ProcessingStatus } from "../../types";
import envConfig from "../../envConfig";

// Import types
import {
  CreateClipArgs,
  UpdateClipArgs,
  RenderClipArgs,
  ReframeClipsArgs,
  UpdateClipPayload,
} from "./types";

import { RESET_STATE } from "../middleware/redux-reset";

export enum ClipLoadingStatus {
  IDLE = "idle",
  LOADING = "loading",
  SUCCEEDED = "succeeded",
  FAILED = "failed",
}

interface ClipState {
  clips: Record<string, IVideoClip[]>;
  loadingStatus: ClipLoadingStatus;
  error: string | null;
}

const initialState: ClipState = {
  clips: {},
  loadingStatus: ClipLoadingStatus.IDLE,
  error: null,
};

// Helper function for authenticated API requests
const authorizedFetch = async (
  url: string,
  state: RootState,
  options: RequestInit = {}
) => {
  const token = state.auth.userToken;
  const headers = new Headers(options.headers || {});

  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  return response;
};

// Create a clip for a video
export const createClip = createAsyncThunk<
  IVideoClip,
  CreateClipArgs,
  { state: RootState }
>("clips/createClip", async ({ videoId }, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/videos/${videoId}/clips`,
      getState(),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      console.warn("bebka, response", response);

      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    console.warn("errorka", error);
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Get all clips for a video
export const fetchVideoClips = createAsyncThunk<
  IVideoClip[],
  string,
  { state: RootState }
>("clips/fetchVideoClips", async (videoId, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/videos/${videoId}/clips`,
      getState()
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Get a specific clip
export const fetchClip = createAsyncThunk<
  IVideoClip,
  string,
  { state: RootState }
>("clips/fetchClip", async (clipId, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/clips/${clipId}`,
      getState()
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Delete a clip
export const deleteClip = createAsyncThunk<
  string,
  string,
  { state: RootState }
>("clips/deleteClip", async (clipId, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/clips/${clipId}`,
      getState(),
      {
        method: "DELETE",
      }
    );

    if (!response.ok) {
      const data = await response.json();
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return clipId;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Update a clip
export const updateClip = createAsyncThunk<
  IVideoClip,
  UpdateClipArgs,
  { state: RootState }
>(
  "clips/updateClip",
  async ({ clipId, updatePayload }, { getState, rejectWithValue }) => {
    try {
      const response = await authorizedFetch(
        `${envConfig.baseApi}/clips/${clipId}`,
        getState(),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(updatePayload),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue({
          statusCode: response.status,
          error: data,
        });
      }

      return data;
    } catch (error) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);

// Render a clip
export const renderClip = createAsyncThunk<
  IVideoClip,
  RenderClipArgs,
  { state: RootState }
>("clips/renderClip", async ({ clipId }, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/clips/${clipId}/render`,
      getState(),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Reframe clips for a video
export const reframeClips = createAsyncThunk<
  IVideoClip[],
  ReframeClipsArgs,
  { state: RootState }
>("clips/reframeClips", async ({ videoId }, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/videos/${videoId}/reframe-clips`,
      getState(),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

// Toggle favorite for a clip
export const toggleFavorite = createAsyncThunk<
  IVideoClip,
  IVideoClip,
  { state: RootState }
>("clips/toggleFavorite", async (clip, { dispatch, rejectWithValue }) => {
  try {
    // Use updateClip thunk to patch is_favorite

    const resultAction = await dispatch(
      updateClip({
        clipId: clip.id,
        updatePayload: { is_favorite: !clip.is_favorite },
      })
    );
    if (updateClip.fulfilled.match(resultAction)) {
      return resultAction.payload;
    } else {
      return rejectWithValue(resultAction.payload);
    }
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Failed to toggle favorite.",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

const clipsSlice = createSlice({
  name: "clips",
  initialState,
  reducers: {
    setClips: (
      state,
      action: PayloadAction<{ videoId: string; clips: IVideoClip[] }>
    ) => {
      state.clips[action.payload.videoId] = action.payload.clips;
    },
    updateClipState: (
      state,
      action: PayloadAction<{
        videoId: string;
        data: IVideoClip;
      }>
    ) => {
      const { videoId, data } = action.payload;
      const clipsArr = state.clips[videoId];
      if (!clipsArr) return;
      const clipIndex = clipsArr.findIndex((c) => c.id === data.id);
      if (clipIndex !== -1) {
        clipsArr[clipIndex] = data;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Create clip
      .addCase(createClip.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(createClip.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        const clip = action.payload;
        if (!state.clips[clip.video_id]) state.clips[clip.video_id] = [];
        state.clips[clip.video_id].push(clip);
      })
      .addCase(createClip.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to create clip";
      })

      // Fetch video clips
      .addCase(fetchVideoClips.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(fetchVideoClips.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        const videoId = action.meta.arg;

        state.clips[videoId] = action.payload;
      })
      .addCase(fetchVideoClips.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to fetch clips";
      })

      // Fetch clip
      .addCase(fetchClip.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(fetchClip.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        const clip = action.payload;
        if (!state.clips[clip.video_id]) state.clips[clip.video_id] = [];
        const idx = state.clips[clip.video_id].findIndex(
          (c) => c.id === clip.id
        );
        if (idx >= 0) {
          state.clips[clip.video_id][idx] = clip;
        } else {
          state.clips[clip.video_id].push(clip);
        }
      })
      .addCase(fetchClip.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to fetch clip";
      })

      // Delete clip
      .addCase(deleteClip.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(deleteClip.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        // Find the videoId for this clip by searching all arrays
        for (const videoId in state.clips) {
          state.clips[videoId] = state.clips[videoId].filter(
            (clip) => clip.id !== action.payload
          );
        }
      })
      .addCase(deleteClip.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to delete clip";
      })

      // Update clip
      .addCase(updateClip.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(updateClip.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        const clip = action.payload;
        const arr = state.clips[clip.video_id];
        if (arr) {
          const idx = arr.findIndex((c) => c.id === clip.id);
          if (idx >= 0) arr[idx] = clip;
        }
      })
      .addCase(updateClip.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to update clip";
      })

      // Render clip
      .addCase(renderClip.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(renderClip.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        const clip = action.payload;
        const arr = state.clips[clip.video_id];
        if (arr) {
          const idx = arr.findIndex((c) => c.id === clip.id);
          if (idx >= 0) {
            arr[idx] = {
              ...arr[idx],
              ...clip,
              rendering_status: ProcessingStatus.PROCESSING,
            };
          }
        }
      })
      .addCase(renderClip.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to render clip";
      })

      // Reframe clips
      .addCase(reframeClips.pending, (state) => {
        state.loadingStatus = ClipLoadingStatus.LOADING;
      })
      .addCase(reframeClips.fulfilled, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.SUCCEEDED;
        if (action.payload.length > 0) {
          const videoId = action.payload[0].video_id;
          state.clips[videoId] = action.payload;
        }
      })
      .addCase(reframeClips.rejected, (state, action) => {
        state.loadingStatus = ClipLoadingStatus.FAILED;
        state.error = action.error.message || "Failed to reframe clips";
      })

      // Add case to handle global state reset
      .addCase(RESET_STATE, () => initialState);
  },
});

export const { setClips, updateClipState } = clipsSlice.actions;

// Selectors
export const selectAllClips = (state: RootState) => state.clips.clips;
export const selectClipsByVideoId = (state: RootState, videoId: string) =>
  state.clips.clips[videoId] || [];
export const selectClipById = (
  state: RootState,
  videoId: string,
  clipId: string
) => state.clips.clips[videoId]?.find((clip: IVideoClip) => clip.id === clipId);
export const selectClipsIdsByVideoId = (state: RootState, videoId: string) =>
  state.clips.clips[videoId]?.map((clip: IVideoClip) => clip.id) || [];
export const selectClipsLoadingStatus = (state: RootState) =>
  state.clips.loadingStatus;
export const selectClipsError = (state: RootState) => state.clips.error;
export const selectLoadingStatus = (state: RootState) =>
  state.clips.loadingStatus;

export default clipsSlice.reducer;
