import { ICreateClipPayload, IVideoClip } from "../../types";
import { ISubtitle, IVideoSegment, ISubtitles } from "../../remotion/types";

// Interface for the arguments required to create a clip
export interface CreateClipArgs {
  videoId: string;
  clipPayload?: ICreateClipPayload;
}

// Interface for the arguments required to update a clip
export interface UpdateClipArgs {
  clipId: string;
  updatePayload: UpdateClipPayload;
}

// Interface for the payload to update a clip
export interface UpdateClipPayload {
  title?: string;
  segments?: IVideoSegment[];
  subtitles?: ISubtitles | null;
  is_favorite?: boolean;
}

// Interface for the arguments required to render a clip
export interface RenderClipArgs {
  clipId: string;
}

// Interface for the arguments required to reframe clips for a video
export interface ReframeClipsArgs {
  videoId: string;
}

// Interface for the payload returned when fetching a clip
export type Clip = IVideoClip;
