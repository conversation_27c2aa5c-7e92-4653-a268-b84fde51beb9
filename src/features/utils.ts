import { RootState } from "../store";

// Helper function for authenticated API requests
export const authorizedFetch = async (
  url: string,
  state: RootState,
  options: RequestInit = {}
) => {
  const token = state.auth.userToken;
  const headers = new Headers(options.headers || {});

  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  return response;
};
