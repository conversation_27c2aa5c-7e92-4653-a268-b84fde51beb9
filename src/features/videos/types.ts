import { ISubtitle } from "../../remotion/types";
import {
  ICreateClipPayload,
  IVideo,
  IVideoClip,
  ProcessingStatus,
} from "../../types";

export interface FetchSubtitlesArgs {
  video_id: string;
  words_per_line: number;
}

export interface FetchSubtitlesPayload {
  video_id: string;
  subtitles: ISubtitle[];
}

export interface CreateClipArgs {
  videoId: string;
  clipPayload: ICreateClipPayload;
}

export type Video = IVideo & {
  processing_status: ProcessingStatus;
  processing_error?: any;
  subtitles?: ISubtitle[];
  subtitles_words_per_line?: number;
  clips: IVideoClip[];
};

export interface GetYoutubeVideoDetailsPayload {
  youtube_url: string;
}

export interface GetYoutubeVideoDetailsResponse {
  title: string;
  duration: number;
}
