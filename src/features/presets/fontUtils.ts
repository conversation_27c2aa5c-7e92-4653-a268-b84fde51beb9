import { FontItem, FontSubFamily } from "../../remotion/types";
import envConfig from "../../envConfig";

/**
 * Loads a font using the FontFace API
 * @param family The font family name
 * @param fontPath The path to the font file
 * @param subFamily The sub-family name (e.g., "Regular", "Bold")
 * @returns A promise that resolves when the font is loaded
 */
export const loadFont = async (
  family: string,
  fontPath: string,
  subFamily: string
): Promise<FontFace> => {
  // Create a unique font family name that includes both family and subFamily
  // This ensures each variant is properly loaded and distinguishable
  const fontName = `${family}-${subFamily}`;

  try {
    // Clean and normalize the font URL
    let fontUrl = cleanFontUrl(fontPath);

    // Create a new FontFace instance
    const fontFace = new FontFace(fontName, `url(${fontUrl})`);

    // Add to the document.fonts (FontFaceSet)
    document.fonts.add(fontFace);

    // Load the font
    await fontFace.load();

    return fontFace;
  } catch (error) {
    throw error;
  }
};

/**
 * Cleans and normalizes a font URL to handle various edge cases
 * NOTE: This function was implemented primarily due to an issue with the backend which was returning malformed font urls
 * @param url The original font URL from the API
 * @returns A cleaned and normalized URL
 */
const cleanFontUrl = (url: string): string => {
  // If it's a data URL, return it as is
  if (url.startsWith("data:")) {
    return url;
  }

  // Handle recursive URL encoding and repeated domains
  if (url.includes("boostcast-fonts.s3.eu-west-1.amazonaws.com")) {
    // First check for over-nested URLs (multiple domains)
    const domainCount = (url.match(/boostcast-fonts\.s3\.eu-west-1\.amazonaws\.com/g) || []).length;

    if (domainCount > 1) {
      // Extract essential parts: the actual S3 path with user ID and file ID plus query params
      const pathMatch = url.match(
        /dev\/([a-f0-9-]+\/[a-f0-9-]+\.ttf(?:\?[^#\s]+)?)/i
      );

      if (pathMatch && pathMatch[1]) {
        return `https://boostcast-fonts.s3.eu-west-1.amazonaws.com/${pathMatch[0]}`;
      }

      // Another attempt to extract the path
      const uuidMatch = url.match(
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}\.ttf)/i
      );

      if (uuidMatch && uuidMatch[1]) {
        // Find the query parameters if they exist
        const queryParams = url.match(
          /(\?X-Amz-Algorithm=AWS4-HMAC-SHA256[^#\s]+)/i
        );
        const queryString = queryParams ? queryParams[1] : "";

        return `https://boostcast-fonts.s3.eu-west-1.amazonaws.com/dev/${uuidMatch[1]}${queryString}`;
      }
    }

    // Check for URL encoded segments
    if (url.includes("%")) {
      try {
        // Try to decode once
        const decodedUrl = decodeURIComponent(url);

        // If the decoded URL is different and still has encoded parts,
        // recursively clean it
        if (decodedUrl !== url && decodedUrl.includes("%")) {
          return cleanFontUrl(decodedUrl);
        }

        // If decoding worked but no more encoding, use this URL
        if (decodedUrl !== url) {
          return decodedUrl;
        }
      } catch (e) {
        // If decoding fails, continue with original url
      }
    }

    // If the URL has https prefixed with http, fix it
    if (url.match(/^http:\/\/https:\/\//)) {
      return url.replace(/^http:\/\/https:\/\//, "https://");
    }

    // If we reached here and the URL still seems valid, return it
    if (url.match(/^https?:\/\//)) {
      return url;
    }
  }

  // If it's not an absolute URL, construct it from the base API
  if (!url.match(/^https?:\/\//)) {
    return `${envConfig.baseApi}${url.startsWith("/") ? "" : "/"}${url}`;
  }

  // If none of the special cases apply, return the original URL
  return url;
};

/**
 * Loads all fonts from the system and user font collections
 * @param systemFonts Array of system fonts
 * @param userFonts Array of user fonts
 * @returns A promise that resolves when all fonts are loaded
 */
export const loadAllFonts = async (
  systemFonts: FontItem[] = [],
  userFonts: FontItem[] = []
): Promise<void> => {
  const allFonts = [...systemFonts, ...userFonts];

  // Count how many sub-families we have in total
  let totalSubFamilies = 0;
  allFonts.forEach((font) => {
    totalSubFamilies += font.sub_families.length;
  });

  const fontLoadPromises: Promise<FontFace>[] = [];

  allFonts.forEach((fontItem) => {
    fontItem.sub_families.forEach((subFamily: FontSubFamily) => {
      fontLoadPromises.push(
        loadFont(fontItem.family, subFamily.file_path, subFamily.sub_family)
      );
    });
  });

  try {
    const results = await Promise.allSettled(fontLoadPromises);

    // Count successes and failures
    const successes = results.filter((r) => r.status === "fulfilled").length;
    const failures = results.filter((r) => r.status === "rejected").length;
  } catch (error) {
    // Error handling without console.error
  }
};

/**
 * Gets the CSS font family name based on family and sub-family
 * This is needed to correctly reference the loaded fonts in CSS
 * @param family The font family name
 * @param subFamily The sub-family name
 * @returns The CSS font family name
 */
export const getCssFontFamily = (family: string, subFamily: string): string => {
  return `${family}-${subFamily}`;
};
