import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { FontsResponse, PresetsResponse } from "./types";
import envConfig from "../../envConfig";
import { RootState } from "../../store";
import { authorizedFetch } from "../utils";
import { Preset, PresetConfig } from "../../remotion/types";
import { RESET_STATE } from "../middleware/redux-reset";

interface PresetsState {
  fonts: FontsResponse | null;
  presets: PresetsResponse | null;
  error: string | null;
  loadingStatus: "idle" | "loading" | "succeeded" | "failed";
}

const initialState: PresetsState = {
  fonts: null,
  presets: null,
  error: null,
  loadingStatus: "idle",
};

export const uploadFont = createAsyncThunk<
  any,
  { file: File },
  { state: RootState }
>("presets/uploadFont", async ({ file }, { getState, rejectWithValue }) => {
  const token = getState().auth.userToken;

  try {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch(`${envConfig.baseApi}/fonts`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    const data: any = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const fetchFonts = createAsyncThunk<
  FontsResponse,
  void,
  { state: RootState }
>("presets/fetchFonts", async (_, { getState, rejectWithValue }) => {
  const token = getState().auth.userToken;

  try {
    const response = await fetch(`${envConfig.baseApi}/fonts`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const data: FontsResponse = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const deleteFont = createAsyncThunk<
  any,
  { family: string },
  { state: RootState }
>("presets/deleteFont", async (family, { getState, rejectWithValue }) => {
  const token = getState().auth.userToken;

  try {
    const response = await fetch(`${envConfig.baseApi}/fonts`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ family }),
    });

    if (!response.ok) return rejectWithValue(response.statusText);

    return await response.json();
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const fetchPresets = createAsyncThunk<
  PresetsResponse,
  void,
  { state: RootState }
>("presets/fetchPresets", async (_, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/presets`,
      getState(),
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const createPreset = createAsyncThunk<
  any,
  { config: PresetConfig; name: string },
  { state: RootState }
>("presets/createPreset", async (preset, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/presets`,
      getState(),
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(preset),
      }
    );

    const data = await response.json();

    if (!response.ok) {
      return rejectWithValue({
        statusCode: response.status,
        error: data,
      });
    }

    return data;
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const updatePreset = createAsyncThunk<
  any,
  { presetConfig: PresetConfig; id: string },
  { state: RootState }
>(
  "presets/updatePreset",
  async ({ presetConfig, id }, { getState, rejectWithValue }) => {
    try {
      const payload = JSON.stringify({
        config: presetConfig,
      });

      const response = await authorizedFetch(
        `${envConfig.baseApi}/presets/${id}`,
        getState(),
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: payload,
        }
      );

      const data = await response.json();

      if (!response.ok) {
        return rejectWithValue({
          statusCode: response.status,
          error: data,
        });
      }

      return data;
    } catch (error) {
      return rejectWithValue({
        error: {
          detail: [
            {
              msg: "Something went wrong..",
            },
          ],
        },
        statusCode: 500,
      });
    }
  }
);

export const deletePreset = createAsyncThunk<
  any,
  { id: string },
  { state: RootState }
>("presets/deletePreset", async ({ id }, { getState, rejectWithValue }) => {
  try {
    const response = await authorizedFetch(
      `${envConfig.baseApi}/presets/${id}`,
      getState(),
      {
        method: "DELETE",
      }
    );

    if (!response.ok) return rejectWithValue(response.statusText);

    return await response.json();
  } catch (error) {
    return rejectWithValue({
      error: {
        detail: [
          {
            msg: "Something went wrong..",
          },
        ],
      },
      statusCode: 500,
    });
  }
});

export const presetsSlice = createSlice({
  name: "presets",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchFonts.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(fetchFonts.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        state.fonts = action.payload;
      })
      .addCase(fetchFonts.rejected, (state, action) => {
        state.loadingStatus = "failed";
        state.error = action.error.message || "Failed to fetch fonts";
      })
      .addCase(uploadFont.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(uploadFont.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        if (state.fonts && action.payload) {
          state.fonts.user = [...state.fonts.user, action.payload];
        }
      })
      .addCase(uploadFont.rejected, (state, action) => {
        state.loadingStatus = "failed";
        state.error = action.error.message || "Failed to upload font";
      })
      .addCase(deleteFont.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(deleteFont.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        if (state.fonts) {
          const deletedFamily = action.meta.arg.family;
          state.fonts.user = state.fonts.user.filter(
            (font) => font.family !== deletedFamily
          );
        }
      })
      .addCase(deleteFont.rejected, (state, action) => {
        state.loadingStatus = "failed";
      })
      .addCase(fetchPresets.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(fetchPresets.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        state.presets = action.payload;
      })
      .addCase(fetchPresets.rejected, (state, action) => {
        state.loadingStatus = "failed";
      })
      .addCase(createPreset.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(createPreset.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        if (state.presets && action.payload) {
          state.presets.user = [...state.presets.user, action.payload];
        }
      })
      .addCase(createPreset.rejected, (state, action) => {
        state.loadingStatus = "failed";
      })
      .addCase(updatePreset.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(updatePreset.fulfilled, (state, action) => {
        state.loadingStatus = "succeeded";
        if (state.presets && action.payload) {
          const updatedPresetId = action.meta.arg.id;
          state.presets.user = state.presets.user.map((preset) =>
            preset.id === updatedPresetId ? action.payload : preset
          );
        }
      })
      .addCase(updatePreset.rejected, (state, action) => {
        state.loadingStatus = "failed";
      })
      .addCase(deletePreset.pending, (state) => {
        state.loadingStatus = "loading";
      })
      .addCase(deletePreset.fulfilled, (state, action) => {
        const deletedPresetId = action.meta.arg.id;

        if (state.presets) {
          state.presets.user = state.presets.user.filter(
            (preset) => preset.id !== deletedPresetId
          );
        }

        state.loadingStatus = "succeeded";
      })
      .addCase(deletePreset.rejected, (state, action) => {
        state.loadingStatus = "failed";
      })
      .addCase(RESET_STATE, () => initialState);
  },
});

export default presetsSlice.reducer;
