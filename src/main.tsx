import React, { useEffect } from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { store, AppDispatch } from "./store";
import envConfig from "./envConfig";
import ReactGA from "react-ga4";
import { Provider } from "react-redux";
import { MantineProvider } from "@mantine/core";
import { NotificationsProvider } from "@mantine/notifications";
import {
  BrowserRouter,
  Navigate,
  Route,
  Routes,
  useLocation,
} from "react-router-dom";
import { AuthenticationPage } from "./pages/authentication-page/AuthenticationPage";
import { AuthProvider } from "./components/AuthContext";
import { useDispatch } from "react-redux";
import ProtectedRoute from "./components/protected-route/ProtectedRoute";
import { hotjar } from "react-hotjar";
import { clarity } from "react-microsoft-clarity";
import { ModalProvider } from "./features/modal/ModalProvider";
import TagManager from "react-gtm-module";
import { extentedMantineTheme } from "./theme/extended-mantine-theme";
import { ClipManagement } from "./pages/clip-management/ClipManagement";
import Editor from "./components/editor";
import { ProjectManagement } from "./pages/project-management/ProjectManagement";
import { AccountSettings } from "./pages/account-settings";
import Header from "./components/header/Header";

function AppWithConditionalHeader() {
  const location = useLocation();
  // Match /projects/:projectId/edit and /projects/:projectId/clips/:clipId/edit
  const isEditorRoute =
    /^\/projects\/[^/]+\/edit$/.test(location.pathname) ||
    /^\/projects\/[^/]+\/clips\/[^/]+\/edit$/.test(location.pathname);
  const dispatch = useDispatch<AppDispatch>();

  // Effect for SSE connection - runs only once on mount
  useEffect(() => {
    dispatch({ type: "CONNECT_SSE" });

    return () => {
      dispatch({ type: "DISCONNECT_SSE" });
    };
  }, [dispatch]); // Only depends on dispatch, which is stable

  // Effect for third-party services - runs once on mount
  useEffect(() => {
    //! TODO: Add production flag in envConfig
    if (envConfig.hotjarId && !hotjar.initialized()) {
      hotjar.initialize({
        id: envConfig.hotjarId,
        sv: 6,
      });
    }

    if (envConfig.clarityId && !clarity.hasStarted()) {
      clarity.init(envConfig.clarityId);
    }

    if (envConfig.googleAnalyticsId && !ReactGA.isInitialized) {
      ReactGA.initialize(envConfig.googleAnalyticsId);
    }

    if (envConfig.googleTagManagerId) {
      TagManager.initialize({
        gtmId: envConfig.googleTagManagerId,
        dataLayer: { platform: "web" },
      });
    }
  }, []); // Empty dependency array - only run once

  return (
    <>
      {!isEditorRoute && <Header />}
      <Routes>
        <Route path="/login" element={<AuthenticationPage />} />
        <Route path="/register" element={<AuthenticationPage />} />
        <Route
          path="/projects"
          element={<ProtectedRoute element={<ProjectManagement />} />}
        />
        <Route
          path="/projects/:projectId/clips"
          element={<ProtectedRoute element={<ClipManagement />} />}
        />
        <Route
          path="/projects/:projectId/clips/:clipId/edit"
          element={<ProtectedRoute element={<Editor />} />}
        />
        <Route
          path="/account-settings"
          element={<ProtectedRoute element={<AccountSettings />} />}
        />
        <Route path="*" element={<Navigate to="/projects" replace />} />
      </Routes>
    </>
  );
}

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <MantineProvider
      withNormalizeCSS
      withCSSVariables
      withGlobalStyles
      theme={{
        ...extentedMantineTheme,
        globalStyles: (theme) => ({
          headings: {
            fontFamily: "Poppins-Bold, sans-serif",
            sizes: {
              h1: {
                fontSize: 36,
                fontWeight: 600,
                lineHeight: "42px",
              },
              h2: {
                fontSize: 28,
                fontWeight: 600,
                lineHeight: "34px",
              },
              h3: {
                fontSize: 22,
                fontWeight: 600,
                lineHeight: "26px",
              },
              h4: {
                fontSize: 16,
                fontWeight: 600,
                lineHeight: "20px",
              },
            },
          },
          h1: {
            fontSize: 36,
            fontWeight: 600,
            lineHeight: "42px",
            margin: 0,
          },
          h2: {
            fontSize: 28,
            fontWeight: 600,
            lineHeight: "34px",
            margin: 0,
          },
          h3: {
            fontSize: 22,
            fontWeight: 600,
            lineHeight: "26px",
            margin: 0,
          },
          h4: {
            fontSize: 16,
            fontWeight: 600,
            lineHeight: "20px",
            margin: 0,
          },
          p: {
            fontFamily: "SF Pro Display, sans-serif",
            margin: 0,
            padding: 0,
          },

          ".p-regular": {
            fontFamily: "SF Pro Display, sans-serif",
            fontWeight: 400,
            fontSize: 16,
            lineHeight: "19px",
          },
          ".p-heavy": {
            fontFamily: "SF Pro Display, sans-serif",
            fontWeight: 500,
            fontSize: 16,
            lineHeight: "19px",
          },
          ".small-p-regular": {
            fontFamily: "SF Pro Display, sans-serif",
            fontWeight: 400,
            fontSize: 12,
            lineHeight: "14px",
          },
          ".small-p-heavy": {
            fontFamily: "SF Pro Display, sans-serif",
            fontWeight: 500,
            fontSize: 12,
            lineHeight: "14px",
          },
          // Size variations
          ".text-default": {
            fontSize: 16,
            lineHeight: "20px",
          },
          ".text-small": {
            fontSize: 12,
            lineHeight: "14px",
          },

          // Weight variations
          ".text-regular": {
            fontWeight: 400,
          },
          ".text-heavy": {
            fontWeight: 500,
          },
          button: {
            fontFamily: "SF Pro Display, sans-serif",
          },
          "#root": {
            display: "block",
            fontFamily: "SF Pro Display, sans-serif",
            width: "100%",
            height: "100%",
          },
          body: {
            display: "block",
            width: "100%",
            height: "100%",
            color: "#1C1C1C",
            scrollbarColor: "#888888 #222222",
          },
          ".vf-button-outlined": {
            color: "#29B1A1",
            borderColor: "#29B1A1",
            "&:hover": {
              backgroundColor: "#29B1A1",
              color: "#FFFFFF",
              borderColor: "#29B1A1",
            },
          },
          ".vf-button-filled": {
            color: "#FFFFFF",
            backgroundColor: "#29B1A1",
            borderColor: "#29B1A1",
            "&:hover": {
              color: "#29B1A1",
              backgroundColor: "#FFFFFF",
              borderColor: "#29B1A1",
            },
          },
        }),
        fontFamily: "SF Pro Display, sans-serif",
      }}
    >
      <NotificationsProvider>
        <Provider store={store}>
          <ModalProvider>
            <BrowserRouter>
              <AuthProvider>
                <AppWithConditionalHeader />
              </AuthProvider>
            </BrowserRouter>
          </ModalProvider>
        </Provider>
      </NotificationsProvider>
    </MantineProvider>
  </React.StrictMode>
);
