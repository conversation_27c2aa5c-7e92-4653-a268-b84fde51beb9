// Facebook Meta Pixel utility module
// Implements vanilla JavaScript/TypeScript integration without external libraries

declare global {
  interface Window {
    fbq?: any;
    _fbq?: any;
  }
}

interface FacebookPixelOptions {
  pixelId: string;
}

class FacebookPixel {
  private pixelId: string;
  private initialized: boolean = false;

  constructor(options: FacebookPixelOptions) {
    this.pixelId = options.pixelId;
  }

  /**
   * Initialize Facebook Meta Pixel
   * Based on the official Facebook Meta Pixel code
   */
  public initialize(): void {
    if (this.initialized || !this.pixelId) {
      return;
    }

    // Add noscript fallback image to the document
    this.addNoscriptFallback();

    // Facebook Meta Pixel initialization code (vanilla JavaScript)
    (function(f: any, b: Document, e: string, v: string, n: any, t: HTMLScriptElement, s: HTMLScriptElement) {
      if (f.fbq) return;
      n = f.fbq = function() {
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = true;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e) as HTMLScriptElement;
      t.async = true;
      t.src = v;
      s = b.getElementsByTagName(e)[0] as HTMLScriptElement;
      s.parentNode?.insertBefore(t, s);
    })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');

    // Initialize the pixel with the provided ID
    window.fbq('init', this.pixelId);

    // Track initial PageView
    this.trackPageView();

    this.initialized = true;
  }

  /**
   * Add noscript fallback image for Facebook Pixel
   * This ensures tracking works even when JavaScript is disabled
   */
  private addNoscriptFallback(): void {
    // Check if noscript element already exists
    const existingNoscript = document.querySelector('noscript[data-facebook-pixel]');
    if (existingNoscript) {
      return;
    }

    // Create noscript element with fallback image
    const noscript = document.createElement('noscript');
    noscript.setAttribute('data-facebook-pixel', 'true');

    const img = document.createElement('img');
    img.height = 1;
    img.width = 1;
    img.style.display = 'none';
    img.src = `https://www.facebook.com/tr?id=${this.pixelId}&ev=PageView&noscript=1`;
    img.alt = 'Facebook Pixel';

    noscript.appendChild(img);

    // Add to document head or body
    const target = document.head || document.body;
    if (target) {
      target.appendChild(noscript);
    }
  }

  /**
   * Track a PageView event
   */
  public trackPageView(): void {
    if (!this.initialized || !window.fbq) {
      return;
    }
    
    window.fbq('track', 'PageView');
  }

  /**
   * Track a custom event
   * @param eventName - The name of the event to track
   * @param parameters - Optional parameters for the event
   */
  public trackEvent(eventName: string, parameters?: Record<string, any>): void {
    if (!this.initialized || !window.fbq) {
      return;
    }
    
    if (parameters) {
      window.fbq('track', eventName, parameters);
    } else {
      window.fbq('track', eventName);
    }
  }

  /**
   * Check if Facebook Pixel is initialized
   */
  public isInitialized(): boolean {
    return this.initialized && !!window.fbq;
  }

  /**
   * Get the pixel ID
   */
  public getPixelId(): string {
    return this.pixelId;
  }
}

// Export a singleton instance factory
let facebookPixelInstance: FacebookPixel | null = null;

export const initializeFacebookPixel = (pixelId: string): FacebookPixel => {
  if (!facebookPixelInstance) {
    facebookPixelInstance = new FacebookPixel({ pixelId });
    facebookPixelInstance.initialize();
  }
  return facebookPixelInstance;
};

export const getFacebookPixel = (): FacebookPixel | null => {
  return facebookPixelInstance;
};

export const trackPageView = (): void => {
  if (facebookPixelInstance) {
    facebookPixelInstance.trackPageView();
  }
};

export const trackEvent = (eventName: string, parameters?: Record<string, any>): void => {
  if (facebookPixelInstance) {
    facebookPixelInstance.trackEvent(eventName, parameters);
  }
};

export default FacebookPixel;
