import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper function to convert RGBA to HEX
const rgbaToHex = (r, g, b, a = 1) => {
  const toHex = (value) => {
    const hex = Math.round(value * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  const hex = `#${toHex(r)}${toHex(g)}${toHex(b)}`;

  // If alpha is 1, return simple hex
  if (a === 1) return hex;

  // If alpha is less than 1, append alpha channel
  const alpha = toHex(a);
  return `${hex}${alpha}`;
};

// Helper function to convert variable path to SCSS variable name
const toSCSSVariable = (path) => {
  // Convert slashes to hyphens and clean up the name
  return `--${path.replace(/\//g, '-').toLowerCase()}`;
};

// Function to extract and format color value
const formatColorValue = (colorData) => {
  if (!colorData) return null;

  // Handle direct color values
  if (typeof colorData === 'object' && 'r' in colorData) {
    return rgbaToHex(colorData.r, colorData.g, colorData.b, colorData.a);
  }

  return null;
};

// Function to process variables and extract values
const processVariables = (tokensData) => {
  const processedTokens = {};

  if (!tokensData || !Array.isArray(tokensData.variables)) {
    console.error('Invalid tokens data structure: missing or invalid variables array');
    return processedTokens;
  }

  tokensData.variables.forEach(variable => {
    const { name, resolvedValuesByMode } = variable;

    // Get the first mode's resolved value (usually "10:1" in this case)
    const firstMode = Object.keys(resolvedValuesByMode)[0];
    const resolvedValue = resolvedValuesByMode[firstMode]?.resolvedValue;

    if (resolvedValue) {
      const colorValue = formatColorValue(resolvedValue);
      if (colorValue) {
        processedTokens[name] = colorValue;
      }
    }
  });

  return processedTokens;
};

// Function to generate SCSS content
const generateSCSS = (tokens) => {
  let scssContent = ':root {\n';

  // Group tokens by their top-level category (before the first slash)
  const groupedTokens = {};

  for (const [path, value] of Object.entries(tokens)) {
    const category = path.split('/')[0];
    if (!groupedTokens[category]) {
      groupedTokens[category] = {};
    }
    groupedTokens[category][path] = value;
  }

  // Sort categories alphabetically and generate SCSS with grouped comments
  const sortedCategories = Object.keys(groupedTokens).sort();

  for (const category of sortedCategories) {
    const values = groupedTokens[category];
    scssContent += `\n  /* ${category} */\n`;

    // Sort variables within each category
    const sortedPaths = Object.keys(values).sort();
    for (const path of sortedPaths) {
      scssContent += `  ${toSCSSVariable(path)}: ${values[path]};\n`;
    }
  }

  scssContent += '}\n';
  return scssContent;
};

// Main function
const main = async () => {
  // Resolve file paths
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const inputFilePath = path.resolve(__dirname, './../figma-tokens.json');
  const outputFilePath = path.resolve(__dirname, './../styles/figma-tokens.scss');

  try {
    // Read and parse the Figma tokens JSON
    const rawData = await fs.promises.readFile(inputFilePath, 'utf-8');
    const tokensData = JSON.parse(rawData);

    // Process variables to extract token values
    const processedTokens = processVariables(tokensData);

    // Generate SCSS content
    const scssContent = generateSCSS(processedTokens);

    // Write SCSS content to file
    await fs.promises.writeFile(outputFilePath, scssContent, 'utf-8');
    console.log(`SCSS file generated successfully at: ${outputFilePath}`);
  } catch (error) {
    console.error('Error generating SCSS file:', error);
  }
};

main();