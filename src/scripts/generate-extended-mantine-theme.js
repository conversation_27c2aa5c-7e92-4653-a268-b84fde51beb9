import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper function to parse CSS variables from SCSS content
const parseCSSVariables = (scssContent) => {
    const cssVariableRegex = /--([\w-]+):\s*([^;]+);/g;
    const variables = {};
    let match;

    while ((match = cssVariableRegex.exec(scssContent)) !== null) {
        const [_, key, value] = match;
        variables[key] = `var(--${key})`;
    }

    return variables;
};

// Main function
const main = async () => {
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    const scssFilePath = path.resolve(__dirname, './../styles/figma-tokens.scss');
    const themeDirPath = path.resolve(__dirname, './../theme/');
    const themeOutputPath = path.resolve(themeDirPath, 'extended-mantine-theme.ts');

    try {
        // Ensure the target directory exists
        if (!fs.existsSync(themeDirPath)) {
            await fs.promises.mkdir(themeDirPath, { recursive: true });
        }

        // Read the SCSS file
        const scssContent = await fs.promises.readFile(scssFilePath, 'utf-8');

        // Parse CSS variables into a JavaScript object
        const cssVariables = parseCSSVariables(scssContent);

        // Build the Mantine theme object
        const mantineTheme = {
            other: cssVariables,
        };

        // Generate the theme file content
        const themeFileContent = `export const extentedMantineTheme = ${JSON.stringify(mantineTheme, null, 2)};`;

        // Write the Mantine theme object to a file
        await fs.promises.writeFile(themeOutputPath, themeFileContent, 'utf-8');
        console.log(`Extended Mantine theme file generated successfully at: ${themeOutputPath}`);
    } catch (error) {
        console.error('Error generating extended Mantine theme:', error);
    }
};

// Execute the script
main();
