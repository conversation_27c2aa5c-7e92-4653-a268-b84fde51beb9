import { exec } from 'child_process';
import path from 'path';

const runScript = (scriptPath) => {
    return new Promise((resolve, reject) => {
        exec(`node ${scriptPath}`, (error, stdout, stderr) => {
            if (error) {
                reject(`Error executing ${scriptPath}: ${error}`);
                return;
            }
            if (stderr) {
                reject(`stderr: ${stderr}`);
                return;
            }
            resolve(stdout);
        });
    });
};

const main = async () => {
    try {
        const __dirname = path.resolve();

        console.log('Generating scss file from figma tokens...');
        await runScript(path.resolve(__dirname, './src/scripts/generate-scss-from-figma-tokens.js'));

        console.log('Generating extended mantine theme...');
        await runScript(path.resolve(__dirname, './src/scripts/generate-extended-mantine-theme.js'));

        console.log('Figma tokens and extened mantine theme successfully generated!');
    } catch (error) {
        console.error('Error generating figma tokens and extended mantine theme scripts:', error);
    }
};

main();
