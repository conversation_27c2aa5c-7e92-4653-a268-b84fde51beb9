import React, {
  createRef,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import { Internals, useVideoConfig } from "remotion";
import { TimelineSliderHandle } from "./TimelineSliderHandle";
import { getCurrentDuration } from "./imperative-state";
import { TimelineWidthContext } from "./context/TimelineWidthProvider";
import { getXPositionOfItemInTimelineImperatively } from "./get-left-of-timeline-slider";
import {
  playerRef,
  sliderAreaRef,
  timelineVerticalScroll,
} from "./components/timeline-refs";
import { PlayerInternals } from "@remotion/player";
import { current } from "@reduxjs/toolkit";

const container: React.CSSProperties = {
  position: "absolute",
  bottom: 0,
  top: 16,
  pointerEvents: "none",
  zIndex: 11,
};

const line: React.CSSProperties = {
  height: "100%",
  width: 1,
  position: "fixed",
  backgroundColor: "#FFC773",
};

export const redrawTimelineSliderFast = createRef<{
  draw: (frame: number, width?: number) => void;
}>();

export const TimelineSlider: React.FC = () => {
  const timelineWidth = useContext(TimelineWidthContext);

  if (timelineWidth === null) {
    //! Todo: add video check
    // if (videoConfig === null || timelineWidth === null) {
    return null;
  }

  return <Inner />;
};

const Inner: React.FC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const timelineWidth = useContext(TimelineWidthContext);

  // Get initial position from player (used only for initial render)
  const initialFrame = playerRef?.current?.getCurrentFrame();

  const durationInFrames = getCurrentDuration();

  // initialFrame commented out

  if (timelineWidth === null) {
    throw new Error("Unexpectedly did not have timeline width");
  }

  const style: React.CSSProperties = useMemo(() => {
    // Default to frame 0 if initialFrame is undefined
    const frame = initialFrame ?? 0;
    // Ensure durationInFrames is at least 1 to avoid division by zero issues
    const duration = Math.max(1, durationInFrames);

    try {
      const left = getXPositionOfItemInTimelineImperatively(
        frame,
        duration,
        timelineWidth
      );

      return {
        ...container,
        transform: `translateX(${left}px)`,
      };
    } catch (err) {
      // Error calculating timeline slider position
      // Return a fallback position
      return {
        ...container,
        transform: `translateX(0px)`,
      };
    }
  }, [initialFrame, durationInFrames, timelineWidth]);

  useImperativeHandle(
    redrawTimelineSliderFast,
    () => {
      return {
        draw: (frame, width?: number) => {
          const { current } = ref;
          if (!current) {
            // timeline slider ref not available
            return;
          }

          try {
            // Ensure we have valid parameters
            const validFrame = Math.max(0, frame);
            const validDuration = Math.max(1, getCurrentDuration());
            const validWidth =
              width ?? (sliderAreaRef.current?.clientWidth as number) ?? 100;

            const position = getXPositionOfItemInTimelineImperatively(
              validFrame,
              validDuration,
              validWidth
            );

            current.style.transform = `translateX(${position}px)`;
          } catch (err) {
            // Error drawing timeline slider
            // Set to a safe position on error
            current.style.transform = `translateX(0px)`;
          }
        },
      };
    },
    []
  );

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) {
      return;
    }

    const { current } = timelineVerticalScroll;
    if (!current) {
      return;
    }

    const onScroll = () => {
      currentRef.style.top = current.scrollTop + "px";
    };

    current.addEventListener("scroll", onScroll);
    return () => {
      current.removeEventListener("scroll", onScroll);
    };
  }, []);

  return (
    <div ref={ref} style={style}>
      <div style={line} />
      <TimelineSliderHandle />
    </div>
  );
};
