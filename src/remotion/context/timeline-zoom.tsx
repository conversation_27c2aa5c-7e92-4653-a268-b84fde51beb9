import React, { createContext, useMemo, useState } from "react";
import { zoomAndPreserveCursor } from "../components/timeline-scroll-logic";
import { getCurrentDuration, getCurrentFrame } from "../imperative-state";

export const TIMELINE_MIN_ZOOM = 1;
export const TIMELINE_MAX_ZOOM = 5;

export const TimelineZoomCtx = createContext<{
  zoom: number;
  setZoom: (prev: (prevZoom: number) => number) => void;
}>({
  zoom: TIMELINE_MIN_ZOOM,
  setZoom: () => {
    throw new Error("has no context");
  },
});

export const TimelineZoomContext: React.FC<{
  readonly children: React.ReactNode;
}> = ({ children }) => {
  const [zoom, setZoom] = useState<number>(TIMELINE_MIN_ZOOM);
  // const [zoom, setZoom] = useState<Record<string, number>>(() => //! todo: fix this
  //   getZoomFromLocalStorage()
  // );

  const value = useMemo(() => {
    return {
      zoom,
      setZoom: (callback: (prevZoomLevel: number) => number) => {
        setZoom((prevZoom: number) => {
          const newZoomWithFloatingPointErrors = Math.min(
            TIMELINE_MAX_ZOOM,
            Math.max(TIMELINE_MIN_ZOOM, callback(prevZoom ?? TIMELINE_MIN_ZOOM))
          );
          const newZoom = Math.round(newZoomWithFloatingPointErrors * 10) / 10;

          zoomAndPreserveCursor({
            oldZoom: prevZoom ?? TIMELINE_MIN_ZOOM,
            newZoom,
            currentDurationInFrames: getCurrentDuration(),
            currentFrame: getCurrentFrame(),
          });

          return newZoom;
        });
      },
    };
  }, [zoom]);

  return (
    <TimelineZoomCtx.Provider value={value}>
      {children}
    </TimelineZoomCtx.Provider>
  );
};
