import { ProcessingStatus } from "../types";

export type IClipConfig = {
  segments: IVideoSegment[];
  subtitles: ISubtitles;
};

export type ISubtitles = {
  config: PresetConfig;
  items: ISubtitle[] | null;
  position: ISubtitlePosition;
};

export type Preset = {
  id: string;
  name: string;
  config: PresetConfig;
  created_at: string;
  updated_at: string;
};

export type PresetConfig = {
  words_per_line: number;
  size: number;
  font: {
    family: string;
    sub_family: string;
  };
  color: string;
  stroke: {
    color: string;
    weight: number;
    opacity: number;
  } | null;
  shadow: {
    color: string;
    opacity: number;
    blur?: number;
    offset?: {
      x: number;
      y: number;
    };
  } | null;
  current_word_color: string | null;
  current_word_bg_color: string | null;
  amplified_word_color: string | null;
};

export type ISubtitlePosition = {
  x: number;
  y: number;
};

export type ISubtitleWord = {
  start: number;
  end: number;
  word: string;
};

export type ISubtitle = {
  start: number;
  end: number;
  text: string;
  words: ISubtitleWord[];
};

export type IVideo = {
  config: IClipConfig;
  created_at: string;
  file_path: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  title: string;
  updated_at: string;
  video_id: string;
  id: string;
};

export type IVideoClip = {
  created_at: string;
  file_path: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  rendering_status: ProcessingStatus;
  rendering_error: any;
  segments: IVideoSegment[];
  subtitles: ISubtitles;
  overall_score: number;
  is_favorite: boolean;
  title: string;
  updated_at: string;
  video_id: string;
  id: string;
};

export interface IVideoMetadata {
  duration_seconds: number;
  file_size_bytes: number;
  height: number;
  width: number;
  fps: number;
}

export interface IVideoSegment {
  start: number;
  end: number;
  crop_box: ICropBox | null;
  text?: string;
  deleted?: boolean;
}

export interface ICropBox {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}
export interface FontSubFamily {
  sub_family: string;
  file_path: string;
}

export interface FontItem {
  family: string;
  created_at: string;
  presets: string[];
  sub_families: FontSubFamily[];
}
