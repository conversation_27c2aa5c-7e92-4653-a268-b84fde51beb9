import { ICropBox } from "./types";

// Initial state values
let currentFrame = 0;
let currentZoom = 1;
let currentDuration = 1;
let originalDuration = 1;
let currentFps = 30;
let temporaryCropBox: ICropBox | null = null;
let currentSegmentIndex: number | null = 0;
let currentSubtitleIndex: number | null = null;

/**
 * Reset all imperative state to defaults
 * Useful when switching between videos or loading a new clip
 */
export const resetState = (): void => {
  currentFrame = 0;
  currentZoom = 1;
  currentDuration = 1;
  originalDuration = 1;
  currentFps = 30;
  currentSegmentIndex = 0;
  currentSubtitleIndex = null;
};

export const getCurrentZoom = (): number => {
  return currentZoom;
};

export const setCurrentZoom = (z: number): void => {
  currentZoom = z;
};

export const getCurrentFrame = (): number => {
  return currentFrame;
};

export const setCurrentFrame = (f: number): void => {
  // Always store integer values for frame to avoid Player initialFrame errors
  // Also ensure frame is within valid bounds (0 to duration-1)
  const roundedFrame = Math.round(f);
  const maxFrame = Math.max(0, currentDuration - 1);
  currentFrame = Math.min(Math.max(0, roundedFrame), maxFrame);
};

export const getCurrentDuration = (): number => {
  return currentDuration;
};

export const setCurrentDuration = (d: number): void => {
  // Ensure duration is at least 1 to avoid division by zero errors
  currentDuration = Math.max(1, Math.round(d));

  // If current frame is now out of bounds, adjust it
  if (currentFrame >= currentDuration) {
    currentFrame = Math.max(0, currentDuration - 1);
  }
};

export const getCurrentFps = (): number => {
  return currentFps;
};

export const setCurrentFps = (d: number): void => {
  currentFps = Math.max(1, d); // Ensure FPS is at least 1
};

export const getOriginalDuration = (): number => {
  return originalDuration;
};

export const setOriginalDuration = (d: number): void => {
  // Ensure duration is at least 1 to avoid division by zero errors
  originalDuration = Math.max(1, Math.round(d));
};

export const setTemporaryCropBox = (cropBox: ICropBox | null): void => {
  temporaryCropBox = cropBox;
};

export const getTemporaryCropBox = (): ICropBox | null => {
  return temporaryCropBox;
};

export const getCurrentSegmentIndex = (): number | null => {
  return currentSegmentIndex;
};

export const setCurrentSegmentIndex = (index: number | null): void => {
  currentSegmentIndex = index;
};

export const getCurrentSubtitleIndex = (): number | null => {
  return currentSubtitleIndex;
};

export const setCurrentSubtitleIndex = (index: number | null): void => {
  currentSubtitleIndex = index;
};

export const clearCurrentSubtitle = (): void => {
  currentSubtitleIndex = null;
};
