import { PlayerInternals } from "@remotion/player";
import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import { Internals, useCurrentFrame } from "remotion";
import { createStyles } from "@mantine/core";
// import { useIsStill } from "../helpers/is-current-selected-still";
// import { useKeybinding } from "../helpers/use-keybinding";
// import { InputDragger } from "./NewComposition/InputDragger";
import { renderFrame } from "./TestTimeline";

export const SPACING_UNIT = 8;

const useSpacingStyles = createStyles(
  (
    theme,
    { x = 0, y = 0, block = false }: { x?: number; y?: number; block?: boolean }
  ) => ({
    root: {
      display: block ? "block" : "inline-block",
      width: x * SPACING_UNIT,
      height: y * SPACING_UNIT,
      flexShrink: 0,
    },
  })
);

export const Spacing: React.FC<{
  readonly x?: number;
  readonly y?: number;
  readonly block?: boolean;
}> = ({ x = 0, y = 0, block = false }) => {
  const { classes } = useSpacingStyles({ x, y, block });
  return <div className={classes.root} />;
};

const useFlexStyles = createStyles(() => ({
  root: {
    flex: 1,
  },
}));

export const Flex: React.FC<{
  readonly children?: React.ReactNode;
}> = ({ children }) => {
  const { classes } = useFlexStyles();
  return <div className={classes.root}>{children}</div>;
};

const LIGHT_TEXT = "#A6A7A9";

const useTimeValueStyles = createStyles((theme) => ({
  text: {
    color: "white",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    fontVariantNumeric: "tabular-nums",
    lineHeight: 1,
    width: "100%",
    userSelect: "none",
    WebkitUserSelect: "none",
  },
  time: {
    display: "inline-block",
    fontSize: 16,
    lineHeight: 1,
    fontFamily: "monospace",
  },
  frame: {
    color: LIGHT_TEXT,
    fontWeight: 500,
    lineHeight: 1,
    fontSize: 16,
    fontFamily: "monospace",
    paddingRight: 10,
  },
}));

export const TimeValue: React.FC = () => {
  const { classes } = useTimeValueStyles();
  const frame = useCurrentFrame();
  const config = Internals.useUnsafeVideoConfig();
  //   const isStill = useIsStill();
  const isStill = false;
  const { seek } = PlayerInternals.usePlayer();
  //   const keybindings = useKeybinding();
  const ref = useRef<HTMLButtonElement>(null);

  const onTextChange = useCallback(
    (newVal: string) => {
      seek(parseInt(newVal, 10));
    },
    [seek]
  );
  const onValueChange = useCallback(
    (val: number) => {
      seek(val);
    },
    [seek]
  );

  //   useImperativeHandle(
  //     Internals.timeValueRef,
  //     () => ({
  //       goToFrame: () => {
  //         ref.current?.click();
  //       },
  //       seek,
  //     }),
  //     [seek]
  //   );

  //   useEffect(() => {
  //     const gKey = keybindings.registerKeybinding({
  //       event: "keypress",
  //       key: "g",
  //       callback: () => {
  //         ref.current?.click();
  //       },
  //       commandCtrlKey: false,
  //       preventDefault: true,
  //       triggerIfInputFieldFocused: false,
  //       keepRegisteredWhenNotHighestContext: false,
  //     });

  //     return () => {
  //       gKey.unregister();
  //     };
  //   }, [keybindings]);

  if (!config) {
    return null;
  }

  if (isStill) {
    return null;
  }

  return (
    <div className={classes.text}>
      <div className={classes.time}>{renderFrame(frame, config.fps)}</div>
      <Spacing x={2} />
      <Flex />
      {/* <InputDragger
        ref={ref}
        value={frame}
        onTextChange={onTextChange}
        onValueChange={onValueChange}
        rightAlign
        status="ok"
        style={frameStyle}
      /> */}
    </div>
  );
};
