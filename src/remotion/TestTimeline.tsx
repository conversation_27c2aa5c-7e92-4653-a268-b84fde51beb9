import React, { useContext, useEffect, useMemo, useRef } from "react";
import { Internals } from "remotion";
import { TimeValue } from "./TimeValue";

export const TIMELINE_TIME_INDICATOR_HEIGHT = 39;

export const BACKGROUND = "rgb(31,36,40)";
export const LIGHT_TEXT = "#A6A7A9";
export const TIMELINE_BACKGROUND = "#111";
export const TIMELINE_TRACK_SEPARATOR = "rgba(0, 0, 0, 0.3)";
export const TIMELINE_ITEM_BORDER_BOTTOM = 1;
export const TIMELINE_PADDING = 16;
export const SPLITTER_HANDLE_SIZE = 3;

export const getFrameIncrementFromWidth = (
  durationInFrames: number,
  width: number
) => {
  return (width - TIMELINE_PADDING * 2) / (durationInFrames - 1);
};

export const renderFrame = (frame: number, fps: number): string => {
  const hours = Math.floor(frame / fps / 3600);

  const remainingMinutes = frame - hours * fps * 3600;
  const minutes = Math.floor(remainingMinutes / 60 / fps);

  const remainingSec = frame - hours * fps * 3600 - minutes * fps * 60;
  const seconds = Math.floor(remainingSec / fps);

  const frameAfterSec = Math.round(frame % fps);

  const hoursStr = String(hours);
  const minutesStr = String(minutes).padStart(2, "0");
  const secondsStr = String(seconds).padStart(2, "0");
  const frameStr = String(frameAfterSec).padStart(2, "0");

  if (hours > 0) {
    return `${hoursStr}:${minutesStr}:${secondsStr}.${frameStr}`;
  }

  return `${minutesStr}:${secondsStr}.${frameStr}`;
};

const container: React.CSSProperties = {
  height: TIMELINE_TIME_INDICATOR_HEIGHT - 4,
  boxShadow: `0 0 4px ${TIMELINE_BACKGROUND}`,
  //   position: "absolute",
  backgroundColor: TIMELINE_BACKGROUND,
  //   top: 0,
};

const tick: React.CSSProperties = {
  width: 1,
  backgroundColor: "#fff",
  height: 20,
  position: "absolute",
};

const secondTick: React.CSSProperties = {
  ...tick,
  height: 15,
};

const tickLabel: React.CSSProperties = {
  fontSize: 12,
  color: LIGHT_TEXT,
  position: "absolute",
  left: "50%",
  top: 15,
  transform: "translateX(-50%)",
};

const timeValue: React.CSSProperties = {
  height: TIMELINE_TIME_INDICATOR_HEIGHT,
  position: "absolute",
  top: 0,
  width: "100%",
  paddingLeft: 10,
  display: "flex",
  alignItems: "center",
  backgroundColor: BACKGROUND,
  borderBottom: `${TIMELINE_ITEM_BORDER_BOTTOM}px solid ${TIMELINE_TRACK_SEPARATOR}`,
};

export const TimelineTimePlaceholders: React.FC = () => {
  return (
    <div style={timeValue}>
      <TimeValue />
    </div>
  );
};

export const TimelineTimePadding: React.FC = () => {
  return (
    <div
      style={{
        height: TIMELINE_TIME_INDICATOR_HEIGHT,
      }}
    />
  );
};

type TimelineTick = {
  frame: number;
  style: React.CSSProperties;
  showTime: boolean;
};

export const TimelineTimeIndicators: React.FC = ({}) => {
  const sliderTrack = width;
  const video = {
    durationInFrames: 45 * 25,
    fps: 25,
  };

  if (sliderTrack === null) {
    return null;
  }

  //   if (video === null) {
  //     return null;
  //   }

  return (
    <Inner
      durationInFrames={video.durationInFrames}
      fps={video.fps}
      windowWidth={sliderTrack}
    />
  );
};

const Inner: React.FC<{
  readonly windowWidth: number;
  readonly fps: number;
  readonly durationInFrames: number;
}> = ({ windowWidth, durationInFrames, fps }) => {
  const ref = useRef<HTMLDivElement>(null);
  const timelineVerticalScroll = React.createRef<HTMLDivElement>();

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) {
      return;
    }

    const { current } = timelineVerticalScroll;
    if (!current) {
      return;
    }

    const onScroll = () => {
      currentRef.style.top = current.scrollTop + "px";
    };

    current.addEventListener("scroll", onScroll);
    return () => {
      current.removeEventListener("scroll", onScroll);
    };
  }, []);

  const style: React.CSSProperties = useMemo(() => {
    return {
      ...container,
      width: windowWidth - SPLITTER_HANDLE_SIZE / 2,
      overflow: "hidden",
      // Since
      marginLeft: SPLITTER_HANDLE_SIZE / 2,
      pointerEvents: "none",
    };
  }, [windowWidth]);

  const ticks: TimelineTick[] = useMemo(() => {
    const frameInterval = getFrameIncrementFromWidth(
      durationInFrames,
      windowWidth
    );

    const MIN_SPACING_BETWEEN_TICKS_PX = 5;

    const seconds = Math.floor(durationInFrames / fps);
    const secondMarkerEveryNth = Math.ceil(
      (MIN_SPACING_BETWEEN_TICKS_PX * fps) / (frameInterval * fps)
    );
    const frameMarkerEveryNth = Math.ceil(
      MIN_SPACING_BETWEEN_TICKS_PX / frameInterval
    );

    // Big ticks showing for every second
    const secondTicks: TimelineTick[] = new Array(seconds)
      .fill(true)
      .map((_, index) => {
        return {
          frame: index * fps,
          style: {
            ...secondTick,
            left:
              frameInterval * index * fps +
              TIMELINE_PADDING -
              SPLITTER_HANDLE_SIZE / 2,
          },
          showTime: index > 0,
        };
      })
      .filter((_, idx) => idx % secondMarkerEveryNth === 0);

    const frameTicks: TimelineTick[] = new Array(durationInFrames)
      .fill(true)
      .map((_, index) => {
        return {
          frame: index,
          style: {
            ...tick,
            left:
              frameInterval * index +
              TIMELINE_PADDING -
              SPLITTER_HANDLE_SIZE / 2,
            height:
              index % fps === 0
                ? 10
                : (index / frameMarkerEveryNth) % 2 === 0
                ? 5
                : 2,
          },
          showTime: false,
        };
      })
      .filter((_, idx) => idx % frameMarkerEveryNth === 0);

    // Merge and deduplicate ticks
    const hasTicks: number[] = [];
    return [...secondTicks, ...frameTicks].filter((t) => {
      const alreadyUsed = hasTicks.find((ht) => ht === t.frame) !== undefined;
      hasTicks.push(t.frame);
      return !alreadyUsed;
    });
  }, [durationInFrames, fps, windowWidth]);

  return (
    <div ref={ref} style={style}>
      {ticks.map((t) => {
        return (
          <div key={t.frame} style={t.style}>
            {t.showTime ? (
              <div style={tickLabel}>{renderFrame(t.frame, fps)}</div>
            ) : null}
          </div>
        );
      })}
    </div>
  );
};
