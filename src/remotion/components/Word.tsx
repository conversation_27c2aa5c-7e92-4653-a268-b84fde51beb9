import React, { useImperativeHandle, useRef } from "react";
import { spring, useCurrentFrame, useVideoConfig } from "remotion";
import { PresetConfig, ISubtitleWord } from "../types";
import { useRemotionContext } from "../RemotionContext";
import { AnimationType, getAnimationStyles } from "./../helpers";

interface WordCompProps {
  word: ISubtitleWord;
  index: number;
  animationType: AnimationType;
  subtitlesConfig: PresetConfig;
}

const WordComp: React.ForwardRefRenderFunction<
  HTMLSpanElement,
  WordCompProps
> = ({ word, index, animationType, subtitlesConfig }, ref) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();

  // Calculate exact frame numbers instead of using timeInSeconds
  const wordStartFrame = Math.floor(word.start * fps);
  const wordEndFrame = Math.floor(word.end * fps);
  const isShown = frame >= wordStartFrame && frame <= wordEndFrame;

  const componentRef = useRef<HTMLSpanElement>(null);
  useImperativeHandle(ref, () => componentRef.current as HTMLSpanElement);

  // Smooth interpolation for background opacity
  const backgroundOpacity = spring({
    frame: frame - wordStartFrame,
    fps,
    config: {
      damping: 80,
      mass: 0.4,
      stiffness: 120,
      overshootClamping: true,
    },
  });

  // Animation styles for the highlighted word
  const highlightAnimationStyles = isShown
    ? getAnimationStyles(animationType, {
        frame,
        fps,
        startFrame: wordStartFrame,
        endFrame: wordEndFrame,
      })
    : {};

  // Determine text color based on active state and configuration
  let textColor = subtitlesConfig.color;

  const backgroundColor = isShown
    ? subtitlesConfig.current_word_bg_color || "transparent"
    : "transparent";

  // Function to brighten a color by a percentage
  const brightenColor = (color: string, percent: number): string => {
    // Handle hex format
    if (color.startsWith("#")) {
      // Remove the hash
      const hex = color.slice(1);

      // Parse the hex values
      let r = parseInt(hex.substring(0, 2), 16);
      let g = parseInt(hex.substring(2, 4), 16);
      let b = parseInt(hex.substring(4, 6), 16);

      // Brighten each component
      r = Math.min(255, Math.floor(r * (1 + percent / 100)));
      g = Math.min(255, Math.floor(g * (1 + percent / 100)));
      b = Math.min(255, Math.floor(b * (1 + percent / 100)));

      // Convert back to hex
      return `#${r.toString(16).padStart(2, "0")}${g
        .toString(16)
        .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
    }

    return color;
  };

  // Apply current word color if available and this word is shown
  if (isShown && subtitlesConfig.current_word_color) {
    textColor = subtitlesConfig.current_word_color;
  }

  // Apply amplified word color if available (for special words)
  // This is a placeholder check - you may need to implement a real condition
  // based on your application logic for when a word should be 'amplified'
  const isAmplified = false; // Replace with actual logic if needed
  if (isShown && isAmplified && subtitlesConfig.amplified_word_color) {
    textColor = subtitlesConfig.amplified_word_color;
  }

  // Apply highlight color if available
  if (isShown && subtitlesConfig.current_word_color) {
    textColor = subtitlesConfig.current_word_color;
  } else if (isShown) {
    // If no specific highlight color but word is active, brighten the base color
    textColor = brightenColor(textColor, 15); // Brighten by 15%
  }

  // Scale transform for current word
  const transform = isShown ? `scale(1.05)` : "scale(1)";

  // Make stroke thinner for active words to emphasize color
  const adjustedStrokeStyle: React.CSSProperties = {};

  if (subtitlesConfig.stroke) {
    const originalWeight = parseFloat(
      (subtitlesConfig.stroke.weight / 2).toString()
    );
    adjustedStrokeStyle.WebkitTextStroke = `${originalWeight}px ${subtitlesConfig.stroke.color}`;
  }

  const padding = "0 5px";

  return (
    <span
      ref={componentRef}
      style={{
        borderRadius: "10px",
        color: textColor,
        display: "inline-block",
        justifyContent: "center",
        backgroundColor,
        transform,
        transition: "transform 0.1s ease-out, color 0.1s ease-out",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
        textRendering: "geometricPrecision",
        padding,
        // Apply thinner stroke for active words
        ...adjustedStrokeStyle,
        // Remove CSS transitions since we're handling everything with springs
        ...highlightAnimationStyles,
      }}
    >
      {word.word}
    </span>
  );
};

export const WordComponent = React.forwardRef(WordComp);
