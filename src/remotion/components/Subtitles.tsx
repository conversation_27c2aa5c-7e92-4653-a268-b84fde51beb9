import React, { useMemo, useState, useEffect } from "react";
import { SegmentComp } from "./Segment";
import Draggable, { DraggableData, DraggableEvent } from "react-draggable";
import { findSubtitle } from "../../utils";
import { useCurrentFrame, useCurrentScale, useVideoConfig } from "remotion";
import { AnimationType } from "./../helpers";
import { ISubtitles, IVideo, IVideoMetadata } from "../types";

interface SubtitlesProps {
  animationType?: AnimationType;
  subtitles: ISubtitles;
  sourceVideoMetadata: IVideoMetadata;
  compositionDimensions: { width: number; height: number };
  configUpdate?: (subtitles: ISubtitles) => void;
}

export const Subtitles: React.FC<SubtitlesProps> = ({
  animationType = "fade",
  subtitles,
  sourceVideoMetadata,
  compositionDimensions,
  configUpdate,
}) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  const timeInSeconds = frame / fps;
  const scale = useCurrentScale();

  // Calculate scaled Y position based on current dimensions
  const scaledInitialY = useMemo(() => {
    return (
      (subtitles.position?.y *
        (compositionDimensions.height / sourceVideoMetadata.height)) /
      scale
    );
  }, [
    subtitles.position?.y,
    compositionDimensions.height,
    sourceVideoMetadata.height,
  ]);

  // Track position with state to maintain controlled component
  const [position, setPosition] = useState({ x: 0, y: scaledInitialY });

  // Update position when scaledInitialY changes
  useEffect(() => {
    setPosition({ x: 0, y: scaledInitialY });
  }, [scaledInitialY]);

  const subtitlesDragHandler = (e: DraggableEvent, data: DraggableData) => {
    const scaledY =
      data.y *
      (sourceVideoMetadata.height / compositionDimensions.height) *
      scale;

    setPosition({ x: 0, y: data.y });

    // Update subtitles with new position
    // The parent component (ClipPreview) will handle preserving items
    configUpdate?.({
      ...subtitles,
      position: { x: 0, y: scaledY },
    });
  };

  const subtitlesDragStartHandler = (e: DraggableEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const currentSegment = findSubtitle(subtitles?.items || [], timeInSeconds);

  // Ensure we have a valid configuration with all required fields
  const validSubtitles = {
    ...subtitles,
    config: {
      ...subtitles.config,
    },
  };

  return (
    <div style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0 }}>
      <Draggable
        scale={scale}
        axis="y"
        position={position}
        bounds={"parent"}
        onStop={subtitlesDragHandler}
        onStart={subtitlesDragStartHandler}
      >
        <div
          style={{
            width: "100%",
            zIndex: 2000,
            position: "absolute",
            transform: `scale(${1 / scale})`, // Counter-scale to ensure proper rendering
            transformOrigin: "center bottom",
            filter: "drop-shadow(0 0 0.5px rgba(0,0,0,0.3))", // Subtle drop shadow for better legibility
          }}
        >
          {currentSegment && (
            <SegmentComp
              segment={currentSegment}
              animationType={animationType}
              subtitles={validSubtitles}
              sourceVideoMetadata={sourceVideoMetadata}
              compositionDimensions={compositionDimensions}
            />
          )}
        </div>
      </Draggable>
    </div>
  );
};
