// TimeDisplay.tsx
import React, { useRef, useEffect, useMemo, useState } from "react";
import {
  getCurrentFps,
  getCurrentFrame,
  getCurrentDuration,
} from "../imperative-state";

// Create a mutable ref object
const timeDisplayUpdateRef = {
  current: {
    updateTime: (frame: number) => {
      // Initial empty implementation
    },
  },
};

export { timeDisplayUpdateRef };

// Format time from frames
const formatTimeFromFrames = (frame: number, fps: number): string => {
  const totalSeconds = frame / fps;
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
};

export const DisplayTime: React.FC = () => {
  const currentTimeRef = useRef<HTMLSpanElement>(null);
  const totalTimeRef = useRef<HTMLSpanElement>(null);
  const initialFrame = getCurrentFrame();
  const fps = getCurrentFps();
  const duration = getCurrentDuration();
  const [currentTimeText, setCurrentTimeText] = useState<string>("");
  const [totalTimeText, setTotalTimeText] = useState<string>("");

  // Styles from Figma design
  const timeDisplayContainer: React.CSSProperties = {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    alignSelf: "stretch",
    margin: "10px 0",
    width: "100%",
  };

  const timeDisplayWrapper: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    minWidth: "200px", // Fixed width to prevent size changes
  };

  const currentTimeStyle: React.CSSProperties = {
    color: "#FFC773",
    width: "84px", // Fixed width for current time
    display: "inline-block",
    textAlign: "right",
    fontVariantNumeric: "tabular-nums", // Keep numbers same width
  };

  const separatorStyle: React.CSSProperties = {
    color: "#FFFFFF",
    margin: "0 8px", // Add spacing around the separator
    fontVariantNumeric: "tabular-nums", // Keep numbers same width
  };

  const totalTimeStyle: React.CSSProperties = {
    color: "#FFFFFF",
    width: "84px", // Fixed width for total time
    display: "inline-block",
    textAlign: "left",
    fontVariantNumeric: "tabular-nums", // Keep numbers same width
  };

  // Initial time for first render
  useEffect(() => {
    const currentTimeString = formatTimeFromFrames(initialFrame, fps);
    const totalTimeString = formatTimeFromFrames(duration, fps);
    setCurrentTimeText(currentTimeString);
    setTotalTimeText(totalTimeString);
  }, [initialFrame, fps, duration]);

  // Expose imperative update method
  useEffect(() => {
    // Update the updateTime method without reassigning the current property
    Object.assign(timeDisplayUpdateRef.current, {
      updateTime: (frame: number) => {
        if (!currentTimeRef.current || !totalTimeRef.current) return;

        const currentTimeString = formatTimeFromFrames(frame, getCurrentFps());
        const totalTimeString = formatTimeFromFrames(
          getCurrentDuration(),
          getCurrentFps()
        );

        // Update both the DOM elements and the state
        currentTimeRef.current.textContent = currentTimeString;
        totalTimeRef.current.textContent = totalTimeString;
        setCurrentTimeText(currentTimeString);
        setTotalTimeText(totalTimeString);
      },
    });
  }, []);

  return (
    <div style={timeDisplayContainer}>
      <div style={timeDisplayWrapper}>
        <span ref={currentTimeRef} className="p-heavy" style={currentTimeStyle}>
          {currentTimeText}
        </span>
        <span className="p-heavy" style={separatorStyle}>
          /
        </span>
        <span ref={totalTimeRef} className="p-heavy" style={totalTimeStyle}>
          {totalTimeText}
        </span>
      </div>
    </div>
  );
};
