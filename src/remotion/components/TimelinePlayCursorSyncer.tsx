import React, { useEffect, useRef } from "react";
import { PlayerInternals } from "@remotion/player";
import {
  setCurrentFrame,
  setCurrentDuration,
  setCurrentFps,
  getCurrentFrame,
  getCurrentDuration,
} from "../imperative-state";
import {
  ensureFrameIsInViewport,
  isCursorInViewport,
} from "./timeline-scroll-logic";
import { playerRef, scrollableRef } from "./timeline-refs";

// Keep track of the state when the last play happened
type TimelinePosition = {
  scrollLeft: number;
  frame: number;
  zoomLevel: number;
  durationInFrames: number;
};

// Maintain this as a module-level variable (outside component)
let lastTimelinePositionWhileScrolling: TimelinePosition | null = null;

export const TimelinePlayCursorSyncer: React.FC = () => {
  const prevPlayingRef = useRef<boolean>(false);

  // Effect to synchronize player frame to imperative state
  useEffect(() => {
    // Initial sync
    // setCurrentFrame(player.getCurrentFrame());
    // setCurrentDuration(player.getDuration());
    // setCurrentFps(player.getFrameRate());

    // Subscribe to frame changes
    playerRef?.current?.addEventListener("frameupdate", (e) => {
      const frame: number = e.detail.frame;

      // Handle auto-scrolling during playback
      if (playerRef?.current?.isPlaying()) {
        ensureFrameIsInViewport({
          direction: "page-right",
          durationInFrames: getCurrentDuration(),
          frame,
        });
      }
    });

    // // Subscribe to play/pause changes
    // playerRef?.current?.addEventListener("ratechange", (playing) => {
    //   const current = scrollableRef.current;

    //   if (!current) return;

    //   if (playing) {
    //     // Store position when starting playback
    //     lastTimelinePositionWhileScrolling = {
    //       scrollLeft: current.scrollLeft,
    //       frame: getCurrentFrame(),
    //       zoomLevel: 1, // Or retrieve from your zoom state
    //       durationInFrames: player.getDuration(),
    //     };
    //   } else if (
    //     // Check if we should restore position (if not in viewport)
    //     prevPlayingRef.current &&
    //     lastTimelinePositionWhileScrolling !== null &&
    //     !isCursorInViewport({
    //       frame: getCurrentFrame(),
    //       durationInFrames: player.getDuration(),
    //     })
    //   ) {
    //     // Restore scroll position
    //     current.scrollLeft = lastTimelinePositionWhileScrolling.scrollLeft;
    //   }

    //   prevPlayingRef.current = playing;
    // });

    // Clean up all listeners
    return () => {
      //   unsubscribeFrame();
      //   unsubscribePlayback();
    };
  }, [playerRef]);

  // Effect to sync if duration changes
  //   useEffect(() => {
  //     const unsubscribe = player.addEventListener(
  //       "durationchange",
  //       (duration) => {
  //         setCurrentDuration(duration);
  //       }
  //     );

  //     return unsubscribe;
  //   }, [player]);

  return null; // This component doesn't render anything
};
