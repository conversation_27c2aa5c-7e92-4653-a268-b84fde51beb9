import React, {
  use<PERSON><PERSON>back,
  useContext,
  useEffect,
  useImperative<PERSON>andle,
  useMemo,
  useRef,
  useState,
} from "react";
import { IVideoSegment } from "../types";
import { TimelineWidthContext } from "../context/TimelineWidthProvider";
import { TimelineZoomCtx } from "../context/timeline-zoom";
import {
  getCurrentDuration,
  getCurrentFps,
  getCurrentSegmentIndex,
  setCurrentSegmentIndex,
} from "../imperative-state";
import { TIMELINE_PADDING } from "../helpers";
import { getXPositionOfItemInTimelineImperatively } from "../get-left-of-timeline-slider";
import { timelineSegmentsRef } from "./timeline-refs";
import {
  useClipConfigAPI,
  useClipSegments,
} from "../../features/clipConfig/ClipConfigApi";

/**
 * TimelineSegments Component
 *
 * This component manages the visual representation of video segments on the timeline.
 * For performance reasons, it uses a hybrid approach to state management:
 *
 * 1. IMPERATIVE STATE FOR ACTIVE SEGMENT:
 *    - The currently playing/active segment is managed fully imperatively
 *    - We rely on getCurrentSegmentIndex() from imperative-state.ts
 *    - Updates are propagated via DOM manipulations and data attributes
 *    - This avoids React re-renders during frequent frame updates
 *
 * 2. DECLARATIVE STATE FOR SELECTED SEGMENT:
 *    - The segment currently selected for editing uses React state
 *    - Selection changes are less frequent so React rendering is acceptable
 *
 * The imperative updates use data attributes and direct DOM manipulation to
 * achieve smooth performance during video playback and scrubbing.
 */

// Configuration for clip editing mode
interface MappingConfig {
  originalToClipTime: (time: number) => number;
  clipToOriginalTime: (time: number) => number;
}

const segmentContainer: React.CSSProperties = {
  position: "absolute",
  top: 45,
  height: 56,
  pointerEvents: "all",
  zIndex: 10,
  left: 0,
  right: 0,
  overflow: "hidden",
};

const segmentStyle: React.CSSProperties = {
  position: "absolute",
  height: "100%",
  backgroundColor: "rgba(70, 130, 220, 0.7)",
  borderRadius: 4,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  userSelect: "none",
  WebkitUserSelect: "none",
  overflow: "visible",
  boxSizing: "border-box",
  cursor: "pointer",
  boxShadow: "0 1px 3px rgba(0, 0, 0, 0.2)",
  border: "1px solid rgba(255, 255, 255, 0.1)",
};

const loadingSegmentStyle: React.CSSProperties = {
  ...segmentStyle,
  backgroundColor: "rgba(70, 130, 180, 0.4)",
  backgroundImage:
    "linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)",
  backgroundSize: "40px 40px",
  animation: "segment-loading 1s linear infinite",
};

const handleStyle: React.CSSProperties = {
  position: "absolute",
  width: "4px",
  height: "20px",
  backgroundColor: "#ffffff",
  cursor: "ew-resize",
  borderRadius: "100px",
  zIndex: 20,
  userSelect: "none",
  WebkitUserSelect: "none",
  display: "none",
  top: "50%",
  transform: "translateY(-50%)",
};

const breakIndicatorStyle: React.CSSProperties = {
  position: "absolute",
  height: "2px",
  backgroundColor: "#73A0FF",
  top: "0",
  zIndex: 50,
};

const filmstripStyle: React.CSSProperties = {
  position: "absolute",
  width: "100%",
  height: "56px",
  top: 0,
  left: 0,
  display: "flex",
  flexDirection: "row",
  overflow: "hidden",
  pointerEvents: "none",
  flexWrap: "nowrap",
  zIndex: 5,
};

const frameStyle: React.CSSProperties = {
  height: "100%",
  width: "100px", // Fixed width for each frame
  flexGrow: 0,
  flexShrink: 0, // Never shrink frames
  margin: 0,
  padding: 0,
  backgroundSize: "contain",
  backgroundPosition: "center",
  backgroundRepeat: "no-repeat",
  imageRendering: "auto",
  minWidth: "100px", // Ensure minimum width
  opacity: 1,
  zIndex: 5, // Ensure frames are visible
};

type DragState =
  | { dragging: false }
  | {
      dragging: "left" | "right";
      segmentIndex: number;
      initialPosition: number;
      initialStart: number;
      initialEnd: number;
    };

const MIN_SEGMENT_DURATION = 1;

// Frame cache to avoid re-fetching frames
const frameCache: Map<string, string> = new Map();

// Frame priority queue for preloading
const framePreloadQueue: string[] = [];
let isPreloading = false;

export const TimelineSegments: React.FC<{
  sourceVideoUrl?: string;
}> = ({ sourceVideoUrl }) => {
  const timelineWidth = useContext(TimelineWidthContext);
  const { zoom } = useContext(TimelineZoomCtx);
  const containerRef = useRef<HTMLDivElement>(null);
  const [dragState, setDragState] = useState<DragState>({ dragging: false });
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const [shouldUpdateFilmstrip, setShouldUpdateFilmstrip] = useState(false);
  const segments = useClipSegments();
  const segmentsRef = useRef<IVideoSegment[]>(segments);
  const { onSegmentsChange } = useClipConfigAPI();

  // Use the current segment index from imperative state
  const getActiveSegmentIndex = useCallback(() => {
    return getCurrentSegmentIndex();
  }, []);

  // Helper function to update active segment display without re-rendering
  const refreshActiveSegment = useCallback(() => {
    if (!containerRef.current) return;

    const currentActiveIndex = getCurrentSegmentIndex();

    containerRef.current.setAttribute(
      "data-active-index",
      currentActiveIndex?.toString() ?? "null"
    );

    // Find all segment elements and update their active state
    const segmentElements =
      containerRef.current.querySelectorAll("[data-index]");
    segmentElements.forEach((element) => {
      const segIndex = parseInt(element.getAttribute("data-index") || "-1");
      const isActive = segIndex === currentActiveIndex;

      // Update data attributes
      element.setAttribute("data-active", isActive ? "true" : "false");
      // Use the same attribute for both active and "selected" visualization
      element.setAttribute("data-selected", isActive ? "true" : "false");

      // Update appearance based on active status only
      if (isActive) {
        // Active segment: bright blue with white border
        Object.assign((element as HTMLElement).style, {
          backgroundColor: "rgba(100, 180, 255, 0.8)",
          border: "2px solid white",
          boxShadow: "0 0 8px rgba(255, 255, 255, 0.5)",
          zIndex: "20",
        });
      } else {
        // Inactive segment: normal blue
        Object.assign((element as HTMLElement).style, {
          backgroundColor: "rgba(70, 130, 220, 0.7)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.2)",
          zIndex: "10",
        });
      }

      // Find all resize handles in this segment
      const leftHandle =
        (element.querySelector('[data-handle="left"]') as HTMLElement) ||
        (element.querySelector('[style*="left: 0"]') as HTMLElement);
      const rightHandle =
        (element.querySelector('[data-handle="right"]') as HTMLElement) ||
        (element.querySelector('[style*="right: 0"]') as HTMLElement);

      // Update handle visibility based on active state
      if (leftHandle) {
        leftHandle.style.display = isActive ? "block" : "none";
      }

      if (rightHandle) {
        rightHandle.style.display = isActive ? "block" : "none";
      }

      // Also update the corresponding indicator
      const segLeft = parseFloat((element as HTMLElement).style.left);
      const indicators = containerRef.current?.querySelectorAll(
        '[style*="position: absolute"][style*="height: 2px"]'
      );
      if (indicators) {
        Array.from(indicators).forEach((indicator) => {
          const indLeft = parseFloat((indicator as HTMLElement).style.left);
          if (Math.abs(indLeft - segLeft) <= 2) {
            if (isActive) {
              (indicator as HTMLElement).style.backgroundColor = "#FFFFFF";
            } else {
              (indicator as HTMLElement).style.backgroundColor = "#73A0FF";
            }
          }
        });
      }
    });
  }, []);

  // Calculate mapping functions for clip mode
  const mappingConfig = useMemo<MappingConfig>(() => {
    if (segments.length === 0) {
      // In video mode or no segments, use identity mapping
      return {
        originalToClipTime: (time) => time,
        clipToOriginalTime: (time) => time,
      };
    }

    // Find minimum start time to use as offset
    const minStartTime = Math.min(...segments.map((segment) => segment.start));

    return {
      // Convert source video time to clip timeline time
      originalToClipTime: (time) => time - minStartTime,
      // Convert clip timeline time back to source video time
      clipToOriginalTime: (time) => time + minStartTime,
    };
  }, [segments]);

  // Update segment click to directly update the active segment
  const handleSegmentClick = useCallback(
    (index: number) => {
      // Update the imperative state
      setCurrentSegmentIndex(index);

      // Immediately refresh to update UI
      setTimeout(() => {
        refreshActiveSegment();

        // Also ensure filmstrip visibility
        if (containerRef.current) {
          // Make sure all filmstrip containers are visible
          const filmstripContainers = containerRef.current.querySelectorAll(
            ".filmstrip-container"
          );
          filmstripContainers.forEach((container) => {
            (container as HTMLElement).style.visibility = "visible";
            (container as HTMLElement).style.opacity = "1";
          });

          // Make sure all frames are visible too
          const frames = containerRef.current.querySelectorAll(
            '[style*="backgroundImage"]'
          );
          frames.forEach((frame) => {
            (frame as HTMLElement).style.visibility = "visible";
            (frame as HTMLElement).style.opacity = "1";
          });

          // Get the active segment
          const activeSegment = containerRef.current.querySelector(
            `[data-index="${index}"]`
          );
          if (activeSegment) {
            // Make sure the active segment allows the filmstrip to be visible
            (activeSegment as HTMLElement).style.overflow = "visible";

            // Get the filmstrip within the active segment
            const activeFilmstrip = activeSegment.querySelector(
              `[data-filmstrip="${index}"]`
            );
            if (activeFilmstrip) {
              // Ensure filmstrip is visible
              (activeFilmstrip as HTMLElement).style.visibility = "visible";
              (activeFilmstrip as HTMLElement).style.opacity = "1";
              (activeFilmstrip as HTMLElement).style.display = "flex";
            }
          }
        }
      }, 0);
    },
    [refreshActiveSegment, containerRef]
  );

  // Initialize video and canvas elements for frame extraction
  useEffect(() => {
    if (!sourceVideoUrl) return;

    // Create video element if it doesn't exist
    if (!videoRef.current) {
      const video = document.createElement("video");
      video.crossOrigin = "anonymous";
      video.muted = true;
      video.playsInline = true;
      // Preload video data for faster seeking
      video.preload = "none";

      // Add event listeners for debugging
      video.addEventListener("error", (e) => {
        // Video loading error handling
      });

      video.addEventListener("loadeddata", () => {
        // Video data loaded successfully
      });

      videoRef.current = video;
    }

    // Create canvas for frame extraction if it doesn't exist
    if (!canvasRef.current) {
      const canvas = document.createElement("canvas");
      canvas.width = 200;
      canvas.height = 112;
      canvasRef.current = canvas;
    }

    // Load video if URL changes
    const video = videoRef.current;
    if (video.src !== sourceVideoUrl) {
      video.src = sourceVideoUrl;

      // Force load and play a bit to ensure the video is ready
      video.load();

      // Try to pre-buffer the video
      const playPromise = video.play();
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            // Video playing for pre-buffering
            // Immediately pause after playing starts to pre-buffer
            setTimeout(() => {
              video.pause();
              // Video paused after pre-buffering
            }, 500);
          })
          .catch((err) => {
            // Auto-play for pre-buffering was prevented
            // No problem, we can still extract frames
          });
      }
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.pause();
        videoRef.current.src = "";
        videoRef.current.load(); // Clear buffer
      }
    };
  }, [sourceVideoUrl]);

  // Update the ref when segments change from props
  useEffect(() => {
    segmentsRef.current = segments;
  }, [segments]);

  // Modify the secondsToPixels function to use originalDuration if provided
  const secondsToPixels = useCallback(
    (seconds: number) => {
      // Apply mapping for clip mode
      const mappedTime = mappingConfig.originalToClipTime(seconds);

      // Use originalDuration if provided, otherwise use getCurrentDuration()
      const durationInFrames = getCurrentDuration();
      const fps = getCurrentFps();
      const frame = Math.round(mappedTime * fps);
      return getXPositionOfItemInTimelineImperatively(
        frame,
        durationInFrames,
        timelineWidth ?? 0
      );
    },
    [timelineWidth, mappingConfig, getCurrentDuration, getCurrentFps]
  );

  const containerStyle = useMemo(
    () => ({
      ...segmentContainer,
      width: timelineWidth ?? 0,
    }),
    [timelineWidth]
  );

  // Extract a frame from the video at a specific time
  const extractFrame = useCallback(
    async (timeInSeconds: number, quality: number = 0.85): Promise<string> => {
      if (!videoRef.current || !canvasRef.current) return "";

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      if (!ctx) return "";

      // Check if frame is already cached
      const cacheKey = `${sourceVideoUrl}-${timeInSeconds.toFixed(2)}`;
      if (frameCache.has(cacheKey)) {
        return frameCache.get(cacheKey) as string;
      }

      return new Promise((resolve) => {
        // Create timeout to avoid hanging - increase timeout to 5 seconds
        const timeoutId = setTimeout(() => {
          // Frame extraction timed out
          resolve(""); // Return empty on timeout
        }, 5000); // Increased from 2000 to 5000ms

        // Safety check for video state
        if (!video.src || video.src !== sourceVideoUrl) {
          video.src = sourceVideoUrl || "";
          video.load();
        }

        // If video is not loaded, set up a listener
        if (video.readyState < 2) {
          const loadHandler = () => {
            video.removeEventListener("loadeddata", loadHandler);
            video.currentTime = timeInSeconds;
          };
          video.addEventListener("loadeddata", loadHandler);
        } else {
          video.currentTime = timeInSeconds;
        }

        // Once the video seeks to the specified time, capture the frame
        const seekHandler = () => {
          try {
            clearTimeout(timeoutId);

            // Use a smaller canvas for thumbnails to improve performance
            canvas.width = 160; // Reduced from 200
            canvas.height = 90; // Reduced from 112

            // Set image smoothing quality to medium for better performance
            if (ctx instanceof CanvasRenderingContext2D) {
              ctx.imageSmoothingEnabled = true;
              ctx.imageSmoothingQuality = "medium"; // Changed from high to medium
            }

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Calculate dimensions preserving aspect ratio
            const videoWidth = video.videoWidth;
            const videoHeight = video.videoHeight;
            const videoAspect = videoWidth / videoHeight;

            let drawWidth,
              drawHeight,
              offsetX = 0,
              offsetY = 0;

            // Determine if this is vertical video
            const isVertical = videoHeight > videoWidth;

            if (isVertical) {
              // For vertical video (9:16 or similar)
              drawHeight = canvas.height;
              drawWidth = drawHeight * videoAspect;
              // Center horizontally
              offsetX = (canvas.width - drawWidth) / 2;
            } else {
              // For horizontal video
              drawWidth = canvas.width;
              drawHeight = drawWidth / videoAspect;
              // Center vertically
              offsetY = (canvas.height - drawHeight) / 2;
            }

            // Draw the frame to canvas with aspect ratio preserved
            ctx.drawImage(video, offsetX, offsetY, drawWidth, drawHeight);

            // Get image data with specified quality - use lower quality to speed up and reduce memory
            const dataUrl = canvas.toDataURL(
              "image/jpeg",
              Math.min(quality, 0.6)
            );

            // Cache the result
            frameCache.set(cacheKey, dataUrl);

            resolve(dataUrl);
          } catch (e) {
            // Error extracting frame
            clearTimeout(timeoutId);
            resolve("");
          }

          video.removeEventListener("seeked", seekHandler);
        };

        video.addEventListener("seeked", seekHandler);
      });
    },
    [sourceVideoUrl]
  );

  // Preload frames in background
  const preloadFrames = useCallback(
    async (times: number[]) => {
      if (isPreloading || !sourceVideoUrl) return;

      isPreloading = true;

      // Process queue in background
      setTimeout(async () => {
        for (const time of times) {
          const cacheKey = `${sourceVideoUrl}-${time.toFixed(2)}`;

          // Skip if already cached or in progress
          if (
            frameCache.has(cacheKey) ||
            framePreloadQueue.includes(cacheKey)
          ) {
            continue;
          }

          framePreloadQueue.push(cacheKey);

          // Extract with lower quality first for speed
          await extractFrame(time, 0.6);
        }

        isPreloading = false;
      }, 10);
    },
    [sourceVideoUrl, extractFrame]
  );

  // Generate filmstrip for a segment
  const generateFilmstrip = useCallback(
    async (segment: IVideoSegment, segmentWidth: number) => {
      if (!sourceVideoUrl || segmentWidth <= 0) {
        // Cannot generate filmstrip: no video URL or invalid width
        return [];
      }

      const duration = segment.end - segment.start;
      if (duration <= 0) {
        // Invalid segment duration
        return [];
      }

      // Calculate optimal number of frames based on visible width
      // For performance, limit the maximum number of frames to render, but ensure we cover the full segment
      const maxFramesToRender = 30; // Increased from 10 to ensure we cover wider segments

      // Calculate frames based on segment width divided by fixed frame size (100px)
      const desiredFramesCount = Math.ceil(segmentWidth / 100);

      // Clamp to maximum for performance
      const numberOfFrames = Math.min(
        Math.max(1, desiredFramesCount), // At least 1 frame
        maxFramesToRender
      );

      const frameUrls: string[] = [];
      const timesToPreload: number[] = [];

      // Extract frames at regular intervals across the segment
      const interval = duration / numberOfFrames;

      // Create an array of promises for parallel extraction
      const framePromises = [];

      // Include start and end frames explicitly to ensure better coverage
      if (numberOfFrames > 2) {
        // Always add start frame
        framePromises.push(
          extractFrame(segment.start, 0.5).catch((err) => {
            // Error extracting start frame
            return "";
          })
        );
        timesToPreload.push(segment.start);

        // For middle frames, distribute evenly
        const middleFrames = numberOfFrames - 2; // subtract start and end frames
        for (let i = 0; i < middleFrames; i++) {
          // Calculate position to evenly distribute frames between start and end
          const progress = (i + 1) / (middleFrames + 1); // normalized 0-1 position
          const timePosition = segment.start + progress * duration;

          timesToPreload.push(timePosition);
          framePromises.push(
            extractFrame(timePosition, 0.5).catch((err) => {
              // Error extracting frame
              return "";
            })
          );
        }

        // Always add end frame (slightly before actual end to ensure it's in range)
        const endPosition = Math.max(segment.start + 0.1, segment.end - 0.1);
        framePromises.push(
          extractFrame(endPosition, 0.5).catch((err) => {
            // Error extracting end frame
            return "";
          })
        );
        timesToPreload.push(endPosition);
      } else {
        // For very short segments, just use evenly spaced frames
        for (let i = 0; i < numberOfFrames; i++) {
          const timePosition = segment.start + i * interval;
          timesToPreload.push(timePosition);

          framePromises.push(
            extractFrame(timePosition, 0.5).catch((err) => {
              // Error extracting frame
              return "";
            })
          );
        }
      }

      // Wait for all frames to be extracted in parallel
      const results = await Promise.all(framePromises);

      // Add the results to our frame URLs array
      frameUrls.push(...results);

      // Preload high quality versions in background
      preloadFrames(timesToPreload);

      return frameUrls;
    },
    [sourceVideoUrl, extractFrame, preloadFrames]
  );

  const handlePointerDown = useCallback(
    (e: React.PointerEvent, handle: "left" | "right", index: number) => {
      e.preventDefault();
      e.stopPropagation();

      // Index is now guaranteed to be the original segment index,
      // so we don't need to extract it from dataset
      const segment = segmentsRef.current[index];
      if (!segment) return;

      setDragState({
        dragging: handle,
        segmentIndex: index,
        initialPosition: e.clientX,
        initialStart: segment.start,
        initialEnd: segment.end,
      });

      document.body.style.cursor = "ew-resize";
      document.body.style.userSelect = "none";
      document.body.style.webkitUserSelect = "none";
    },
    []
  );

  // Function to render segments imperatively with filmstrip
  const renderSegments = useCallback(
    async (
      segmentsToRender: IVideoSegment[],
      updateFilmstrip: boolean = true
    ) => {
      if (!containerRef.current || !timelineWidth) {
        // Cannot render segments: container or timelineWidth not available
        return;
      }

      // Filter out any segments marked as deleted - they should not appear on the timeline
      const visibleSegments = segmentsToRender.filter(
        (segment) => segment.deleted !== true
      );

      // Get current active segment index from imperative state
      const activeSegmentIndex = getCurrentSegmentIndex();

      // Rendering segments with width, segments, and activeSegment

      // Clear existing segments
      const container = containerRef.current;
      container.innerHTML = "";

      // Set the active index on the container element - ALWAYS use the imperative state
      container.setAttribute(
        "data-active-index",
        activeSegmentIndex?.toString() ?? "null"
      );

      // Track loading segments
      const newLoadingSegments = new Set<number>();

      // First pass: render all visible segments immediately with loading state if needed
      for (
        let visibleIndex = 0;
        visibleIndex < visibleSegments.length;
        visibleIndex++
      ) {
        const segment = visibleSegments[visibleIndex];

        // Find the original index in the full segments array (including deleted ones)
        const originalIndex = segmentsToRender.findIndex((s) => s === segment);

        // Existing code for rendering segments
        // Ensure we're using fresh calculations for left/right positions
        const left = secondsToPixels(segment.start);
        const right = secondsToPixels(segment.end);

        // Add a small gap by reducing the width slightly
        const gap = 1; // 1px gap
        const segmentWidth = Math.max(0, right - left - gap);
        const fullWidth = Math.max(0, right - left);

        // Always use imperative state for the active segment
        const isActive = visibleIndex === activeSegmentIndex;
        const isLoading = sourceVideoUrl && updateFilmstrip;

        if (isLoading) {
          newLoadingSegments.add(visibleIndex);
        }

        // Create segment element
        const segmentElement = document.createElement("div");
        // Apply base style first
        Object.assign(
          segmentElement.style,
          isLoading ? loadingSegmentStyle : segmentStyle
        );

        // Add an id for easier debugging
        segmentElement.id = `segment-${visibleIndex}`;

        // Update data attributes to help with debugging and for original segment mapping
        segmentElement.dataset.active = isActive ? "true" : "false";
        segmentElement.dataset.index = visibleIndex.toString();
        segmentElement.dataset.originalIndex = originalIndex.toString();

        // Set appearance based on status
        if (isActive) {
          // Active segment: bright blue with white border
          segmentElement.style.backgroundColor = "rgba(100, 180, 255, 0.8)";
          segmentElement.style.border = "2px solid white";
          segmentElement.style.boxShadow = "0 0 8px rgba(255, 255, 255, 0.5)";
          segmentElement.style.zIndex = "20"; // Highest priority
        } else {
          // Inactive segment: normal blue
          segmentElement.style.backgroundColor = "rgba(70, 130, 220, 0.7)";
          segmentElement.style.zIndex = "10"; // Normal priority
        }

        segmentElement.style.left = `${left}px`;
        segmentElement.style.width = `${segmentWidth}px`;
        // Lock the width to prevent any children from affecting it
        segmentElement.style.maxWidth = `${segmentWidth}px`;
        segmentElement.style.boxSizing = "border-box";

        // Add a clear text indicator for the segment number
        const segmentNumber = document.createElement("div");
        segmentNumber.textContent = `${visibleIndex + 1}`;
        segmentNumber.style.position = "absolute";
        segmentNumber.style.top = "2px";
        segmentNumber.style.left = "2px";
        segmentNumber.style.fontSize = "10px";
        segmentNumber.style.color = "white";
        segmentNumber.style.textShadow = "0 0 2px black";
        segmentElement.appendChild(segmentNumber);

        // Add click event with proper log - this selects the segment
        segmentElement.addEventListener("click", (e) => {
          e.stopPropagation();
          if (!dragState.dragging) {
            // Segment clicked, setting as selected
            handleSegmentClick(visibleIndex);
          }
        });

        // Create separate indicator element
        const indicatorElement = document.createElement("div");
        Object.assign(indicatorElement.style, breakIndicatorStyle);
        indicatorElement.style.left = `${left + 1}px`;
        indicatorElement.style.width = `${fullWidth - 2}px`;
        indicatorElement.style.top = "-8px";
        indicatorElement.style.borderRadius = "100px";

        // Color indicator based on status
        if (isActive) {
          indicatorElement.style.backgroundColor = "#FFFFFF";
        } else {
          indicatorElement.style.backgroundColor = "#73A0FF";
        }

        container.appendChild(indicatorElement);

        // Create left handle for resizing
        const leftHandle = document.createElement("div");
        Object.assign(leftHandle.style, handleStyle);
        // Only show handle if segment is selected
        if (isActive) {
          leftHandle.style.display = "block";
        }
        leftHandle.style.left = "0";
        leftHandle.setAttribute("data-handle", "left");

        leftHandle.addEventListener(
          "pointerdown",
          (e: PointerEvent) => {
            e.preventDefault();
            e.stopPropagation();

            // Get the original index from the segment element's dataset
            const originalIndex = parseInt(
              segmentElement.dataset.originalIndex || "0"
            );

            handlePointerDown(
              {
                preventDefault: () => e.preventDefault(),
                stopPropagation: () => e.stopPropagation(),
                clientX: e.clientX,
                target: e.target,
              } as any as React.PointerEvent,
              "left",
              originalIndex // Use original index instead of visible index
            );
          },
          { passive: false }
        );

        // Create right handle for resizing
        const rightHandle = document.createElement("div");
        Object.assign(rightHandle.style, handleStyle);
        // Only show handle if segment is selected
        if (isActive) {
          rightHandle.style.display = "block";
        }
        rightHandle.style.right = "0";
        rightHandle.setAttribute("data-handle", "right");
        rightHandle.addEventListener(
          "pointerdown",
          (e: PointerEvent) => {
            e.preventDefault();
            e.stopPropagation();

            // Get the original index from the segment element's dataset
            const originalIndex = parseInt(
              segmentElement.dataset.originalIndex || "0"
            );

            handlePointerDown(
              {
                preventDefault: () => e.preventDefault(),
                stopPropagation: () => e.stopPropagation(),
                clientX: e.clientX,
                target: e.target,
              } as any as React.PointerEvent,
              "right",
              originalIndex // Use original index instead of visible index
            );
          },
          { passive: false }
        );

        // Append handles to segment
        segmentElement.appendChild(leftHandle);
        segmentElement.appendChild(rightHandle);

        // Add filmstrip container as placeholder if needed
        if (sourceVideoUrl) {
          const filmstripContainer = document.createElement("div");
          Object.assign(filmstripContainer.style, filmstripStyle);
          filmstripContainer.dataset.filmstrip = visibleIndex.toString();

          // Set explicit styling to ensure visibility
          filmstripContainer.style.position = "absolute";
          filmstripContainer.style.display = "flex";
          filmstripContainer.style.width = "100%";
          filmstripContainer.style.height = "100%";
          filmstripContainer.style.top = "0";
          filmstripContainer.style.left = "0";
          filmstripContainer.style.zIndex = "5";
          filmstripContainer.style.overflow = "hidden";
          filmstripContainer.style.pointerEvents = "none";

          // Add class for easier styling
          filmstripContainer.classList.add("filmstrip-container");

          segmentElement.appendChild(filmstripContainer);

          // Add a temporary loading indicator to show something is happening
          const loadingIndicator = document.createElement("div");
          loadingIndicator.classList.add("filmstrip-loading-indicator");
          loadingIndicator.style.position = "absolute";
          loadingIndicator.style.top = "50%";
          loadingIndicator.style.left = "50%";
          loadingIndicator.style.transform = "translate(-50%, -50%)";
          loadingIndicator.style.width = "20px";
          loadingIndicator.style.height = "20px";
          loadingIndicator.style.borderRadius = "50%";
          loadingIndicator.style.border = "2px solid rgba(255,255,255,0.3)";
          loadingIndicator.style.borderTopColor = "#fff";
          loadingIndicator.style.animation =
            "filmstrip-loading-spin 1s linear infinite";
          filmstripContainer.appendChild(loadingIndicator);
        }

        // Append segment to container
        container.appendChild(segmentElement);
      }

      // Second pass: load all filmstrips concurrently if needed
      if (sourceVideoUrl && updateFilmstrip) {
        // Generate all filmstrips in parallel
        const filmstripPromises = visibleSegments.map((segment, index) => {
          // Recalculate dimensions here to ensure we're using the most current values
          const left = secondsToPixels(segment.start);
          const right = secondsToPixels(segment.end);
          const gap = 1;
          const segmentWidth = Math.max(0, right - left - gap);

          // Return a promise for each filmstrip generation
          return generateFilmstrip(segment, segmentWidth).then((frames) => {
            return { index, frames, segmentWidth };
          });
        });

        // Process filmstrips as they complete
        Promise.all(filmstripPromises).then((results) => {
          // For each completed filmstrip
          results.forEach(({ index, frames, segmentWidth }) => {
            if (!containerRef.current) return;

            // Find the segment by data-index
            const segmentElement = containerRef.current.querySelector(
              `[data-index="${index}"]`
            ) as HTMLDivElement;
            if (!segmentElement) {
              // Filmstrip container for segment not found
              return;
            }

            // Find the filmstrip container
            const filmstripContainer = segmentElement.querySelector(
              `[data-filmstrip="${index}"]`
            ) as HTMLDivElement;
            if (!filmstripContainer) {
              // Filmstrip container for segment not found
              return;
            }

            // Critical fix: Make sure segment is not in loading state and fully visible
            // Apply style in a way that preserves critical properties
            segmentElement.style.backgroundColor = "rgba(70, 130, 180, 0.6)";
            segmentElement.style.animation = "none";
            segmentElement.style.overflow = "hidden";

            // Remove any loading background pattern
            segmentElement.style.backgroundImage = "none";

            // Update Z-index to ensure it's displayed above timeline but below handles
            segmentElement.style.zIndex = "15";

            // Add debugging class
            segmentElement.classList.add("filmstrip-loaded");

            // Clear filmstrip container but preserve its position
            filmstripContainer.innerHTML = "";

            // Ensure filmstrip container has correct styling
            filmstripContainer.style.position = "absolute";
            filmstripContainer.style.top = "0";
            filmstripContainer.style.left = "0";
            filmstripContainer.style.width = "100%";
            filmstripContainer.style.height = "100%";
            filmstripContainer.style.display = "flex";
            filmstripContainer.style.zIndex = "5";
            filmstripContainer.style.overflow = "hidden";

            // Add debugging info
            filmstripContainer.setAttribute(
              "data-frames-count",
              frames.length.toString()
            );
            filmstripContainer.classList.add("filmstrip-container-updated");

            // Configure filmstrip container for better frame distribution
            if (segmentWidth > frames.length * 100) {
              // For segments wider than our available frames, use space-between
              filmstripContainer.style.display = "flex";
              filmstripContainer.style.justifyContent = "space-between";
            } else {
              // For segments that are narrower, use flex-start to avoid stretching
              filmstripContainer.style.display = "flex";
              filmstripContainer.style.justifyContent = "flex-start";
            }

            // Add frames to filmstrip one by one, ensuring each is visible
            frames.forEach((frameUrl, frameIndex) => {
              if (!frameUrl) return;

              const frameElement = document.createElement("div");

              // Apply styles explicitly rather than using Object.assign
              frameElement.style.height = "100%";
              frameElement.style.width = "100px";
              frameElement.style.minWidth = "100px";
              frameElement.style.flexShrink = "0";
              frameElement.style.backgroundSize = "contain";
              frameElement.style.backgroundPosition = "center";
              frameElement.style.backgroundRepeat = "no-repeat";
              frameElement.style.backgroundImage = `url(${frameUrl})`;
              frameElement.style.opacity = "1";
              frameElement.style.visibility = "visible";
              frameElement.style.position = "relative";
              frameElement.style.zIndex = "10";

              // Add debug attributes
              frameElement.setAttribute(
                "data-frame-index",
                frameIndex.toString()
              );
              frameElement.setAttribute("data-segment-index", index.toString());
              frameElement.classList.add("filmstrip-frame");

              filmstripContainer.appendChild(frameElement);

              // Force a reflow to ensure the browser updates the display
              void frameElement.offsetHeight;
            });

            // Add a sentinel element to help with debugging
            const sentinelElement = document.createElement("div");
            sentinelElement.classList.add("filmstrip-sentinel");
            sentinelElement.style.position = "absolute";
            sentinelElement.style.right = "0";
            sentinelElement.style.top = "0";
            sentinelElement.style.width = "5px";
            sentinelElement.style.height = "5px";
            sentinelElement.style.backgroundColor = "lime";
            sentinelElement.style.zIndex = "100";
            filmstripContainer.appendChild(sentinelElement);
          });

          // After all filmstrips are processed, force a refresh of the layout
          if (containerRef.current) {
            // Add a class to indicate completed loading
            containerRef.current.classList.add("filmstrips-loaded");

            // Force a reflow
            void containerRef.current.offsetHeight;
          }
        });
      }

      // After rendering everything, call refreshActiveSegment to ensure correct styling and handles
      setTimeout(() => refreshActiveSegment(), 0);
    },
    [
      timelineWidth,
      secondsToPixels,
      handlePointerDown,
      generateFilmstrip,
      sourceVideoUrl,
      handleSegmentClick,
      dragState,
      refreshActiveSegment,
    ]
  );

  const handlePointerMove = useCallback(
    (e: PointerEvent) => {
      if (!dragState.dragging || !timelineWidth) return;

      e.preventDefault();
      e.stopPropagation();

      const fps = getCurrentFps();
      const durationInFrames = getCurrentDuration();
      const durationInSeconds = durationInFrames / fps;

      // Calculate how many seconds per pixel
      const pixelToSecondRatio =
        durationInSeconds / (timelineWidth - TIMELINE_PADDING * 2);

      // Set gap to 0 to allow segments to be placed directly next to each other
      const gapInSeconds = 0;

      // Calculate the delta in seconds
      const deltaPixels = e.clientX - (dragState as any).initialPosition;
      const deltaSeconds = deltaPixels * pixelToSecondRatio;

      // Create a copy of the segments
      const newSegments = [...segmentsRef.current];
      const segmentIndex = (dragState as any).segmentIndex;
      const segment = newSegments[segmentIndex];

      if (dragState.dragging === "left") {
        // Find the previous non-deleted segment
        let prevSegment = null;
        for (let i = segmentIndex - 1; i >= 0; i--) {
          if (newSegments[i] && newSegments[i].deleted !== true) {
            prevSegment = newSegments[i];
            break;
          }
        }

        // Calculate min allowed start based on clip or video mode
        let minAllowedStart;

        if (prevSegment) {
          minAllowedStart = prevSegment.end + gapInSeconds;
        } else {
          // For first segment in clip mode, minimum is 0 + offset
          minAllowedStart = mappingConfig.clipToOriginalTime(0);
        }

        // Calculate new start time with constraints
        const newStart = Math.max(
          minAllowedStart,
          (dragState as any).initialStart + deltaSeconds
        );

        // Ensure start doesn't go beyond end - minimum duration
        const clampedStart = Math.min(
          newStart,
          segment.end - MIN_SEGMENT_DURATION
        );

        // Create a new segment object instead of modifying directly
        newSegments[segmentIndex] = {
          ...segment,
          start: clampedStart,
        };
      } else {
        // Find the next non-deleted segment
        let nextSegment = null;
        for (let i = segmentIndex + 1; i < newSegments.length; i++) {
          if (newSegments[i] && newSegments[i].deleted !== true) {
            nextSegment = newSegments[i];
            break;
          }
        }

        // Calculate max allowed end based on clip or video mode
        let maxAllowedEnd;
        if (nextSegment) {
          maxAllowedEnd = nextSegment.start - gapInSeconds;
        } else {
          // For last segment in clip mode, maximum is clip duration + offset
          const clipDuration = durationInSeconds;
          maxAllowedEnd = mappingConfig.clipToOriginalTime(clipDuration);
        }

        // Calculate new end time with constraints
        const newEnd = Math.min(
          maxAllowedEnd,
          (dragState as any).initialEnd + deltaSeconds
        );

        // Ensure end doesn't go below start + minimum duration
        const clampedEnd = Math.max(
          newEnd,
          segment.start + MIN_SEGMENT_DURATION
        );

        // Create a new segment object instead of modifying directly
        newSegments[segmentIndex] = {
          ...segment,
          end: clampedEnd,
        };
      }

      // Update the segments ref
      segmentsRef.current = newSegments;

      // Redraw segments with real-time updates
      // Use false for updateFilmstrip to avoid performance issues during dragging
      renderSegments(newSegments, false);
      onSegmentsChange(newSegments);
    },
    [dragState, timelineWidth, mappingConfig, renderSegments]
  );

  // Update filmstrips when zoom changes or segments change
  useEffect(() => {
    if (sourceVideoUrl) {
      setShouldUpdateFilmstrip(true);
    }
  }, [zoom, segments, sourceVideoUrl]);

  const handlePointerUp = useCallback(() => {
    if (!dragState.dragging) return;

    // Store current state of segments before changing drag state
    const currentSegments = [...segmentsRef.current];
    const draggedSegmentIndex = (dragState as any).segmentIndex;

    // Reset dragging state
    setDragState({ dragging: false });
    document.body.style.cursor = "default";
    document.body.style.userSelect = "";
    document.body.style.webkitUserSelect = "";

    // Clear the container directly to ensure a fresh redraw
    if (containerRef.current) {
      containerRef.current.innerHTML = "";
    }

    // Use setTimeout to ensure this runs after other state updates
    setTimeout(() => {
      // Redrawing with updated segments

      // Always generate filmstrips after resize operations
      // This is critical to ensure they reappear properly
      renderSegments(currentSegments, true);

      // Signal full filmstrip update is needed with higher priority
      // This ensures frames are regenerated even if another rendering cycle is in progress
      setTimeout(() => {
        setShouldUpdateFilmstrip(true);
      }, 10);
    }, 0);
  }, [dragState, renderSegments, containerRef]);

  // Attach global pointer move and up handlers
  useEffect(() => {
    if (dragState.dragging) {
      window.addEventListener("pointermove", handlePointerMove, {
        passive: false,
      });
      window.addEventListener("pointerup", handlePointerUp, { passive: false });

      return () => {
        window.removeEventListener("pointermove", handlePointerMove);
        window.removeEventListener("pointerup", handlePointerUp);
      };
    }
  }, [dragState, handlePointerMove, handlePointerUp]);

  // Handle filmstrip updates - completely redesigned approach
  useEffect(() => {
    if (shouldUpdateFilmstrip && sourceVideoUrl && containerRef.current) {
      // Updating filmstrips due to shouldUpdateFilmstrip change

      // First, mark current filmstrip containers with a "stale" class
      const existingContainers =
        containerRef.current.querySelectorAll("[data-filmstrip]");
      existingContainers.forEach((container) => {
        container.classList.add("stale-filmstrip");
      });

      // Then render fresh segments with filmstrips
      renderSegments(segmentsRef.current, true);

      // Add styles to ensure filmstrips are visible
      const styleElement = document.createElement("style");
      styleElement.textContent = `
        .filmstrip-loaded {
          overflow: hidden !important;
          z-index: 15 !important;
        }
        .filmstrip-container-updated {
          display: flex !important;
          visibility: visible !important;
          opacity: 1 !important;
          z-index: 5 !important;
        }
        .filmstrip-frame {
          visibility: visible !important;
          opacity: 1 !important;
          z-index: 10 !important;
        }
      `;
      document.head.appendChild(styleElement);

      // Reset the update flag after ensuring rendering completes
      setTimeout(() => {
        setShouldUpdateFilmstrip(false);

        // Check if filmstrips are visible
        const frames =
          containerRef.current?.querySelectorAll(".filmstrip-frame");
        // Found filmstrip frames after update

        // Clean up the style element after sufficient time
        setTimeout(() => {
          styleElement.remove();
        }, 1000);
      }, 100);
    }
  }, [
    shouldUpdateFilmstrip,
    renderSegments,
    sourceVideoUrl,
    containerRef.current,
  ]);

  // Update when zoom or segments or active/selected segment index changes
  useEffect(() => {
    renderSegments(segments, sourceVideoUrl !== undefined);

    // Preload frames for visible segments when idle
    if (sourceVideoUrl && segments.length > 0) {
      const preloadAllSegments = () => {
        const timesToPreload: number[] = [];

        segments.forEach((segment) => {
          const duration = segment.end - segment.start;
          const framesNeeded = Math.ceil(
            (secondsToPixels(segment.end) - secondsToPixels(segment.start)) /
              100
          );
          const interval = duration / Math.max(1, framesNeeded);

          for (let i = 0; i < framesNeeded; i++) {
            timesToPreload.push(segment.start + i * interval);
          }
        });

        // If the active segment exists, prioritize its frames by adding them first
        if (
          getActiveSegmentIndex() !== undefined &&
          segments[getActiveSegmentIndex() ?? 0]
        ) {
          const activeSegment = segments[getActiveSegmentIndex() ?? 0];
          const activeDuration = activeSegment.end - activeSegment.start;
          const activeFramesNeeded = Math.ceil(
            (secondsToPixels(activeSegment.end) -
              secondsToPixels(activeSegment.start)) /
              100
          );
          const activeInterval =
            activeDuration / Math.max(1, activeFramesNeeded);

          const activeTimesToPreload = [];
          for (let i = 0; i < activeFramesNeeded; i++) {
            activeTimesToPreload.push(activeSegment.start + i * activeInterval);
          }

          // Preload active segment frames first
          preloadFrames(activeTimesToPreload);
        }

        // Preload all other frames
        preloadFrames(timesToPreload);
      };

      // Use requestIdleCallback if available, otherwise use setTimeout
      if ("requestIdleCallback" in window) {
        (window as any).requestIdleCallback(preloadAllSegments, {
          timeout: 1000,
        });
      } else {
        setTimeout(preloadAllSegments, 500);
      }
    }
  }, [
    renderSegments,
    segments,
    zoom,
    sourceVideoUrl,
    getActiveSegmentIndex,
    secondsToPixels,
    preloadFrames,
  ]);

  // Limit frame cache size (keep only last 100 frames)
  useEffect(() => {
    if (frameCache.size > 100) {
      const keysToDelete = Array.from(frameCache.keys()).slice(
        0,
        frameCache.size - 100
      );
      keysToDelete.forEach((key) => frameCache.delete(key));
    }
  }, [shouldUpdateFilmstrip]);

  // Add keyframes for loading animation
  useEffect(() => {
    if (!document.getElementById("segment-loading-keyframes")) {
      const style = document.createElement("style");
      style.id = "segment-loading-keyframes";
      style.innerHTML = `
        @keyframes segment-loading {
          0% { background-position: 0 0; }
          100% { background-position: 40px 0; }
        }
        
        @keyframes filmstrip-loading-spin {
          0% { transform: translate(-50%, -50%) rotate(0deg); }
          100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .filmstrip-loaded {
          overflow: hidden !important;
        }
        
        .filmstrip-container {
          visibility: visible !important;
          opacity: 1 !important;
        }
        
        .filmstrip-frame {
          visibility: visible !important;
          opacity: 1 !important;
        }
      `;
      document.head.appendChild(style);

      return () => {
        const element = document.getElementById("segment-loading-keyframes");
        if (element) element.remove();
      };
    }
  }, []);

  useEffect(() => {
    // TimelineSegments rendered with activeSegmentIndex
  }, [getActiveSegmentIndex]);

  // Also add a useEffect to log when container or timelineWidth changes
  useEffect(() => {
    // timeline container or width changed
  }, [containerRef.current, timelineWidth]);

  // Expose imperative handle for updating active segment
  useImperativeHandle(
    timelineSegmentsRef,
    () => {
      return {
        setActiveSegmentIndex: (index: number | null) => {
          // Update imperative state to keep everything in sync
          setCurrentSegmentIndex(index);

          // Immediately refresh the segments to avoid any visual lag
          setTimeout(() => refreshActiveSegment(), 0);
        },
      };
    },
    [refreshActiveSegment]
  );

  // Create a style element for active segments CSS
  useEffect(() => {
    // Add CSS rules for active segments if not already added
    if (!document.getElementById("timeline-segments-styles")) {
      const styleElement = document.createElement("style");
      styleElement.id = "timeline-segments-styles";
      styleElement.innerHTML = `
        /* Enhanced styling for active segments - controlled by data-active attribute */
        [data-active="true"] {
          z-index: 15 !important;
          box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.3) !important;
        }
        
        [data-active="true"][data-selected="true"] {
          background-color: rgba(120, 190, 255, 0.9) !important;
          border: 2px solid white !important;
          box-shadow: 0 0 8px rgba(255, 255, 255, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.3) !important;
          z-index: 20 !important;
        }
        
        /* Indicators for active segments */
        [data-active-index] ~ div[style*="position: absolute"][style*="height: 2px"] {
          background-color: #73A0FF !important;
        }
        
        [data-active-index] > [data-active="true"] ~ div[style*="position: absolute"][style*="height: 2px"] {
          background-color: #FFFFFF !important;
          height: 3px !important; 
        }
      `;
      document.head.appendChild(styleElement);

      return () => {
        const element = document.getElementById("timeline-segments-styles");
        if (element) element.remove();
      };
    }
  }, []);

  // Add back the effect to maintain sync
  useEffect(() => {
    // Initial refresh
    refreshActiveSegment();

    // Set up an interval to refresh the active segment display
    const intervalId = setInterval(() => {
      refreshActiveSegment();
    }, 300); // Refresh every 300ms to ensure UI stays in sync

    return () => {
      clearInterval(intervalId);
    };
  }, [refreshActiveSegment]);

  return (
    <div
      ref={containerRef}
      style={containerStyle}
      data-active-index={getActiveSegmentIndex()}
      data-test-id="timeline-segments"
    />
  );
};
