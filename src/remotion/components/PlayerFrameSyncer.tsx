import {
  currentWordRef,
  playerRef,
  timelineSegmentsRef,
} from "./timeline-refs";
import {
  clearCurrentSubtitle,
  getCurrentDuration,
  getCurrentFps,
  getCurrentSegmentIndex,
  setCurrentFrame,
  setCurrentSegmentIndex,
  setCurrentSubtitleIndex,
} from "../imperative-state";
import { redrawTimelineSliderFast } from "../TimelineSlider";
import { useCallback, useEffect, useRef } from "react";
import { ensureFrameIsInViewport } from "./timeline-scroll-logic";
import { timeDisplayUpdateRef } from "./DisplayTime";
import {
  useClipSegments,
  useClipSubtitles,
} from "../../features/clipConfig/ClipConfigApi";
import { calibrateSubtitles } from "../../utils";

interface FrameUpdateEvent {
  detail: {
    frame: number;
  };
}

export const PlayerFrameSyncer: React.FC = () => {
  const segments = useClipSegments();
  const subtitles = useClipSubtitles();
  const lastFrameCheckedRef = useRef<number>(-1);
  const calibratedSubtitlesRef = useRef<any[]>([]);

  // Calculate and store calibrated subtitles when segments or subtitles change
  useEffect(() => {
    if (subtitles?.items && segments.length > 0) {
      const calibrated = calibrateSubtitles(subtitles.items, segments);
      calibratedSubtitlesRef.current = calibrated;
    } else {
      calibratedSubtitlesRef.current = [];
    }
  }, [subtitles?.items, segments]);

  const handleFrameUpdate = useCallback(
    (e: FrameUpdateEvent) => {
      const frame: number = e.detail.frame;
      const fps = getCurrentFps();
      const currentSegmentIndex = getCurrentSegmentIndex();

      // Update imperative state without re-renders
      setCurrentFrame(frame);

      const currentFrameInSeconds = frame / fps;

      // Get only non-deleted segments
      const activeSegments = segments.filter((segment) => !segment.deleted);

      // Find which segment we're currently in
      const currSegIndex = activeSegments.findIndex((segment) => {
        const relativeStart = segment.start - segments[0].start;
        const relativeEnd = segment.end - segments[0].start;

        return (
          currentFrameInSeconds >= relativeStart &&
          currentFrameInSeconds < relativeEnd
        );
      });

      if (frame === 0) {
        setCurrentSegmentIndex(0);
      } else if (currSegIndex === -1) {
        setCurrentSegmentIndex(null);

        // Check if we're past the last segment's end
        if (activeSegments.length > 0 && playerRef.current?.isPlaying()) {
          const lastSegment = activeSegments[activeSegments.length - 1];
          const lastSegmentEndInSeconds = lastSegment.end - segments[0].start;
          const lastSegmentEndFrame = Math.floor(lastSegmentEndInSeconds * fps);

          // If we're at or past the last segment's end frame, pause the player
          // Also check if this isn't a duplicate check of the same frame
          if (
            frame >= lastSegmentEndFrame &&
            frame !== lastFrameCheckedRef.current
          ) {
            lastFrameCheckedRef.current = frame;
            // Stopping playback at end of last segment
            playerRef.current.pause();

            // Seek to the end of the last segment to prevent looping
            playerRef.current.seekTo(lastSegmentEndFrame);
          }
        }
      } else if (currSegIndex !== currentSegmentIndex) {
        // Only update if segment changed and we found a valid segment
        // Frame sync: Segment changed

        // Update the imperative segment state
        setCurrentSegmentIndex(currSegIndex);

        // Update the timeline segments display imperatively
        if (timelineSegmentsRef.current) {
          // Updating timeline segments via imperative ref
          timelineSegmentsRef.current.setActiveSegmentIndex(currSegIndex);
        } else {
          // timelineSegmentsRef.current is null, cannot update active segment
        }
      }

      // Find the current subtitle based on the current frame time using calibrated subtitles
      const calibratedSubtitles = calibratedSubtitlesRef.current;
      if (calibratedSubtitles && calibratedSubtitles.length > 0) {
        // Calculate the current time in seconds
        const currentTimeInSeconds = currentFrameInSeconds;

        // Find the current subtitle that contains the current time
        let foundCurrentSubtitle = false;

        for (
          let subtitleIndex = 0;
          subtitleIndex < calibratedSubtitles.length;
          subtitleIndex++
        ) {
          const subtitle = calibratedSubtitles[subtitleIndex];
          if (!subtitle) continue;

          // Check if this subtitle contains the current time
          if (
            currentTimeInSeconds >= subtitle.start &&
            currentTimeInSeconds < subtitle.end
          ) {
            // Found the current subtitle - update the imperative state
            setCurrentSubtitleIndex(subtitleIndex);

            // Update the currentWordRef if available
            if (currentWordRef.current) {
              currentWordRef.current.setCurrentSubtitleIndex(subtitleIndex);
            }

            foundCurrentSubtitle = true;
            break;
          }
        }

        // If no current subtitle found and we were previously tracking one, clear it
        if (!foundCurrentSubtitle) {
          clearCurrentSubtitle();
          if (currentWordRef.current) {
            currentWordRef.current.clearCurrentSubtitle();
          }
        }
      }

      // Update playhead position directly for smooth movement
      redrawTimelineSliderFast.current?.draw(frame);

      // Update time display
      timeDisplayUpdateRef.current?.updateTime(frame);

      // Handle auto-scrolling during playback
      if (playerRef?.current?.isPlaying()) {
        ensureFrameIsInViewport({
          direction: "page-right",
          durationInFrames: getCurrentDuration(),
          frame,
        });
      }
    },
    [segments]
  );

  // Effect to sync player's current frame to imperative state
  useEffect(() => {
    if (playerRef.current) {
      // Get initial frame and segment on mount
      const initialFrame = playerRef.current.getCurrentFrame();

      playerRef.current.addEventListener("frameupdate", handleFrameUpdate);

      // Cleanup on unmount
      return () => {
        playerRef.current?.removeEventListener(
          "frameupdate",
          handleFrameUpdate
        );
      };
    }
  }, [handleFrameUpdate]);

  return null;
};
