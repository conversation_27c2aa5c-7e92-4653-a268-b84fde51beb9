import React, { useContext, useEffect, useMemo, useRef } from "react";
import { TimeValue } from "../TimeValue";
import { timelineVerticalScroll } from "./timeline-refs";
import { getFrameIncrementFromWidth } from "./timeline-scroll-logic";
import {
  SPLITTER_HANDLE_SIZE,
  TIMELINE_ITEM_BORDER_BOTTOM,
  TIMELINE_PADDING,
  TIMELINE_TRACK_SEPARATOR,
} from "../helpers";
import { TimelineWidthContext } from "../context/TimelineWidthProvider";
import { renderFrame } from "../time-utils";
import { createStyles } from "@mantine/core";
import { getCurrentDuration, getCurrentFps } from "../imperative-state";

export const TIMELINE_TIME_INDICATOR_HEIGHT = 39;

const useStyles = createStyles((theme) => ({
  container: {
    height: TIMELINE_TIME_INDICATOR_HEIGHT - 4,
    position: "absolute",
    backgroundColor: "transparent",
    top: 0,
    overflow: "hidden",
    // Since
    marginLeft: SPLITTER_HANDLE_SIZE / 2,
    pointerEvents: "none",
  },

  tick: {
    width: 1,
    backgroundColor: theme.other["surface-subtext-color"],
    height: 20,
    position: "absolute",
    bottom: 0,
  },

  secondTick: {
    width: 1,
    backgroundColor: theme.other["surface-subtext-color"],
    height: 15,
    position: "absolute",
    bottom: 0,
  },

  tickLabel: {
    position: "absolute",
    top: 0,
    transform: "translateX(-50%)",
    color: theme.other["surface-subtext-color"],
    whiteSpace: "nowrap",
  },

  timeValue: {
    height: TIMELINE_TIME_INDICATOR_HEIGHT,
    position: "absolute",
    top: 0,
    width: "100%",
    paddingLeft: 10,
    display: "flex",
    alignItems: "center",
    backgroundColor: "transparent",
    borderBottom: `${TIMELINE_ITEM_BORDER_BOTTOM}px solid ${TIMELINE_TRACK_SEPARATOR}`,
  },

  timePadding: {
    height: TIMELINE_TIME_INDICATOR_HEIGHT,
  },
}));

export const TimelineTimePlaceholders: React.FC = () => {
  const { classes } = useStyles();
  return (
    <div className={classes.timeValue}>
      <TimeValue />
    </div>
  );
};

export const TimelineTimePadding: React.FC = () => {
  const { classes } = useStyles();
  return <div className={classes.timePadding} />;
};

type TimelineTick = {
  frame: number;
  style: {
    left: number;
    height?: number;
  };
  showTime: boolean;
};

interface TimelineTimeIndicatorsProps {}

export const TimelineTimeIndicators: React.FC<
  TimelineTimeIndicatorsProps
> = ({}) => {
  const sliderTrack = useContext(TimelineWidthContext);

  // Use originalDuration if provided, otherwise use current duration
  const duration = getCurrentDuration();
  const fps = getCurrentFps();

  if (sliderTrack === null) {
    return null;
  }

  return (
    <Inner durationInFrames={duration} fps={fps} windowWidth={sliderTrack} />
  );
};

const Inner: React.FC<{
  readonly windowWidth: number;
  readonly fps: number;
  readonly durationInFrames: number;
}> = ({ windowWidth, durationInFrames, fps }) => {
  const { classes, cx } = useStyles();
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) {
      return;
    }

    const { current } = timelineVerticalScroll;
    if (!current) {
      return;
    }

    const onScroll = () => {
      currentRef.style.top = current.scrollTop + "px";
    };

    current.addEventListener("scroll", onScroll);
    return () => {
      current.removeEventListener("scroll", onScroll);
    };
  }, []);

  const containerStyle = useMemo(() => {
    return {
      width: windowWidth - SPLITTER_HANDLE_SIZE / 2,
    };
  }, [windowWidth]);

  const ticks: TimelineTick[] = useMemo(() => {
    const frameInterval = getFrameIncrementFromWidth(
      durationInFrames,
      windowWidth
    );

    const MIN_SPACING_BETWEEN_TICKS_PX = 5;

    const seconds = Math.floor(durationInFrames / fps);
    const secondMarkerEveryNth = Math.ceil(
      (MIN_SPACING_BETWEEN_TICKS_PX * fps) / (frameInterval * fps)
    );
    const frameMarkerEveryNth = Math.ceil(
      MIN_SPACING_BETWEEN_TICKS_PX / frameInterval
    );

    // Big ticks showing for every second
    const secondTicks: TimelineTick[] = new Array(seconds)
      .fill(true)
      .map((_, index) => {
        return {
          frame: index * fps,
          style: {
            left:
              frameInterval * index * fps +
              TIMELINE_PADDING -
              SPLITTER_HANDLE_SIZE / 2,
          },
          showTime: true,
        };
      })
      .filter((_, idx) => idx % secondMarkerEveryNth === 0);

    const frameTicks: TimelineTick[] = new Array(durationInFrames)
      .fill(true)
      .map((_, index) => {
        return {
          frame: index,
          style: {
            left:
              frameInterval * index +
              TIMELINE_PADDING -
              SPLITTER_HANDLE_SIZE / 2,
            height:
              index % fps === 0
                ? 10
                : (index / frameMarkerEveryNth) % 2 === 0
                ? 5
                : 2,
          },
          showTime: false,
        };
      })
      .filter((_, idx) => idx % frameMarkerEveryNth === 0);

    // Merge and deduplicate ticks
    const hasTicks: number[] = [];
    return [...secondTicks, ...frameTicks].filter((t) => {
      const alreadyUsed = hasTicks.find((ht) => ht === t.frame) !== undefined;
      hasTicks.push(t.frame);
      return !alreadyUsed;
    });
  }, [durationInFrames, fps, windowWidth]);

  return (
    <div ref={ref} className={classes.container} style={containerStyle}>
      {ticks.map((t) => {
        return (
          <React.Fragment key={t.frame}>
            {/* Time label positioned above the markers */}
            {t.showTime ? (
              <div
                className={cx(classes.tickLabel, "small-p-regular")}
                style={{ left: t.style.left }}
              >
                {t.frame === 0 ? "0" : renderFrame(t.frame, fps)}
              </div>
            ) : null}

            <div
              className={t.showTime ? classes.secondTick : classes.tick}
              style={{
                left: t.style.left,
                height: t.style.height,
              }}
            />
          </React.Fragment>
        );
      })}
    </div>
  );
};
