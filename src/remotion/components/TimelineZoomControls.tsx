import React, { useCallback, useContext } from "react";
import { Internals } from "remotion";
import {
  TIMELINE_MAX_ZOOM,
  TIMELINE_MIN_ZOOM,
  TimelineZoomCtx,
} from "../context/timeline-zoom";
import { VFButton } from "../../components/ui/Button";
import { Box } from "@mantine/core";
import VFSlider from "../../components/ui/Slider";

// import { useZIndex } from "../../state/z-index"; //! TODO: implement this ?? if needed

const container: React.CSSProperties = {
  color: "black",
  flexDirection: "row",
  display: "flex",
  alignItems: "center",
};

export const TimelineZoomControls: React.FC = () => {
  const { setZoom, zoom } = useContext(TimelineZoomCtx);
  //   const { tabIndex } = useZIndex();

  const onMinusClicked = useCallback(() => {
    setZoom((z) => Math.max(TIMELINE_MIN_ZOOM, z - 0.2));
  }, [setZoom]);

  const onPlusClicked = useCallback(() => {
    setZoom((z) => Math.min(TIMELINE_MAX_ZOOM, z + 0.2));
  }, [setZoom]);

  const onChange = useCallback(
    (value: number) => {
      setZoom(() => value);
    },
    [setZoom]
  );

  //   const isStill = useIsStill();

  return (
    <div style={container}>
      <VFButton
        variant="secondary"
        buttonDisplay="onlyIcon"
        iconName="minus"
        onClick={onMinusClicked}
        disabled={TIMELINE_MIN_ZOOM === zoom}
      />
      <Box
        sx={{
          width: 120,
          display: "flex",
          alignItems: "center",
        }}
      >
        <VFSlider
          variant="default"
          value={zoom}
          min={TIMELINE_MIN_ZOOM}
          max={TIMELINE_MAX_ZOOM}
          step={0.1}
          onChange={onChange}
          fullWidth
        />
      </Box>
      <VFButton
        variant="secondary"
        buttonDisplay="onlyIcon"
        iconName="plus"
        onClick={onPlusClicked}
        disabled={TIMELINE_MAX_ZOOM === zoom}
      />
    </div>
  );
};
