import { PlayerRef } from "@remotion/player";
import React from "react";

export const sliderAreaRef = React.createRef<HTMLDivElement>();
export const playerRef = React.createRef<PlayerRef>();
export const scrollableRef = React.createRef<HTMLDivElement>();
export const timelineVerticalScroll = React.createRef<HTMLDivElement>();
export const inMarkerAreaRef = React.createRef<HTMLDivElement>();
export const outMarkerAreaRef = React.createRef<HTMLDivElement>();
export const inPointerHandle = React.createRef<HTMLDivElement>();
export const outPointerHandle = React.createRef<HTMLDivElement>();
export const timelineSegmentsRef = React.createRef<{
  setActiveSegmentIndex: (index: number) => void;
}>();
export const currentWordRef = React.createRef<{
  setCurrentSubtitleIndex: (subtitleIndex: number) => void;
  clearCurrentSubtitle: () => void;
}>();
