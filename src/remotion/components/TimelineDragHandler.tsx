import { PlayerInternals } from "@remotion/player";
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { playerRef, scrollableRef, sliderAreaRef } from "./timeline-refs";
import {
  canScrollTimelineIntoDirection,
  getFrameFromX,
  getFrameWhileScrollingLeft,
  getFrameWhileScrollingRight,
  getScrollPositionForCursorOnLeftEdge,
  getScrollPositionForCursorOnRightEdge,
  scrollToTimelineXOffset,
} from "./timeline-scroll-logic";
import { TimelineZoomCtx } from "../context/timeline-zoom";
import { TimelineWidthContext } from "../context/TimelineWidthProvider";
import { getXPositionOfItemInTimelineImperatively } from "../get-left-of-timeline-slider";
import { TIMELINE_PADDING } from "../helpers";
import { redrawTimelineSliderFast } from "../TimelineSlider";
import { getCurrentDuration, setCurrentFrame } from "../imperative-state";

const inner: React.CSSProperties = {
  overflowY: "auto",
  overflowX: "hidden",
};

const container: React.CSSProperties = {
  userSelect: "none",
  WebkitUserSelect: "none",
  position: "absolute",
  height: "100%",
  top: 0,
};

const getClientXWithScroll = (x: number) => {
  return x + (scrollableRef.current?.scrollLeft as number);
};

export const TimelineDragHandler: React.FC = () => {
  const size = PlayerInternals.useElementSize(scrollableRef, {
    triggerOnWindowResize: true,
    shouldApplyCssTransforms: true,
  });

  const width = scrollableRef.current?.scrollWidth ?? 0;
  const left = size?.left ?? 0;

  const { zoom } = useContext(TimelineZoomCtx);

  const scroller = useRef<ReturnType<typeof setInterval> | null>(null);

  const stopInterval = () => {
    if (scroller.current) {
      clearInterval(scroller.current);
      scroller.current = null;
    }
  };

  const containerStyle: React.CSSProperties = useMemo(() => {
    return {
      ...container,
      width: 100 * zoom + "%",
    };
  }, [zoom]);

  const [dragging, setDragging] = useState<
    | {
        dragging: false;
      }
    | {
        dragging: true;
        wasPlaying: boolean;
      }
  >({
    dragging: false,
  });

  const onPointerMoveScrubbing = useCallback(
    (e: PointerEvent) => {
      if (!dragging.dragging) {
        return;
      }

      const isRightOfArea =
        e.clientX >=
        (scrollableRef.current?.clientWidth as number) +
          left -
          TIMELINE_PADDING;

      const isLeftOfArea = e.clientX <= left;

      const frame = getFrameFromX({
        clientX: getClientXWithScroll(e.clientX) - left,
        durationInFrames: getCurrentDuration(),
        width,
        extrapolate: "clamp",
      });

      if (isLeftOfArea && canScrollTimelineIntoDirection().canScrollLeft) {
        if (scroller.current) {
          return;
        }

        const scrollEvery = () => {
          if (!canScrollTimelineIntoDirection().canScrollLeft) {
            stopInterval();
            return;
          }

          const nextFrame = getFrameWhileScrollingLeft({
            durationInFrames: getCurrentDuration(),
            width,
          });

          const scrollPos = getScrollPositionForCursorOnLeftEdge({
            nextFrame,
            durationInFrames: getCurrentDuration(),
          });

          redrawTimelineSliderFast.current?.draw(nextFrame);
          playerRef.current?.seekTo(nextFrame);
          scrollToTimelineXOffset(scrollPos);
        };

        scrollEvery();
        scroller.current = setInterval(() => {
          scrollEvery();
        }, 100);
      } else if (
        isRightOfArea &&
        canScrollTimelineIntoDirection().canScrollRight
      ) {
        if (scroller.current) {
          return;
        }

        const scrollEvery = () => {
          if (!canScrollTimelineIntoDirection().canScrollRight) {
            stopInterval();
            return;
          }

          const nextFrame = getFrameWhileScrollingRight({
            durationInFrames: getCurrentDuration(),
            width,
          });

          const scrollPos = getScrollPositionForCursorOnRightEdge({
            nextFrame,
            durationInFrames: getCurrentDuration(),
          });

          redrawTimelineSliderFast.current?.draw(nextFrame);
          playerRef.current?.seekTo(nextFrame);
          scrollToTimelineXOffset(scrollPos);
        };

        scrollEvery();

        scroller.current = setInterval(() => {
          scrollEvery();
        }, 100);
      } else {
        stopInterval();
        playerRef.current?.seekTo(frame);
      }
    },
    [dragging.dragging, left, width]
  );

  const onPointerUpScrubbing = useCallback(
    (e: PointerEvent) => {
      stopInterval();

      if (!dragging.dragging) {
        return;
      }

      setDragging({
        dragging: false,
      });

      const frame = getFrameFromX({
        clientX: getClientXWithScroll(e.clientX) - left,
        durationInFrames: getCurrentDuration(),
        width,
        extrapolate: "clamp",
      });

      setCurrentFrame(frame);

      if (dragging.wasPlaying) {
        playerRef.current?.play();
      }
    },
    [dragging, left, width]
  );

  const onPointerDown = useCallback(
    (e: React.PointerEvent<HTMLDivElement>) => {
      if (e.button !== 0) {
        return;
      }

      if (e.button !== 0) {
        return;
      }

      const frame = getFrameFromX({
        clientX: getClientXWithScroll(e.clientX) - left,
        durationInFrames: getCurrentDuration(),
        width,
        extrapolate: "clamp",
      });

      playerRef.current?.seekTo(frame);
      // seek(frame);
      setDragging({
        dragging: true,
        wasPlaying: playerRef.current?.isPlaying() ?? false,
      });

      // playerRef.current?.pause();
    },
    [left, width]
  );

  useEffect(() => {
    if (!dragging.dragging) {
      return;
    }

    window.addEventListener("pointermove", onPointerMoveScrubbing);
    window.addEventListener("pointerup", onPointerUpScrubbing);
    return () => {
      window.removeEventListener("pointermove", onPointerMoveScrubbing);
      window.removeEventListener("pointerup", onPointerUpScrubbing);
    };
  }, [dragging.dragging, onPointerMoveScrubbing, onPointerUpScrubbing]);

  return (
    <div
      onPointerDown={onPointerDown}
      ref={sliderAreaRef}
      style={containerStyle}
    ></div>
  );
};
