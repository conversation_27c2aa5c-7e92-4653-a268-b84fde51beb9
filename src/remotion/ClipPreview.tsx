import React, { useMemo, useEffect, useRef, useState } from "react";
import {
  CalculateMetadataFunction,
  OffthreadVideo,
  useVideoConfig,
  prefetch,
  useCurrentScale,
  useCurrentFrame,
  Sequence,
} from "remotion";
import { parseMedia } from "@remotion/media-parser";
import { ICropBox, ISubtitles, IVideoMetadata, IVideoSegment } from "./types";
import { Subtitles } from "./components/Subtitles";
import {
  calibrateSubtitles,
  detectAspectRatio,
  getFrameDimensions,
} from "../utils";
import {
  getCurrentSegmentIndex,
  setTemporaryCropBox,
} from "./imperative-state";
import Draggable, { DraggableData, DraggableEvent } from "react-draggable";
import {
  useClipSubtitles,
  useClipSegments,
  useClipConfigAPI,
} from "../features/clipConfig/ClipConfigApi";

// Create a global cache to track which videos have been prefetched
// This prevents duplicate prefetch calls across multiple ClipPreview instances
const prefetchCache = new Set<string>();

// Constants for optimization
const PREMOUNT_FRAMES = 50; // Number of frames to premount segments
const OVERLAP_FRAMES = 3; // Number of frames to overlap between segments

type Props = {
  sourceVideoUrl: string;
  segments?: IVideoSegment[]; // now optional
  dimensions: { width: number; height: number };
  sourceVideoMetadata: IVideoMetadata;
  isBuffering: boolean;
  cropMode: boolean;
  subtitles?: ISubtitles; // now optional
  useImperativeSegmentIndex?: boolean; // new prop
};

type VideoToEmbed = {
  src: string;
  durationInFrames: number | null;
  startFrame: number;
};

export const calculateMetadata: CalculateMetadataFunction<Props> = async ({
  props,
}) => {
  const { segments: propSegments, sourceVideoMetadata } = props;

  if (!propSegments || propSegments.length === 0) {
    return {
      props,
      fps: sourceVideoMetadata.fps,
      durationInFrames: 0,
    };
  }

  // Ensure segments is an array and not undefined/empty before proceeding
  const segments: IVideoSegment[] = Array.isArray(propSegments)
    ? propSegments
    : [];

  if (segments.length === 0) {
    // Handle case where propSegments might be an empty object or something unexpected
    // though type Props suggests it should be IVideoSegment[] or undefined.
    return {
      props,
      fps: sourceVideoMetadata.fps,
      durationInFrames: 0,
    };
  }

  const minStartTimeInSeconds = Math.min(...segments.map((s) => s.start));
  const maxEndTimeInSeconds = Math.max(...segments.map((s) => s.end));

  const durationInSeconds = maxEndTimeInSeconds - minStartTimeInSeconds;
  const totalDurationInFrames = Math.max(
    0, // Ensure duration is not negative
    Math.floor(durationInSeconds * sourceVideoMetadata.fps)
  );

  return {
    props: {
      ...props,
      // Pass minStartTimeInSeconds to the component via props for convenience,
      // or let the component calculate it. Passing it avoids recalculation.
      // However, modifying props in calculateMetadata is generally for data fetching/transformation.
      // Let's allow the component to calculate it via useMemo as it's derived purely from props.
    },
    fps: sourceVideoMetadata.fps,
    durationInFrames: totalDurationInFrames,
  };
};

export const ClipPreview: React.FC<Props> = React.memo(
  ({
    sourceVideoUrl,
    segments: segmentsProp,
    dimensions,
    sourceVideoMetadata,
    isBuffering,
    cropMode = false,
    subtitles: subtitlesProp,
    useImperativeSegmentIndex = false,
  }) => {
    // Context fallback for Editor
    const contextSegments = useClipSegments();
    const contextSubtitles = useClipSubtitles();

    const { onSubtitlesChange } = useClipConfigAPI();

    // Use props if provided, otherwise context
    const segments = segmentsProp ?? contextSegments;
    const subtitles = subtitlesProp ?? contextSubtitles;

    // Dev warning if neither is available
    if (!segments) {
      if (process.env.NODE_ENV === "development") {
        // eslint-disable-next-line no-console
        console.warn(
          "ClipPreview: Neither props nor context provided for segments/subtitles."
        );
      }
    }

    // Get the current frame and fps
    const frame = useCurrentFrame();
    const { fps, durationInFrames } = useVideoConfig();
    const scale = useCurrentScale();

    // Calculate minStartTimeInSeconds for the composition timeline
    const minStartTimeInSeconds = useMemo(() => {
      if (!segments || segments.length === 0) return 0;
      return Math.min(...segments.map((s) => s.start));
    }, [segments]);

    // Calculate the current segment index
    let currentSegmentIndex: number | null;

    if (useImperativeSegmentIndex) {
      currentSegmentIndex = getCurrentSegmentIndex();
    } else {
      currentSegmentIndex = useMemo(() => {
        if (!segments || segments.length === 0) return null; // Return null if no segments

        const compositionTimeSeconds = frame / fps;
        // minStartTimeInSeconds is used to map composition time to segment's relative time

        const idx = segments.findIndex((segment) => {
          const segmentStartInComposition =
            segment.start - minStartTimeInSeconds;
          const segmentEndInComposition = segment.end - minStartTimeInSeconds;
          return (
            compositionTimeSeconds >= segmentStartInComposition &&
            compositionTimeSeconds < segmentEndInComposition
          );
        });

        return idx !== -1 ? idx : null; // Return null if in a gap
      }, [frame, segments, fps, minStartTimeInSeconds]);
    }

    const currentSegment: IVideoSegment | null = useMemo(
      () =>
        currentSegmentIndex !== null ? segments[currentSegmentIndex] : null,
      [segments, currentSegmentIndex]
    );

    const currentSegmentCropBox = useMemo(
      () => currentSegment?.crop_box || null,
      [currentSegment]
    );

    // Use the crop box for the current segment

    // Add a ref to the container div
    const containerRef = useRef<HTMLDivElement>(null);
    // Store element dimensions and position
    const [elementMetrics, setElementMetrics] = useState<{
      width: number;
      height: number;
      x: number;
      y: number;
      visual: { width: number; height: number };
      composition: { width: number; height: number };
    } | null>(null);

    const [currentFramePosition, setCurrentFramePosition] = useState<{
      x: number;
      y: number;
    }>({
      x: 0,
      y: 0,
    });

    // Measure the element dimensions and position
    useEffect(() => {
      if (!containerRef.current) return;

      // Get measurements
      const rect = containerRef.current.getBoundingClientRect();

      // Calculate visual dimensions (what you see in DevTools)
      const visualWidth = rect.width * scale;
      const visualHeight = rect.height * scale;

      // Calculate composition dimensions (unscaled)
      const compositionWidth = rect.width / scale;
      const compositionHeight = rect.height / scale;

      // Store all metrics
      setElementMetrics({
        width: rect.width,
        height: rect.height,
        x: rect.x,
        y: rect.y,
        visual: {
          width: visualWidth,
          height: visualHeight,
        },
        composition: {
          width: compositionWidth,
          height: compositionHeight,
        },
      });

      // Use ResizeObserver to track size changes
      const resizeObserver = new ResizeObserver((entries) => {
        const { width, height } = entries[0].contentRect;
        const x = rect.x; // Use the initial x position
        const y = rect.y; // Use the initial y position

        setElementMetrics({
          width,
          height,
          x,
          y,
          visual: {
            width: width * scale,
            height: height * scale,
          },
          composition: {
            width: width / scale,
            height: height / scale,
          },
        });

        // set frame position here because in initiating crop mode the composition size is changing
        const ratio = width / sourceVideoMetadata.width;
        const currentSegment = segments[currentSegmentIndex ?? 0];

        if (currentSegment?.crop_box) {
          const currentFramePosition = {
            x: currentSegment.crop_box.x1 * ratio,
            y: currentSegment.crop_box.y1 * ratio,
          };

          setCurrentFramePosition(currentFramePosition);
        }
      });

      // Start observing
      resizeObserver.observe(containerRef.current);

      // Cleanup
      return () => resizeObserver.disconnect();
    }, [scale, currentSegmentIndex, segments, sourceVideoMetadata]);

    // Optimized prefetch with caching
    useEffect(() => {
      // Only prefetch if not already in cache
      if (sourceVideoUrl && !prefetchCache.has(sourceVideoUrl)) {
        // Add to cache before prefetching to prevent race conditions
        prefetchCache.add(sourceVideoUrl);

        // Use logLevel: 'warn' to track prefetching events if needed
        prefetch(sourceVideoUrl, {
          // logLevel: "warn", // Optional: for debugging prefetch
          contentType: "video/mp4", // Optional: hint content type if server doesn't set it well
        });
      }
    }, [sourceVideoUrl]);

    const handleFrameDrag = (
      e: DraggableEvent,
      position: DraggableData
    ): void => {
      setCurrentFramePosition({
        x: position.x,
        y: position.y,
      });
    };

    const handleFrameDragStop = (
      e: DraggableEvent,
      position: DraggableData
    ): void => {
      const currentSegment = segments[currentSegmentIndex ?? 0];

      if (currentSegment && currentSegment.crop_box) {
        const currentSegmentCropBox = currentSegment.crop_box;
        const videoWidth = sourceVideoMetadata.width;
        const videoHeight = sourceVideoMetadata.height;

        const ratio = (elementMetrics?.visual.width ?? 0) / videoWidth;
        const scaledX1: number = (position.x * scale) / ratio;
        const scaledY1: number = (position.y * scale) / ratio;

        // 1. Round x1/y1
        let x1 = Math.round(scaledX1);
        let y1 = Math.round(scaledY1);

        // 2. Calculate float width/height and aspect ratio
        const floatWidth = currentSegmentCropBox.x2 - currentSegmentCropBox.x1;
        const floatHeight = currentSegmentCropBox.y2 - currentSegmentCropBox.y1;
        const aspectRatio = floatWidth / floatHeight;

        // 3. Round width, then calculate height to preserve aspect ratio
        let width = Math.round(floatWidth);
        let height = Math.round(width / aspectRatio);

        // 4. Clamp to video bounds and preserve aspect ratio
        if (x1 + width > videoWidth) {
          width = videoWidth - x1;
          width = Math.max(width, 1); // minimum size
          height = Math.round(width / aspectRatio);
        }
        if (y1 + height > videoHeight) {
          height = videoHeight - y1;
          height = Math.max(height, 1); // minimum size
          width = Math.round(height * aspectRatio);
          if (x1 + width > videoWidth) {
            width = videoWidth - x1;
            width = Math.max(width, 1);
            height = Math.round(width / aspectRatio);
          }
        }

        // 5. Ensure minimum size
        width = Math.max(width, 1);
        height = Math.max(height, 1);

        // 6. Calculate x2/y2
        const x2 = x1 + width;
        const y2 = y1 + height;

        const newCropBox: ICropBox = {
          x1,
          x2,
          y1,
          y2,
        };

        setTemporaryCropBox(newCropBox);
      }
    };

    const hasCropBox = segments.some((segment) => segment.crop_box !== null);

    const calculateDimensions = useMemo(() => {
      if (!hasCropBox) {
        const originalAspectRatio =
          sourceVideoMetadata.width / sourceVideoMetadata.height;
        const calculatedHeight = Math.floor(
          dimensions.width / originalAspectRatio
        );

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      } else if (cropMode) {
        const cropBoxWidth = sourceVideoMetadata.width;
        const cropBoxHeight = sourceVideoMetadata.height;
        const cropBoxAspectRatio = cropBoxWidth / cropBoxHeight;
        const calculatedHeight = Math.floor(
          dimensions.width / cropBoxAspectRatio
        );

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      } else if (currentSegmentCropBox) {
        const cropBoxWidth =
          currentSegmentCropBox.x2 - currentSegmentCropBox.x1;
        const cropBoxHeight =
          currentSegmentCropBox.y2 - currentSegmentCropBox.y1;
        const cropBoxAspectRatio = cropBoxWidth / cropBoxHeight;
        const calculatedHeight = Math.floor(
          dimensions.width / cropBoxAspectRatio
        );

        return {
          width: dimensions.width,
          height: calculatedHeight,
        };
      }

      return {
        width: dimensions.width,
        height: Math.floor(dimensions.height || 0),
      };
    }, [
      dimensions,
      hasCropBox,
      currentSegmentCropBox,
      sourceVideoMetadata,
      cropMode,
    ]);

    const calculateVideoStylesForSegment = (
      segmentForStyle: IVideoSegment | null,
      currentCropMode: boolean // Renamed from cropMode to avoid conflict
    ) => {
      const activeSegmentCropBox = segmentForStyle?.crop_box || null;

      if (!activeSegmentCropBox) {
        return {
          height: calculateDimensions.height,
          width: "auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        } as const;
      }

      let cropBoxToWorkWith: ICropBox;

      if (currentCropMode) {
        // When cropMode is active, we might want to show the full video for the segment being edited.
        // For now, this logic applies the crop box of the segment. If it's the active one being edited,
        // the Draggable component takes precedence for visuals. This part might need refinement
        // if non-active segments should behave differently in cropMode.
        // The original logic showed sourceVideoMetadata dimensions if cropMode was true globally.
        // Let's simplify: if currentCropMode is true, and it IS the currentSegment, show full, else show crop_box.
        // This requires knowing if segmentForStyle IS currentSegment.
        // For initial pass, let's use activeSegmentCropBox, or full video if cropMode implies editing THIS segment.
        // The provided `cropMode` (now `currentCropMode`) refers to the global state.
        // The `Draggable` overlay handles the visual cropping interface.
        // The video underneath, when `currentCropMode` is true, should probably show the full extent
        // of the source video, so the crop_box can be defined against it.
        // This applies if segmentForStyle IS the currentSegment that is being targeted by cropMode.
        // This is a simplification for now: if global cropMode is on, use sourceVideoMetadata for simplicity for all segments.
        // This is likely NOT the final behavior desired for non-edited segments during crop mode.
        // A better approach: calculateVideoStylesForSegment gets the specific crop_box for the segment. The global
        // cropMode enables the Draggable overlay for the *currentSegment*. The video for *that currentSegment*
        // should be styled to show its full extent, while others show their crop.
        // For now, to make progress: If currentCropMode is true, AND segmentForStyle is THE currentSegment, show full video.
        // This means this function needs to know if segmentForStyle === currentSegment.
        // Let's stick to using the segment's own crop_box, or full video if no crop_box, and let
        // the Draggable component and its container handle the crop mode visuals separately for the active segment.
        // The logic here will determine how the <OffthreadVideo> itself is scaled and positioned.

        // If global crop mode is on, and this function is called for the *active* segment (currentSegment),
        // then we want to display the video using the source dimensions to allow free cropping.
        // Otherwise, display with its own crop box.
        if (currentCropMode && segmentForStyle === currentSegment) {
          cropBoxToWorkWith = {
            x1: 0,
            y1: 0,
            x2: sourceVideoMetadata.width,
            y2: sourceVideoMetadata.height,
          };
        } else {
          cropBoxToWorkWith = activeSegmentCropBox;
        }
      } else {
        cropBoxToWorkWith = activeSegmentCropBox;
      }

      const cropBoxWidth = cropBoxToWorkWith.x2 - cropBoxToWorkWith.x1;
      const cropBoxHeight = cropBoxToWorkWith.y2 - cropBoxToWorkWith.y1;

      const containerWidth = dimensions.width; // Overall player width from props
      const containerHeight = calculateDimensions.height; // Calculated height for the player area

      const widthRatio = containerWidth / cropBoxWidth;
      const heightRatio = containerHeight / cropBoxHeight;
      const scaleToFit = Math.min(widthRatio, heightRatio);

      const videoScaledWidth = Math.floor(
        sourceVideoMetadata.width * scaleToFit
      );
      const videoScaledHeight = Math.floor(
        sourceVideoMetadata.height * scaleToFit
      );

      const offsetX = Math.floor(
        (containerWidth - cropBoxWidth * scaleToFit) / 2
      );
      const offsetY = Math.floor(
        (containerHeight - cropBoxHeight * scaleToFit) / 2
      );

      const finalLeft = Math.floor(offsetX - cropBoxToWorkWith.x1 * scaleToFit);
      const finalTop = Math.floor(offsetY - cropBoxToWorkWith.y1 * scaleToFit);

      return {
        position: "absolute",
        width: videoScaledWidth,
        height: videoScaledHeight,
        left: finalLeft,
        top: finalTop,
        display: "block",
      } as const;
    };

    // **Linter Fix: Prepare dimensions for Subtitles**
    // This needs to be calculated before the return statement,
    // and relies on currentSegment, sourceVideoMetadata, and calculateVideoStylesForSegment.
    let subtitleCompositionWidthForProps = 0;
    let subtitleCompositionHeightForProps = 0;

    if (currentSegmentIndex !== null && currentSegment) {
      // Use calculateVideoStylesForSegment for the current active segment
      const videoStyles = calculateVideoStylesForSegment(
        currentSegment,
        cropMode
      );
      const videoHeightInComposition = videoStyles.height as number;

      let actualVideoWidthInComposition: number;
      if (typeof videoStyles.width === "number") {
        actualVideoWidthInComposition = videoStyles.width;
      } else {
        // width is 'auto' - this case should be less frequent with the new func
        if (sourceVideoMetadata.height > 0) {
          actualVideoWidthInComposition =
            videoHeightInComposition *
            (sourceVideoMetadata.width / sourceVideoMetadata.height);
        } else {
          actualVideoWidthInComposition = 0; // Fallback
        }
      }
      subtitleCompositionWidthForProps = Math.floor(
        actualVideoWidthInComposition * scale
      ); // scale is useCurrentScale()
      subtitleCompositionHeightForProps = Math.floor(
        videoHeightInComposition * scale
      );
    }
    // **End Linter Fix Prep**

    const frameDimensionsForDraggable = useMemo(() => {
      if (!cropMode || !elementMetrics || !currentSegment) {
        return { frameWidth: 0, frameHeight: 0 }; // Default or hide if not in crop mode for a segment
      }

      const activeCropBox = currentSegment.crop_box;
      let aspectRatioDetails;

      if (activeCropBox) {
        aspectRatioDetails = detectAspectRatio(
          activeCropBox.x2 - activeCropBox.x1,
          activeCropBox.y2 - activeCropBox.y1
        );
      } else {
        // If no crop_box on current segment, use source video aspect ratio for the draggable frame guide
        aspectRatioDetails = detectAspectRatio(
          sourceVideoMetadata.width,
          sourceVideoMetadata.height
        );
      }

      return getFrameDimensions(aspectRatioDetails, {
        width: elementMetrics.width,
        height: elementMetrics.height,
      });
    }, [cropMode, elementMetrics, currentSegment, sourceVideoMetadata]);

    return (
      <>
        {cropMode && elementMetrics && (
          <div
            style={{
              position: "fixed",
              left: 0,
              top: 0,
              width: elementMetrics.width,
              height: elementMetrics.height,
              zIndex: 1000,
              overflow: "visible",
            }}
          >
            {/* Add dimming overlay around the frame */}
            <div
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                width: "100%",
                height: "100%",
                // backgroundColor: "rgba(0, 0, 0, 0.5)",
                zIndex: 1,
              }}
            />

            <Draggable
              axis="both"
              position={currentFramePosition}
              bounds="parent"
              onDrag={handleFrameDrag}
              onStop={handleFrameDragStop}
              scale={scale}
            >
              <div
                className="video-frame"
                id="videoFrame"
                style={{
                  width: `${frameDimensionsForDraggable.frameWidth}px`,
                  height: `${frameDimensionsForDraggable.frameHeight}px`,
                  border: "1px solid #E1E1E1",
                  boxSizing: "border-box",
                  cursor: "move",
                  position: "relative",
                  overflow: "visible",
                  backgroundColor: "transparent",
                  zIndex: 2,
                  boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.5)",
                }}
              >
                {/* Corner handles - restyled to match Figma design */}
                <div
                  className="corner-handle top-left"
                  style={{
                    position: "absolute",
                    top: "-4px",
                    left: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle top-right"
                  style={{
                    position: "absolute",
                    top: "-4px",
                    right: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle bottom-left"
                  style={{
                    position: "absolute",
                    bottom: "-4px",
                    left: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                <div
                  className="corner-handle bottom-right"
                  style={{
                    position: "absolute",
                    bottom: "-4px",
                    right: "-4px",
                    width: "8px",
                    height: "8px",
                    background: "#E1E1E1",
                    borderRadius: "50%",
                  }}
                ></div>

                {/* Grid lines - 3x3 grid as seen in Figma */}
                <div
                  style={{
                    position: "absolute",
                    top: "33.33%",
                    left: 0,
                    right: 0,
                    height: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    top: "66.66%",
                    left: 0,
                    right: 0,
                    height: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    left: "33.33%",
                    top: 0,
                    bottom: 0,
                    width: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                <div
                  style={{
                    position: "absolute",
                    left: "66.66%",
                    top: 0,
                    bottom: 0,
                    width: "1px",
                    background: "#E1E1E1",
                  }}
                ></div>

                {/* Semi-transparent overlay for better visibility */}
                <div
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    pointerEvents: "none",
                  }}
                ></div>
              </div>
            </Draggable>
          </div>
        )}
        <div
          ref={containerRef}
          style={{
            position: "relative",
            // !IMPORTANT: In rendering API the width and height of this container has to be explicit numbers!
            width: "100%",
            maxHeight: "100%",
            overflow: "hidden",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {/* Add a black background that will show through when no video is playing */}
          <div
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "black",
              zIndex: 0,
            }}
          />

          {segments &&
            segments.map((segment, index) => {
              const sequenceFromTimeInSeconds =
                segment.start - minStartTimeInSeconds;
              const sequenceFromFrame = Math.floor(
                sequenceFromTimeInSeconds * fps
              );
              const sequenceDurationInSeconds = segment.end - segment.start;
              // Ensure duration is at least 1 frame to prevent Remotion errors with zero-duration sequences.
              // Also handle cases where end might be < start after some manipulation, cap duration at 0 then Math.max to 1.
              const calculatedDurationInFrames = Math.floor(
                sequenceDurationInSeconds * fps
              );
              const sequenceDurationInFrames = Math.max(
                1,
                calculatedDurationInFrames < 0 ? 0 : calculatedDurationInFrames
              );

              const videoStartFromFrame = Math.floor(segment.start * fps);

              const isCurrentlyCroppingThisSegment =
                cropMode && currentSegment === segment;

              return (
                <Sequence
                  key={`segment-${index}-${segment.start}-${segment.end}`} // Using index and start/end for a more stable key
                  from={sequenceFromFrame}
                  durationInFrames={sequenceDurationInFrames}
                  premountFor={fps * 10} // Premount 2 seconds before playback
                >
                  <OffthreadVideo
                    pauseWhenBuffering
                    crossOrigin="anonymous"
                    src={sourceVideoUrl} // Use clean sourceVideoUrl for all segments
                    startFrom={videoStartFromFrame}
                    volume={1}
                    style={calculateVideoStylesForSegment(
                      segment,
                      isCurrentlyCroppingThisSegment
                    )}
                  />
                </Sequence>
              );
            })}

          {!isBuffering &&
            currentSegmentIndex !== null &&
            subtitles &&
            subtitles.items && (
              <Subtitles
                animationType="none"
                subtitles={{
                  ...subtitles,
                  items: calibrateSubtitles(subtitles.items, segments),
                }}
                sourceVideoMetadata={sourceVideoMetadata}
                compositionDimensions={{
                  width: subtitleCompositionWidthForProps,
                  height: subtitleCompositionHeightForProps,
                }}
                configUpdate={(updatedSubtitles) => {
                  // When position is updated, preserve the original items
                  // This ensures that when onSubtitlesChange is called, it retains all subtitle data
                  onSubtitlesChange({
                    ...updatedSubtitles,
                    items: subtitles.items,
                  });
                }}
              />
            )}
        </div>
      </>
    );
  },
  // Custom equality function to prevent ANY rerenders during playback
  (prevProps, nextProps) => {
    // Only rerender if these props change, NOT on frame changes
    return (
      prevProps.sourceVideoUrl === nextProps.sourceVideoUrl &&
      prevProps.dimensions.width === nextProps.dimensions.width &&
      prevProps.dimensions.height === nextProps.dimensions.height &&
      prevProps.isBuffering === nextProps.isBuffering &&
      prevProps.segments === nextProps.segments &&
      prevProps.sourceVideoMetadata === nextProps.sourceVideoMetadata &&
      prevProps.cropMode === nextProps.cropMode &&
      prevProps.useImperativeSegmentIndex ===
        nextProps.useImperativeSegmentIndex
    );
  }
);
