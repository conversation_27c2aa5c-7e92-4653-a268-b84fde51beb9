.skeleton {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--skeleton-bg, #222);

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    background-image: linear-gradient(
      var(--shimmer-angle, 90deg),
      transparent 10%,
      rgba(255, 255, 255, 0.1) calc(50% - var(--shimmer-width, 10%)),
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.1) calc(50% + var(--shimmer-width, 10%)),
      transparent 100%
    );
    background-repeat: no-repeat;
    background-size: 200% 200%;

    animation: shimmer var(--shimmer-speed, 1.5s) ease-in-out infinite;
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 0%;
  }
  100% {
    background-position: -200% 0%;
  }
}
