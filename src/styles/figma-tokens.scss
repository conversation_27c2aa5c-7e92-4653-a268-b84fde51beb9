:root {

  /* btn */
  --btn-destructive-bg-color: #ff6b6b;
  --btn-destructive-border-color: #fafafa;
  --btn-destructive-disabled-bg-color: #ff6b6b99;
  --btn-destructive-hover-bg-color: #ec4c4c;
  --btn-destructive-text-color: #121212;
  --btn-floating-contextmenu-bg-color: #b3b3b3;
  --btn-floating-contextmenu-border-color: #3a3a3a;
  --btn-primary-bg-color: #fafafa;
  --btn-primary-border-color: #fafafa;
  --btn-primary-disable-bg-color: #fafafa99;
  --btn-primary-hover-bg-color: #e1e1e1;
  --btn-primary-text-color: #181818;
  --btn-secondary-border-color: #3a3a3a;
  --btn-secondary-disabled-text-color: #fafafa99;
  --btn-secondary-focus-border-color: #fafafa;
  --btn-secondary-hover-bg-color: #3a3a3a;
  --btn-secondary-text-color: #fafafa;

  /* captions */
  --captions-highlight-default: #ffe100;
  --captions-text-color: #ffffff;
  --captions-text-stroke-color: #000000;

  /* card */
  --card-border-color: #3a3a3a;
  --card-text-color: #e1e1e1;

  /* checkbox */
  --checkbox-bg-color-false: #121212;
  --checkbox-bg-color-true: #fafafa;
  --checkbox-border-color: #3a3a3a;
  --checkbox-check-color: #181818;
  --checkbox-disabled-color: #fafafa99;
  --checkbox-focus-color: #fafafa;
  --checkbox-hover-color: #b3b3b3;
  --checkbox-invalid-color: #ff6b6b;
  --checkbox-label-color: #fafafa;

  /* color-picker */
  --color-picker-bg-color: #181818;
  --color-picker-border-color: #3a3a3a;

  /* dialog */
  --dialog-bg-color: #181818;
  --dialog-border-color: #3a3a3a;
  --dialog-text-color: #e1e1e1;

  /* expander */
  --expander-bg-color-default: #181818;
  --expander-bg-color-hover: #3a3a3a;
  --expander-bottom-border-color: #3a3a3a;
  --expander-focus-border-color: #fafafa;
  --expander-text-color-default: #e1e1e1;
  --expander-text-color-disabled: #b3b3b3;

  /* label */
  --label-default-border-color: #4caf50;
  --label-default-border-error-color: #ff6b6b;
  --label-default-progress-color: #4caf504d;
  --label-default-text-color: #4caf50;
  --label-default-text-error-color: #ff6b6b;
  --label-small-default-bg-color: #3a3a3a;
  --label-small-default-text-color: #fafafa;
  --label-small-high-virality-bg-color: #4caf50;
  --label-small-low-virality-bg-color: #eec34d;
  --label-small-virality-text-color: #181818;

  /* menu */
  --menu-bg-color: #181818;
  --menu-border-color: #3a3a3a;
  --menu-menu-item-disabled-text-color: #fafafa99;
  --menu-menu-item-error-color: #ff6b6b;
  --menu-menu-item-focus-border-color: #fafafa;
  --menu-menu-item-hover-bg-color: #3a3a3a;
  --menu-menu-item-subtitle-color: #b3b3b3;
  --menu-menu-item-text-color: #e1e1e1;

  /* notification */
  --notification-default-bg-color: #181818;
  --notification-default-border-color: #3a3a3a;
  --notification-default-text-color: #fafafa;
  --notification-error-bg-color: #ff6b6b14;
  --notification-error-border-color: #ff6b6b;
  --notification-error-text-color: #ff6b6b;
  --notification-success-bg-color: #4caf5014;
  --notification-success-border-color: #4caf50;
  --notification-success-text-color: #4caf50;

  /* progressbar */
  --progressbar-progress-color: #fafafa;
  --progressbar-track-color: #b3b3b3;

  /* selector */
  --selector-bg-color: #181818;
  --selector-border-color: #3a3a3a;
  --selector-selector-item-disabled-text-color: #fafafa99;
  --selector-selector-item-focus-border-color: #fafafa;
  --selector-selector-item-hover-bg-color: #3a3a3a;
  --selector-selector-item-selected-bg-color: #b3b3b3;
  --selector-selector-item-selected-text-color: #181818;
  --selector-selector-item-subtitle-color: #b3b3b3;
  --selector-selector-item-text-color: #e1e1e1;

  /* surface */
  --surface-bg-color: #181818;
  --surface-border-color: #3a3a3a;
  --surface-main-bg-color: #121212;
  --surface-subtext-color: #b3b3b3;
  --surface-text-color: #fafafa;

  /* switch */
  --switch-bg-color-off: #3a3a3a;
  --switch-bg-color-on: #fafafa;
  --switch-border-color-off: #3a3a3a;
  --switch-border-color-on: #fafafa;
  --switch-disabled-color: #b3b3b3;
  --switch-focus-border-color: #fafafa;
  --switch-handle-color-off: #fafafa;
  --switch-handle-color-on: #3a3a3a;
  --switch-hover-color: #e1e1e1;

  /* table */
  --table-border-color: #3a3a3a;
  --table-header-text-color: #b3b3b3;
  --table-text-color: #e1e1e1;

  /* tabs */
  --tabs-default-bg-color-hover: #3a3a3a;
  --tabs-default-text-color: #fafafa;
  --tabs-focus-border-color: #fafafa;
  --tabs-selected-bg-color: #fafafa;
  --tabs-selected-bg-color-hover: #e1e1e1;
  --tabs-selected-text-color: #181818;

  /* textbox */
  --textbox-bg-color: #121212;
  --textbox-border-color: #3a3a3a;
  --textbox-disabled-text-color: #fafafa99;
  --textbox-focus-border-color: #fafafa;
  --textbox-hint-text-color: #b3b3b3;
  --textbox-hover-border-color: #b3b3b3;
  --textbox-invalid-color: #ff6b6b;
  --textbox-placeholder-text-color: #b3b3b3;
  --textbox-text-color: #e1e1e1;
  --textbox-valid-color: #4caf50;

  /* timeline */
  --timeline-active-video-marker-bg-color: #73a0ff;
  --timeline-audio-bg-color: #3a3a3a;
  --timeline-audio-graph-color: #b3b3b3;
  --timeline-current-marker-bg-color: #ffc773;
  --timeline-video-inner-border: #000000;
  --timeline-video-marker-color: #ffffff;
  --timeline-video-outer-border: #ffffff;

  /* tooltip */
  --tooltip-bg-color: #3a3a3a;
  --tooltip-text-color: #e1e1e1;

  /* uploader */
  --uploader-bg-color: #181818;
  --uploader-border-color: #b3b3b3;
  --uploader-error-color: #ff6b6b;
  --uploader-subtext-color: #b3b3b3;
  --uploader-text-color: #e1e1e1;

  /* video-preview */
  --video-preview-bg-color: #3a3a3a;
  --video-preview-content-color: #b3b3b3;
}
