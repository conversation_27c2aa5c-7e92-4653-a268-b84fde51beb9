@import "./styles/figma-tokens.scss";
@import "./styles/_skeleton.scss";

@font-face {
  font-family: "SF Pro Display";
  src: url("./assets/fonts/SF-Pro-Display-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: "SF Pro Display";
  src: url("./assets/fonts/SF-Pro-Display-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "SF Pro Display";
  src: url("./assets/fonts/SF-Pro-Display-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
}

::-webkit-scrollbar {
  width: 8px; /* Adjust scrollbar width */
  height: 8px;
  border-radius: 30px;
  cursor: pointer;
}

/* Track */
::-webkit-scrollbar-track {
  background: #222222; /* Darker track for dark theme */
  border-radius: 30px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #888888; /* Lighter thumb for better visibility */
  border-radius: 30px;
  cursor: pointer;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #aaaaaa; /* Even lighter on hover for feedback */
  cursor: pointer;
}
