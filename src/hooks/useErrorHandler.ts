import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { ErrorResponse, ErrorResponseDetailType } from "../types";
import { parseErrorMessage } from "../utils";
import { ModalType } from "../features/modal/types";
import { ModalConfig } from "../features/modal/types";
import {
  closeAllModals,
  closeModal,
  openModal,
} from "../features/modal/modalSlice";
import { RootState } from "../store";
import { getCustomerPortalUrl } from "@/features/auth/authActions";

export const useErrorHandler = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userData } = useSelector((state: RootState) => state.auth);

  const handleError = async (error: ErrorResponse, statusCode: number) => {
    if (error) {
      const message = parseErrorMessage(error);

      dispatch(closeAllModals());

      switch (statusCode) {
        // Invalid credentials
        case 401:
          dispatch(
            openModal({
              type: ModalType.BASIC,
              title: "You are not authorized",
              subtitle: "Invalid credentials",
              actions: [
                {
                  label: "Go to login",
                  variant: "primary",
                  fullWidth: true,
                  onClick: () => {
                    navigate("/login");

                    dispatch(closeModal());
                  },
                },
              ],
            })
          );

          break;
        // Trial expired
        // Quota exceeded
        case 403:
          const errorResponseType: ErrorResponseDetailType =
            error?.detail[0]?.type;

          const stripeCustomerId = userData?.stripe_customer_id;

          if (
            errorResponseType === ErrorResponseDetailType.OUT_OF_BOOST ||
            errorResponseType === ErrorResponseDetailType.OUT_OF_STORAGE
          ) {
            const isOutofBoost =
              errorResponseType === ErrorResponseDetailType.OUT_OF_BOOST;

            if (stripeCustomerId) {
              const result = await dispatch(getCustomerPortalUrl() as any);

              if (getCustomerPortalUrl.fulfilled.match(result)) {
                const { url } = result.payload;

                window.open(url, "_blank");
              }
            } else {
              const pricingModalConfig: ModalConfig = {
                type: ModalType.PRICING,
                title: `Out of ${isOutofBoost ? "boost" : "storage"}`,
                subtitle: `You’re out of ${
                  isOutofBoost ? "boost" : "storage"
                }. Upgrade your plan to unlock your full experience.`,
              };

              dispatch(openModal(pricingModalConfig));
            }
          } else {
            const modalConfig: ModalConfig = {
              type: ModalType.BASIC,
              title: "Oops! Something went wrong!",
              subtitle: message,
              actions: [
                {
                  label: "Okay",
                  variant: "primary",
                  fullWidth: true,
                  onClick: () => {
                    dispatch(closeModal());
                  },
                },
              ],
            };

            dispatch(openModal(modalConfig));
          }

          break;
        default:
          const modalConfig: ModalConfig = {
            type: ModalType.BASIC,
            title: "Oops! Something went wrong!",
            subtitle: message,
            actions: [
              {
                label: "Okay",
                variant: "primary",
                fullWidth: true,
                onClick: () => {
                  dispatch(closeModal());
                },
              },
            ],
          };

          dispatch(openModal(modalConfig));

          break;
      }
    } else {
      const modalConfig: ModalConfig = {
        type: ModalType.BASIC,
        title: "Oops! Something went wrong!",
        subtitle: "Please try again later.",
        actions: [
          {
            label: "Okay",
            variant: "primary",
            fullWidth: true,
            onClick: () => {
              dispatch(closeModal());
            },
          },
        ],
      };

      dispatch(openModal(modalConfig));
    }
  };

  return { handleError };
};
