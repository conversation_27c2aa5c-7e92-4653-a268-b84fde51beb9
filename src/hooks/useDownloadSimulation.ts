import { useEffect, useState, useRef, useCallback } from "react";
import { ProcessingStatus } from "../types";

// Enum for simulation statuses
export enum SimulationStatus {
  IDLE = "IDLE",
  SIMULATING = "SIMULATING",
  COMPLETED = "COMPLETED",
}

// Total simulation time in milliseconds
// Based on average observed processing time:
// For videos without duration metadata, use 15 minutes as default
const DEFAULT_SIMULATION_TIME = 15 * 60 * 1000;
// Update interval in milliseconds
const UPDATE_INTERVAL = 1000;
// Minimum time between React state updates in milliseconds
const MIN_STATE_UPDATE_INTERVAL = 3000; // Only update React state every 3 seconds

interface SimulationState {
  endTime: number; // When the simulation will end
  status: SimulationStatus;
}

interface SimulationResult {
  percentage: number;
  eta: string;
  status: SimulationStatus;
}

// Define a type for observers (components that want to be notified of changes)
type SimulationObserver = (result: SimulationResult) => void;

/**
 * Imperative simulation manager that sits outside of React's render cycle
 */
class SimulationManager {
  private simulations: Map<
    string,
    {
      state: SimulationState;
      observers: Set<SimulationObserver>;
      lastUpdateTime: number;
      totalSimulationTime: number;
      onComplete?: () => void;
    }
  > = new Map();

  private intervalId: number | null = null;

  constructor() {
    // Start the interval tick when created
    this.startTick();
  }

  private startTick() {
    if (this.intervalId === null) {
      // Only initialize the interval if it doesn't exist
      this.intervalId = window.setInterval(() => this.tick(), UPDATE_INTERVAL);
    }
  }

  private stopTick() {
    if (this.intervalId !== null) {
      window.clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private tick() {
    const now = Date.now();

    // Early return if no simulations are active
    if (this.simulations.size === 0) {
      this.stopTick();
      return;
    }

    // Process each active simulation
    for (const [key, simulation] of this.simulations.entries()) {
      // Skip if no observers are interested in this simulation
      if (simulation.observers.size === 0) continue;

      const { state, totalSimulationTime, lastUpdateTime, onComplete } =
        simulation;

      // Skip if not in SIMULATING state
      if (state.status !== SimulationStatus.SIMULATING) continue;

      // Calculate current progress
      const remainingMs = Math.max(0, state.endTime - now);
      const elapsedMs = totalSimulationTime - remainingMs;

      // Calculate percentage and eta
      const currentPercentage = Math.min(
        100,
        Math.floor((elapsedMs / totalSimulationTime) * 100)
      );

      let currentEta: string;
      if (remainingMs <= 0) {
        currentEta = "0m";
      } else {
        // For download (clip), do NOT apply buffer in ETA calculation (already applied in simulation time)
        // For processing (video), keep the buffer
        const isDownload = key.startsWith("download_");
        // Based on real-world data, processing videos don't need much buffer
        const bufferMultiplier = isDownload ? 1.0 : 1.1;
        const bufferedMs = Math.ceil(remainingMs * bufferMultiplier);
        const remainingMinutes = Math.ceil(bufferedMs / 60000);
        currentEta = `${remainingMinutes}m`;
      }

      // Check if simulation is complete
      if (remainingMs <= 0) {
        // Mark as complete
        state.status = SimulationStatus.COMPLETED;

        // Create result
        const result: SimulationResult = {
          percentage: 100,
          eta: "0m",
          status: SimulationStatus.COMPLETED,
        };

        // Notify all observers
        simulation.observers.forEach((observer) => observer(result));

        // Call completion callback if provided
        if (onComplete) onComplete();

        // Remove from localStorage
        localStorage.removeItem(key);

        // Remove from active simulations
        this.simulations.delete(key);
      } else {
        // Still in progress

        // Only notify observers if enough time has passed since the last update
        // or if this is the first update (lastUpdateTime === 0)
        if (
          now - lastUpdateTime >= MIN_STATE_UPDATE_INTERVAL ||
          lastUpdateTime === 0
        ) {
          // Create result
          const result: SimulationResult = {
            percentage: currentPercentage,
            eta: currentEta,
            status: SimulationStatus.SIMULATING,
          };

          // Notify all observers
          simulation.observers.forEach((observer) => observer(result));

          // Update last update time
          simulation.lastUpdateTime = now;
        }
      }
    }
  }

  // Helper function to calculate remaining minutes with buffer
  private calculateRemainingMinutes(
    remainingMs: number,
    isDownload: boolean
  ): string {
    if (remainingMs <= 0) return "0m";
    // For download (clip), do NOT apply buffer in ETA calculation (already applied in simulation time)
    // For processing (video), keep the buffer
    // Based on real-world data, processing videos don't need much buffer
    const bufferMultiplier = isDownload ? 1.0 : 1.1;
    const bufferedMs = Math.ceil(remainingMs * bufferMultiplier);
    const remainingMinutes = Math.ceil(bufferedMs / 60000);
    return `${remainingMinutes}m`;
  }

  /**
   * Start a new simulation or resume an existing one
   */
  startSimulation(
    key: string,
    simulationTimeMs: number = DEFAULT_SIMULATION_TIME,
    onComplete?: () => void
  ): SimulationResult {
    // Check if we already have this simulation in memory
    const existingSimulation = this.simulations.get(key);
    if (
      existingSimulation &&
      existingSimulation.state.status === SimulationStatus.SIMULATING
    ) {
      // Calculate current progress
      const now = Date.now();
      const remainingMs = Math.max(0, existingSimulation.state.endTime - now);
      const elapsedMs = existingSimulation.totalSimulationTime - remainingMs;

      // Calculate percentage and eta
      const currentPercentage = Math.min(
        100,
        Math.floor((elapsedMs / existingSimulation.totalSimulationTime) * 100)
      );

      const isDownload = key.startsWith("download_");
      const currentEta = this.calculateRemainingMinutes(
        remainingMs,
        isDownload
      );

      return {
        percentage: currentPercentage,
        eta: currentEta,
        status: SimulationStatus.SIMULATING,
      };
    }

    // Check if we have a saved state in localStorage
    try {
      const savedState = localStorage.getItem(key);
      if (savedState) {
        const parsedState: SimulationState = JSON.parse(savedState);
        if (parsedState.status === SimulationStatus.SIMULATING) {
          // Calculate current progress
          const now = Date.now();
          const remainingMs = Math.max(0, parsedState.endTime - now);
          const elapsedMs = simulationTimeMs - remainingMs;

          // Calculate percentage and eta
          const currentPercentage = Math.min(
            100,
            Math.floor((elapsedMs / simulationTimeMs) * 100)
          );

          const isDownload = key.startsWith("download_");
          const currentEta = this.calculateRemainingMinutes(
            remainingMs,
            isDownload
          );

          // Create simulation object
          this.simulations.set(key, {
            state: parsedState,
            observers: new Set(),
            lastUpdateTime: 0, // Force immediate update
            totalSimulationTime: simulationTimeMs,
            onComplete,
          });

          // Ensure interval is running
          this.startTick();

          return {
            percentage: currentPercentage,
            eta: currentEta,
            status: SimulationStatus.SIMULATING,
          };
        }
      }
    } catch (error) {
      console.error(`Error loading simulation state for ${key}:`, error);
    }

    // Create a new simulation
    const now = Date.now();
    const endTime = now + simulationTimeMs;

    const newState: SimulationState = {
      endTime,
      status: SimulationStatus.SIMULATING,
    };

    // Save to localStorage
    try {
      localStorage.setItem(key, JSON.stringify(newState));
    } catch (error) {
      console.error(`Error saving simulation state for ${key}:`, error);
    }

    // Create simulation object
    this.simulations.set(key, {
      state: newState,
      observers: new Set(),
      lastUpdateTime: 0, // Force immediate update
      totalSimulationTime: simulationTimeMs,
      onComplete,
    });

    // Ensure interval is running
    this.startTick();

    // Calculate initial ETA
    const isDownload = key.startsWith("download_");
    const initialEta = this.calculateRemainingMinutes(
      simulationTimeMs,
      isDownload
    );

    return {
      percentage: 0,
      eta: initialEta,
      status: SimulationStatus.SIMULATING,
    };
  }

  /**
   * Reset a simulation (cancel it)
   */
  resetSimulation(key: string): SimulationResult {
    // Remove from localStorage
    localStorage.removeItem(key);

    // Remove from active simulations
    this.simulations.delete(key);

    return {
      percentage: 0,
      eta: "0m",
      status: SimulationStatus.IDLE,
    };
  }

  /**
   * Subscribe to simulation updates
   */
  subscribe(key: string, observer: SimulationObserver): () => void {
    const simulation = this.simulations.get(key);
    if (simulation) {
      simulation.observers.add(observer);
    } else {
      // Try to create a simulation from localStorage
      this.initializeFromLocalStorage(key, DEFAULT_SIMULATION_TIME);

      // Add the observer to the simulation (it may have been created)
      const newSimulation = this.simulations.get(key);
      if (newSimulation) {
        newSimulation.observers.add(observer);
      }
    }

    // Return unsubscribe function
    return () => {
      const simulation = this.simulations.get(key);
      if (simulation) {
        simulation.observers.delete(observer);

        // If no observers left, consider removing the simulation
        if (simulation.observers.size === 0) {
          // We don't immediately remove it in case it's resubscribed to
          // But we could add cleanup logic here if needed
        }
      }
    };
  }

  /**
   * Check if a simulation exists in localStorage and load it
   */
  initializeFromLocalStorage(
    key: string,
    simulationTimeMs: number = DEFAULT_SIMULATION_TIME,
    onComplete?: () => void
  ): SimulationResult | null {
    try {
      const savedState = localStorage.getItem(key);
      if (!savedState) return null;

      const parsedState: SimulationState = JSON.parse(savedState);

      if (parsedState.status !== SimulationStatus.SIMULATING) {
        return {
          percentage: 0,
          eta: "0m",
          status: parsedState.status,
        };
      }

      // Calculate current progress
      const now = Date.now();
      const remainingMs = Math.max(0, parsedState.endTime - now);
      const elapsedMs = simulationTimeMs - remainingMs;

      // If simulation is complete
      if (remainingMs <= 0) {
        localStorage.removeItem(key);

        if (onComplete) onComplete();

        return {
          percentage: 100,
          eta: "0m",
          status: SimulationStatus.COMPLETED,
        };
      }

      // Calculate percentage and eta
      const currentPercentage = Math.min(
        100,
        Math.floor((elapsedMs / simulationTimeMs) * 100)
      );

      const isDownload = key.startsWith("download_");
      const currentEta = this.calculateRemainingMinutes(
        remainingMs,
        isDownload
      );

      // Create simulation object
      this.simulations.set(key, {
        state: parsedState,
        observers: new Set(),
        lastUpdateTime: 0, // Force immediate update
        totalSimulationTime: simulationTimeMs,
        onComplete,
      });

      // Ensure interval is running
      this.startTick();

      return {
        percentage: currentPercentage,
        eta: currentEta,
        status: SimulationStatus.SIMULATING,
      };
    } catch (error) {
      console.error(
        `Error initializing simulation from localStorage for ${key}:`,
        error
      );
      return null;
    }
  }

  /**
   * Get current simulation state without subscribing
   */
  getSimulationState(
    key: string,
    simulationTimeMs: number = DEFAULT_SIMULATION_TIME
  ): SimulationResult | null {
    // First check if we have this simulation in memory
    const existingSimulation = this.simulations.get(key);
    if (existingSimulation) {
      const now = Date.now();
      const remainingMs = Math.max(0, existingSimulation.state.endTime - now);
      const elapsedMs = existingSimulation.totalSimulationTime - remainingMs;

      if (remainingMs <= 0) {
        return {
          percentage: 100,
          eta: "0m",
          status: SimulationStatus.COMPLETED,
        };
      }

      const currentPercentage = Math.min(
        100,
        Math.floor((elapsedMs / existingSimulation.totalSimulationTime) * 100)
      );

      const isDownload = key.startsWith("download_");
      const currentEta = this.calculateRemainingMinutes(
        remainingMs,
        isDownload
      );

      return {
        percentage: currentPercentage,
        eta: currentEta,
        status: existingSimulation.state.status,
      };
    }

    // Try to get from localStorage
    try {
      const savedState = localStorage.getItem(key);
      if (!savedState) return null;

      const parsedState: SimulationState = JSON.parse(savedState);

      if (parsedState.status !== SimulationStatus.SIMULATING) {
        return {
          percentage: 0,
          eta: "0m",
          status: parsedState.status,
        };
      }

      const now = Date.now();
      const remainingMs = Math.max(0, parsedState.endTime - now);
      const elapsedMs = simulationTimeMs - remainingMs;

      if (remainingMs <= 0) {
        return {
          percentage: 100,
          eta: "0m",
          status: SimulationStatus.COMPLETED,
        };
      }

      const currentPercentage = Math.min(
        100,
        Math.floor((elapsedMs / simulationTimeMs) * 100)
      );

      const isDownload = key.startsWith("download_");
      const currentEta = this.calculateRemainingMinutes(
        remainingMs,
        isDownload
      );

      return {
        percentage: currentPercentage,
        eta: currentEta,
        status: SimulationStatus.SIMULATING,
      };
    } catch (error) {
      console.error(
        `Error getting simulation state from localStorage for ${key}:`,
        error
      );
      return null;
    }
  }
}

// Create a singleton instance
export const simulationManager = new SimulationManager();

/**
 * React hook for using the simulation manager with React components
 */
export const useSimulation = (
  itemId: string,
  simulationType: "download" | "processing",
  onComplete: () => void,
  simulationTimeMs: number = DEFAULT_SIMULATION_TIME
) => {
  const localStorageKey = `${simulationType}_${itemId}`;

  // Refs to store current values without causing rerenders
  const currentStateRef = useRef<SimulationResult>({
    percentage: 0,
    eta: "5m",
    status: SimulationStatus.IDLE,
  });

  // State to force rerender when needed (only at longer intervals)
  const [state, setState] = useState<SimulationResult>(currentStateRef.current);

  // Track the last time we updated state
  const lastStateUpdateRef = useRef<number>(0);

  // Create an observer function that updates refs and state when needed
  const observerCallback = useCallback((result: SimulationResult) => {
    // Always update the ref with latest values
    currentStateRef.current = result;

    // Only update state (causing rerender) at intervals
    const now = Date.now();
    if (now - lastStateUpdateRef.current >= MIN_STATE_UPDATE_INTERVAL) {
      lastStateUpdateRef.current = now;
      setState(result);
    }
  }, []);

  // Initialize the simulation
  useEffect(() => {
    // Initialize from localStorage if a simulation exists
    const initialState = simulationManager.initializeFromLocalStorage(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );

    if (initialState) {
      currentStateRef.current = initialState;
      setState(initialState);
    }

    // Subscribe to updates
    const unsubscribe = simulationManager.subscribe(
      localStorageKey,
      observerCallback
    );

    return () => {
      unsubscribe();
    };
  }, [
    itemId,
    simulationType,
    onComplete,
    simulationTimeMs,
    observerCallback,
    localStorageKey,
  ]);

  // Create API functions
  const startSimulation = useCallback(() => {
    const result = simulationManager.startSimulation(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );
    currentStateRef.current = result;
    setState(result);
  }, [localStorageKey, simulationTimeMs, onComplete]);

  const resetSimulation = useCallback(() => {
    const result = simulationManager.resetSimulation(localStorageKey);
    currentStateRef.current = result;
    setState(result);
  }, [localStorageKey]);

  const initializeFromLocalStorage = useCallback(() => {
    const result = simulationManager.initializeFromLocalStorage(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );

    if (result) {
      currentStateRef.current = result;
      setState(result);
      return true;
    }
    return false;
  }, [localStorageKey, simulationTimeMs, onComplete]);

  // Create a stable API object
  const api = useRef({
    get percentage() {
      return currentStateRef.current.percentage;
    },
    get eta() {
      return currentStateRef.current.eta;
    },
    get status() {
      return currentStateRef.current.status;
    },
    startSimulation,
    resetSimulation,
    initializeFromLocalStorage,
  }).current;

  // Return the API object and state for UI rendering
  return {
    // Values for UI rendering (causes rerenders)
    ...state,
    // Stable API methods (don't cause rerenders)
    startSimulation: api.startSimulation,
    resetSimulation: api.resetSimulation,
    initializeFromLocalStorage: api.initializeFromLocalStorage,
  };
};

// Compatibility wrappers
export const useDownloadSimulation = (
  clipId: string,
  onComplete: () => void,
  simulationTimeMs?: number
) => {
  return useSimulation(clipId, "download", onComplete, simulationTimeMs);
};

export const useProcessingSimulation = (
  videoId: string,
  onComplete: () => void,
  simulationTimeMs?: number
) => {
  return useSimulation(videoId, "processing", onComplete, simulationTimeMs);
};

// Get current state without hooks
export const getProcessingSimulationState = (
  clipId: string
): SimulationResult | null => {
  return simulationManager.getSimulationState(`processing_${clipId}`);
};

/**
 * Special no-render version of the hook for Editor component
 * Returns a ref object with simulation values that can be read without causing re-renders
 */
export const useNoRenderProcessingSimulation = (
  videoId: string,
  onComplete: () => void,
  simulationTimeMs: number = DEFAULT_SIMULATION_TIME
) => {
  const localStorageKey = `processing_${videoId}`;

  // Create a ref to store the current state
  const stateRef = useRef<SimulationResult>({
    percentage: 0,
    eta: "5m",
    status: SimulationStatus.IDLE,
  });

  // The observer just updates the ref without causing render
  const observerCallback = useCallback((result: SimulationResult) => {
    stateRef.current = result;
  }, []);

  // Init function directly sets up the simulation
  const init = useCallback(() => {
    // Initialize from localStorage if a simulation exists
    const initialState = simulationManager.initializeFromLocalStorage(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );

    if (initialState) {
      stateRef.current = initialState;
    }

    // Subscribe to updates
    return simulationManager.subscribe(localStorageKey, observerCallback);
  }, [localStorageKey, simulationTimeMs, onComplete, observerCallback]);

  // Set up initial subscription and cleanup
  useEffect(() => {
    const unsubscribe = init();
    return unsubscribe;
  }, [init]);

  // API functions
  const startSimulation = useCallback(() => {
    const result = simulationManager.startSimulation(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );
    stateRef.current = result;
  }, [localStorageKey, simulationTimeMs, onComplete]);

  const resetSimulation = useCallback(() => {
    const result = simulationManager.resetSimulation(localStorageKey);
    stateRef.current = result;
  }, [localStorageKey]);

  const checkExistingSimulation = useCallback(() => {
    return simulationManager.getSimulationState(localStorageKey) !== null;
  }, [localStorageKey]);

  const initializeFromLocalStorage = useCallback(() => {
    const result = simulationManager.initializeFromLocalStorage(
      localStorageKey,
      simulationTimeMs,
      onComplete
    );

    if (result) {
      stateRef.current = result;
      return true;
    }
    return false;
  }, [localStorageKey, simulationTimeMs, onComplete]);

  // Return a stable API object
  return useRef({
    get percentage() {
      return stateRef.current.percentage;
    },
    get eta() {
      return stateRef.current.eta;
    },
    get status() {
      return stateRef.current.status;
    },
    startSimulation,
    resetSimulation,
    initializeFromLocalStorage,
    checkExistingSimulation,
  }).current;
};

/**
 * Improved hook for managing multiple processing simulations at once with efficient updates
 * Reimplemented to use the new SimulationManager approach
 */
export const useMultipleProcessingSimulations = (
  videoIds: string[],
  videosById: Record<string, { rendering_status?: ProcessingStatus }>,
  onCompleteCallbacks?: { [id: string]: () => void },
  simulationTimes?: { [id: string]: number }
) => {
  // Store simulation data for each video ID
  const [simulationData, setSimulationData] = useState<{
    [id: string]: { percentage: number; eta: string; status: SimulationStatus };
  }>({});

  // References to track previous values to prevent unnecessary updates
  const prevDataRef = useRef<{
    [id: string]: { percentage: number; eta: string; status: SimulationStatus };
  }>({});

  // Track if we've initialized the simulations
  const initializedRef = useRef(false);

  // Create an observer callback that will handle updates for all video IDs
  const observerCallback = useCallback(
    (videoId: string, result: SimulationResult) => {
      // Use a functional update to avoid the dependency on simulationData
      setSimulationData((prevData) => {
        // Only update if the data has actually changed
        const prevResult = prevData[videoId];

        if (
          !prevResult ||
          prevResult.percentage !== result.percentage ||
          prevResult.eta !== result.eta ||
          prevResult.status !== result.status
        ) {
          // Update the data for this specific video
          prevDataRef.current = {
            ...prevDataRef.current,
            [videoId]: result,
          };

          // Return updated state
          return {
            ...prevData,
            [videoId]: result,
          };
        }

        // No change
        return prevData;
      });
    },
    []
  ); // No dependencies

  useEffect(() => {
    // Skip if already initialized
    if (initializedRef.current) return;
    initializedRef.current = true;

    // Store all unsubscribe functions
    const unsubscribeFunctions: (() => void)[] = [];

    // Initialize each simulation
    videoIds.forEach((videoId) => {
      const localStorageKey = `processing_${videoId}`;
      const simulationTimeMs =
        simulationTimes?.[videoId] ?? DEFAULT_SIMULATION_TIME;
      const onComplete = onCompleteCallbacks?.[videoId];

      // First check if this simulation already exists
      const existingState = simulationManager.getSimulationState(
        localStorageKey,
        simulationTimeMs
      );

      if (existingState) {
        // Add initial state to our data
        prevDataRef.current[videoId] = existingState;
        setSimulationData((prev) => ({
          ...prev,
          [videoId]: existingState,
        }));
      } else if (videoId) {
        // Start a new simulation if this video is in PROCESSING status
        try {
          const videoData = videosById[videoId];
          const isProcessing =
            videoData?.rendering_status === ProcessingStatus.PROCESSING;

          if (isProcessing) {
            const result = simulationManager.startSimulation(
              localStorageKey,
              simulationTimeMs,
              onComplete
            );

            prevDataRef.current[videoId] = result;
            setSimulationData((prev) => ({
              ...prev,
              [videoId]: result,
            }));
          }
        } catch (error) {
          console.error(
            `Error starting simulation for video ${videoId}:`,
            error
          );
        }
      }

      // Create a custom observer for this videoId
      const observer = (result: SimulationResult) => {
        observerCallback(videoId, result);
      };

      // Subscribe to updates for this video
      const unsubscribe = simulationManager.subscribe(
        localStorageKey,
        observer
      );

      // Add to our unsubscribe functions array
      unsubscribeFunctions.push(unsubscribe);
    });

    // Return proper cleanup function
    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe());
    };
  }, [videoIds, videosById, onCompleteCallbacks, simulationTimes]); // Include dependencies

  return simulationData;
};

// Export the old enum name for backward compatibility
export const DownloadStatus = SimulationStatus;
