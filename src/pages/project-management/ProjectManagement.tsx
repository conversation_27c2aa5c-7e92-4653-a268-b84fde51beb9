import {
  deleteVideo,
  selectVideos,
  fetchVideos,
} from "../../features/videos/videosSlice";
import { AppDispatch, RootState } from "../../store";
import { IVideo, ErrorResponse } from "../../types";
import {
  createStyles,
  LoadingOverlay,
  MantineTheme,
  Box,
  Text,
  Stack,
  Image,
} from "@mantine/core";
import {
  ProjectCard,
  ProjectCardAction,
} from "../../components/project-card/ProjectCard";
import { useCallback, useEffect, useMemo, useState, useRef } from "react";
import VideoUploadForm from "../../components/video-upload-form/VideoUploadForm";
import { useSelector } from "react-redux";
import { ModalConfig, ModalType } from "../../features/modal/types";
import { closeModal, openModal } from "../../features/modal/modalSlice";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import emptyProjectsImage from "../../assets/images/empty-projects.png";
import { useErrorHandler } from "../../hooks/useErrorHandler";

const useStyles = createStyles((theme: MantineTheme) => ({
  root: {
    minHeight: "100vh",
    backgroundColor: theme.other["surface-main-bg-color"],
  },
  contentContainer: {
    maxWidth: 1440,
    padding: "60px 128px 80px",
    margin: "0 auto",

    [`@media (max-width: 1727px)`]: {
      padding: "60px 128px 80px",
    },

    [`@media (max-width: 1279px)`]: {
      padding: "60px 64px 80px",
    },

    [`@media (max-width: 743px)`]: {
      padding: "60px 16px 64px",
    },
  },
  uploadVideoFormHolder: {
    marginBottom: 48,
    maxWidth: 916,
    marginLeft: "auto",
    marginRight: "auto",
  },
  projectsContainer: {
    [`@media (min-width: 1728px)`]: {
      maxWidth: "904px",
      margin: "0 auto",
    },
    [`@media (min-width: 1280px) and (max-width: 1727px)`]: {
      maxWidth: "952px",
      margin: "0 auto",
    },
    [`@media (min-width: 744px) and (max-width: 1279px)`]: {
      maxWidth: "616px",
      margin: "0 auto",
    },
    [`@media (max-width: 743px)`]: {
      maxWidth: "312px",
      margin: "0 auto",
    },
  },
  projectsGrid: {
    display: "grid",
    gap: 24,
    width: "100%",
    [`@media (min-width: 1728px)`]: {
      gridTemplateColumns: "repeat(4, 226px)",
    },
    [`@media (min-width: 1280px) and (max-width: 1727px)`]: {
      gridTemplateColumns: "repeat(4, 238px)",
    },
    [`@media (min-width: 744px) and (max-width: 1279px)`]: {
      gridTemplateColumns: "repeat(2, 296px)",
    },
    [`@media (max-width: 743px)`]: {
      gridTemplateColumns: "312px",
    },
  },
  projectsHeading: {
    color: theme.other["surface-text-color"],
    marginBottom: 24,
  },
  emptyState: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 64,
    marginTop: 80,

    [`@media (max-width: 1100px)`]: {
      flexDirection: "column",
      gap: 32,
    },
  },
  emptyStateImage: {
    maxWidth: 400,
    height: "auto",
  },
  emptyStateContent: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
  },
  emptyStateHeading: {
    fontSize: 64,
    fontWeight: 600,
    color: theme.other["surface-text-color"],
    marginBottom: 24,
    lineHeight: 1.2,

    [`@media (max-width: 1100px)`]: {
      fontSize: 48,
      textAlign: "center",
    },

    [`@media (max-width: 743px)`]: {
      fontSize: 36,
    },
  },
  emptyStateSubtext: {
    fontSize: 28,
    fontWeight: 600,
    color: theme.other["surface-text-color"],
    maxWidth: 500,
    lineHeight: 1.2,

    [`@media (max-width: 1100px)`]: {
      textAlign: "center",
      fontSize: 24,
    },

    [`@media (max-width: 743px)`]: {
      fontSize: 18,
    },
  },
}));

export const ProjectManagement: React.FC = () => {
  const videos: IVideo[] = useSelector(selectVideos);
  const { classes } = useStyles();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const videosStatus = useSelector(
    (state: RootState) => state.videos.loadingStatus
  );
  const { handleError } = useErrorHandler();
  const [isVideosLoaded, setIsVideosLoaded] = useState(false);
  const hasInitialFetchRun = useRef(false);

  // Fetch videos on component mount
  useEffect(() => {
    const fetchInitialVideos = async () => {
      if (hasInitialFetchRun.current) return;

      hasInitialFetchRun.current = true;

      // Fetch videos
      try {
        const videosResult = await dispatch(fetchVideos());
        if (
          !fetchVideos.fulfilled.match(videosResult) &&
          videosResult.payload
        ) {
          const errorPayload = videosResult.payload as {
            error: ErrorResponse;
            statusCode: number;
          };
          handleError(errorPayload.error, errorPayload.statusCode);
        }
        setIsVideosLoaded(true);
      } catch (error: any) {
        handleError(error.error, error.statusCode);
        setIsVideosLoaded(true); // Continue despite error
      }
    };

    fetchInitialVideos();
  }, []); // Empty dependency array - only run on mount

  const handleVideoAction = useCallback(
    (actionType: ProjectCardAction, videoId: string, videoTitle: string) => {
      console.warn(`Action ${actionType} for video ${videoId} was clicked`);
      switch (actionType) {
        case "delete":
          const modalConfig: ModalConfig = {
            type: ModalType.BASIC,
            title: "Delete project",
            subtitle:
              "Deleting the project will permanently remove all associated files, clips, and data. This action cannot be undone.",
            actions: [
              {
                label: "Cancel",
                variant: "secondary",
                fullWidth: true,
                buttonDisplay: "onlyText",
                onClick: () => dispatch(closeModal()),
              },
              {
                label: "Delete",
                variant: "destructive",
                fullWidth: true,
                buttonDisplay: "withIcon",
                iconName: "trash-can",
                onClick: async () => {
                  console.warn("Delete action clicked");

                  try {
                    const resultAction = await dispatch(deleteVideo(videoId));

                    if (
                      !deleteVideo.fulfilled.match(resultAction) &&
                      resultAction.payload
                    ) {
                      const errorPayload = resultAction.payload as {
                        error: ErrorResponse;
                        statusCode: number;
                      };
                      handleError(errorPayload.error, errorPayload.statusCode);
                    } else {
                      console.warn(`Video ${videoId} was deleted`);
                    }
                  } catch (errorResponse: any) {
                    handleError(errorResponse.error, errorResponse.statusCode);
                  }

                  dispatch(closeModal());
                },
              },
            ],
          };

          dispatch(openModal(modalConfig));
          break;
        case "view-clips":
          console.warn("View clips action clicked");

          navigate(`./${videoId}/clips`, {
            state: { projectTitle: videoTitle },
          });

          break;
        default:
          console.error("Unknown action type");
      }
    },
    [dispatch, navigate, handleError]
  );

  let content;

  if (videosStatus === "loading") {
    content = (
      <LoadingOverlay
        visible={true}
        loaderProps={{ size: "lg", variant: "dots" }}
      />
    );
  } else if (videosStatus === "succeeded") {
    content =
      videos.length > 0 ? (
        <div className={classes.projectsContainer}>
          <h3 className={classes.projectsHeading}>My projects</h3>
          <div className={classes.projectsGrid}>
            {videos.map((video: IVideo) => (
              <ProjectCard
                key={video.id}
                videoId={video.id}
                thumbnail={video.thumbnail_path}
                title={video.title}
                onActionClick={(action: ProjectCardAction) =>
                  handleVideoAction(action, video.id, video.title)
                }
                createdAt={video.created_at}
                processingStatus={video.processing_status}
              />
            ))}
          </div>
        </div>
      ) : (
        <Box className={classes.emptyState}>
          <Image
            src={emptyProjectsImage}
            alt="No projects"
            className={classes.emptyStateImage}
          />
          <Box className={classes.emptyStateContent}>
            <Text className={classes.emptyStateHeading}>
              Start your first project
            </Text>
            <Text className={classes.emptyStateSubtext}>
              Upload a video file or drop a YouTube video link
            </Text>
          </Box>
        </Box>
      );
  }

  return (
    <div className={classes.root}>
      <div className={classes.contentContainer}>
        <div className={classes.uploadVideoFormHolder}>
          <VideoUploadForm />
        </div>
        {content}
      </div>
    </div>
  );
};
