import {
  useState,
  useEffect,
  useRef,
  useMemo,
  useC<PERSON>back,
  useReducer,
} from "react";
import {
  createStyles,
  Box,
  Button,
  Text,
  LoadingOverlay,
  Transition,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useNavigate, useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { Player } from "@remotion/player";
import { DEFAULT_SUBTITLES_WORDS_PER_LINE } from "../../constants";
import { PresetConfig, IVideoSegment, ISubtitle } from "../../remotion/types";
import { fetchVideo, selectVideoById } from "../../features/videos/videosSlice";
import { RootState, AppDispatch, store } from "../../store";
import { ClipPreview } from "../../remotion/ClipPreview";
import { Timeline } from "../../components/timeline";
import { playerRef } from "../../remotion/components/timeline-refs";
import { DisplayTime } from "../../remotion/components/DisplayTime";
import {
  setCurrentFrame,
  setCurrentDuration,
  setCurrentFps,
  setOriginalDuration,
  resetState,
} from "../../remotion/imperative-state";
import React, { FC } from "react";
import { TimelineZoomContext } from "../../remotion/context/timeline-zoom";
import { TimelineWidthProvider } from "../../remotion/context/TimelineWidthProvider";
import { TimelineControls } from "@/components/timeline/timeline-controls";
import {
  detectAspectRatio,
  isValidCropBox,
  totalDurationInFrames,
  getClipDurationInSeconds,
} from "../../utils";
import Transcript from "../../components/editor/Transcript";
import {
  ClipConfigProvider,
  useClipConfigAPI,
} from "../../features/clipConfig/ClipConfigApi";
import EditorSettings, {
  GetCurrentCropBoxFn,
} from "../../components/editor/EditorSettings";
import { CropButton } from "../../components/editor/CropButton";
import Header from "../../components/header/Header";
import { VFNotification } from "../../components/ui/Notification";
import { ProcessingStatus, IVideoClip } from "../../types";
import {
  useNoRenderProcessingSimulation,
  getProcessingSimulationState,
} from "../../hooks/useDownloadSimulation";
import {
  fetchClip,
  selectClipsByVideoId,
  updateClipState,
} from "../../features/clips/clipsSlice";
import { VFButton } from "../../components/ui/Button";

// Extend the Video type to include our virtual crop box
declare module "../../features/videos/videosSlice" {
  interface Video {
    virtualCropBox?: {
      x1: number;
      y1: number;
      x2: number;
      y2: number;
    } | null;
  }
}

interface StylesProps {
  leftVisible: boolean;
  rightVisible: boolean;
}

const useStyles = createStyles(
  (theme, { leftVisible, rightVisible }: StylesProps) => ({
    editorWrapper: {
      display: "flex",
      flexDirection: "column",
      width: "100%",
      height: "100vh",
      backgroundColor: theme.other["surface-main-bg-color"],
    },

    processingLoaderContainer: {
      display: "block",
      margin: "16px",
    },

    editorContainer: {
      display: "flex",
      position: "relative",
      width: "100%",
      flex: 1,
      backgroundColor: theme.other["surface-bg-color"],
      overflow: "auto",
      justifyContent: "space-between",

      "@media (max-width: 744px)": {
        overflow: "auto",
      },
    },

    leftSection: {
      backgroundColor: theme.other["surface-main-bg-color"],
      height: "100%",
      transition: "transform 0.3s ease",
      position: "relative",
      zIndex: 1,
      overflowY: "auto",
      minWidth: "452px",

      "@media (max-width: 744px)": {
        position: "absolute",
        left: 0,
        top: "34px", // Button height + 6px offset
        transform: leftVisible ? "translateX(0)" : "translateX(-100%)",
        width: "100%",
        minWidth: "unset",
        maxWidth: "452px",
      },

      "@media (max-width: 440px)": {
        width: "100%",
        minWidth: "unset",
        maxWidth: "100%",
      },
    },

    centerSection: {
      height: "100%",
      transition: "width 0.3s ease",
      display: "block",

      "@media (max-width: 744px)": {
        width: "100% !important", // Override any inline styles
        maxWidth: "100%",
      },
    },

    rightSection: {
      height: "100%",
      transition: "transform 0.3s ease",
      position: "relative",
      zIndex: 1,

      "@media (max-width: 744px)": {
        position: "absolute",
        right: 0,
        top: "34px", // Button height + 6px offset
        transform: rightVisible ? "translateX(0)" : "translateX(100%)",
        width: "100%",
        minWidth: "unset",
        maxWidth: "452px",
      },

      "@media (max-width: 440px)": {
        width: "100%",
        minWidth: "unset",
        maxWidth: "100%",
      },
    },

    bottomSection: {
      width: "100%",
      borderTop: `1px solid ${theme.other["surface-border-color"]}`,
      display: "flex",
      flexDirection: "column",
      position: "relative", // Ensure proper positioning
      overflow: "visible", // Allow the timeline to be visible
      zIndex: 10, // Ensure it's above other elements
    },

    toggleButton: {
      position: "absolute",
      top: "4px",
      backgroundColor: theme.other["surface-bg-color"],
      color: theme.other["surface-text-color"],
      border: `1px solid ${theme.other["surface-border-color"]}`,
      zIndex: 3, // Higher z-index than sections

      "&:hover": {
        backgroundColor: theme.other["btn-secondary-hover-bg-color"],
      },
    },

    leftToggleButton: {
      left: "4px",
    },

    rightToggleButton: {
      right: "4px",
    },

    // Settings styles
    settingsContainer: {
      padding: "16px",
      position: "relative",
      zIndex: 1,
    },

    settingSection: {
      marginBottom: "24px",
    },

    sectionHeader: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "8px",
    },

    sectionTitle: {
      color: "#FFFFFF",
      margin: 0,
    },

    settingRow: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: "12px",
    },

    settingLabel: {
      color: theme.other["surface-subtext-color"],
      display: "block",
    },

    colorRow: {
      display: "flex",
      alignItems: "center",
      gap: "8px",
    },

    typographyControls: {
      display: "flex",
      flexDirection: "column",
      gap: "12px",
    },

    alignmentControls: {
      display: "flex",
      gap: "8px",
    },

    fontSizeGroup: {
      display: "flex",
      alignItems: "center",
      gap: "8px",
    },

    colorPickerWrapper: {
      position: "relative",
      zIndex: 5,
    },
  })
);

interface EditorProps {
  // You can add props as needed
}

const Editor: FC<EditorProps> = React.memo(() => {
  const [leftVisible, setLeftVisible] = useState(false);
  const [rightVisible, setRightVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  // Replace state with ref to prevent unnecessary rerenders
  const [videoSubtitles, setVideoSubtitles] = useState<ISubtitle[]>([]);
  const [subtitlesFetched, setSubtitlesFetched] = useState(false);
  const [cropMode, setCropMode] = useState(false);

  // Subtitle settings state
  const aspectRatioRef = useRef<string | null>(null);
  const [aspectRatio, setAspectRatio] = useState<string | null>(null);
  // Re-add captionsEnabled state for initialization
  const [captionsEnabled, setCaptionsEnabled] = useState(true);

  // Extract route parameters
  const { projectId, clipId } = useParams();
  const dispatch = useDispatch<AppDispatch>();

  const navigate = useNavigate();

  // Get presets from Redux store
  const presets = useSelector((state: RootState) => state.presets.presets);
  const userData = useSelector((state: RootState) => state.auth.userData);

  // Get the default preset configuration from Redux store
  const defaultPresetConfig = useMemo(() => {
    // Fallback to system presets as default
    if (presets?.system?.length) {
      // Directly use the first system preset
      return presets.system[0].config;
    }

    // Return a minimal default preset config if API data isn't available yet
    return {
      preset_name: "Default",
      words_per_line: DEFAULT_SUBTITLES_WORDS_PER_LINE,
      size: 40,
      font: {
        family: "Inter",
        sub_family: "normal",
      },
      color: "#FFFFFF",
      stroke: {
        color: "#000000",
        weight: 1,
        opacity: 100,
      },
      shadow: null,
      highlight_color: null,
      current_word_color: "#FFE100",
      amplified_word_color: null,
      current_word_bg_color: "#000000",
    };
  }, [presets]);

  const [subtitleConfig, setSubtitleConfig] =
    useState<PresetConfig>(defaultPresetConfig);

  // Default to 3 words per line as a reasonable default

  // Track whether aspect ratio was manually set
  const aspectRatioManuallySet = useRef(false);

  // Track previous aspect ratio to prevent unnecessary updates
  const previousAspectRatioRef = useRef<string | null>(null);

  // Store original crop boxes to restore when switching back
  const originalCropBoxesRef = useRef<{ [segmentIndex: number]: any }>({});

  // Helper for force rerender
  const [, updateState] = useState({});
  // const forceUpdate = useCallback(() => updateState({}), []);

  // Get video data
  const video = useSelector((state: RootState) =>
    projectId ? selectVideoById(state, projectId) : null
  );

  // Get clip data from Redux store instead of local state
  const clipsForVideo = useSelector((state: RootState) =>
    projectId ? selectClipsByVideoId(state, projectId) : []
  );

  const clipData = useMemo(() => {
    if (!clipId || !clipsForVideo.length) return null;
    return clipsForVideo.find(
      (clip) => clip.id === clipId
    ) as IVideoClip | null;
  }, [clipId, clipsForVideo]);

  // Create a setClipData function that updates Redux
  const setClipData = useCallback(
    (newClipData: IVideoClip | null) => {
      if (!newClipData || !projectId) return;

      dispatch(
        updateClipState({
          videoId: projectId,
          data: newClipData,
        })
      );
    },
    [dispatch, projectId]
  );

  // Add a ref to track initialization
  const hasInitializedRef = useRef(false);

  // Helper function to calculate duration in frames based on editor mode
  const getVideoFramesDuration = useCallback((): number => {
    const fps = video?.media_metadata?.fps || 30;

    if (clipData) {
      // For clip mode: use totalDurationInFrames with clip segments and video fps
      return totalDurationInFrames(clipData.segments, fps);
    } else {
      // For video mode: use video duration in seconds * video fps
      return Math.round((video?.media_metadata?.duration_seconds || 0) * fps);
    }
  }, [clipData, video]);

  // Helper function to get segments based on editor mode
  const getVideoSegments = useCallback((): IVideoSegment[] => {
    if (clipData) {
      // For clip mode: use clip segments directly
      return clipData.segments;
    }
    // Return an empty array if no clip data
    return [];
  }, [clipData, video]);

  // Individual player state values for better performance (prevent unnecessary rerenders)
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPlayerFrame, setCurrentPlayerFrame] = useState(0);
  const [isMuted, setIsMuted] = useState(false);

  // Use refs to track player state without causing rerenders
  const playerStateRef = useRef({
    isPlaying: false,
    frame: 0,
    isMuted: false,
  });

  // Create a memoized playerState object for components that need the combined state
  const playerStateForProps = useMemo(
    () => ({
      isPlaying,
      frame: currentPlayerFrame,
      isMuted,
    }),
    [isPlaying, currentPlayerFrame, isMuted]
  );

  // Create a memoized setter function to update player state
  const setPlayerStateForProps = useCallback(
    (
      newState:
        | typeof playerStateForProps
        | ((prev: typeof playerStateForProps) => typeof playerStateForProps)
    ) => {
      const updatedState =
        typeof newState === "function"
          ? newState(playerStateForProps)
          : newState;

      // Update the ref first
      playerStateRef.current = updatedState;

      // Only update React state if the value actually changed
      // to prevent unnecessary rerenders
      if (playerStateRef.current.isPlaying !== isPlaying) {
        setIsPlaying(playerStateRef.current.isPlaying);
      }

      if (playerStateRef.current.frame !== currentPlayerFrame) {
        setCurrentPlayerFrame(playerStateRef.current.frame);
      }

      if (playerStateRef.current.isMuted !== isMuted) {
        setIsMuted(playerStateRef.current.isMuted);
      }
    },
    [] // Remove dependencies to prevent recreation
  );

  // Create an internal component for initialization
  const ClipConfigInitializer = () => {
    const { onInitialConfigChange, restoreHistoryFromStorage } =
      useClipConfigAPI();
    useEffect(() => {
      // Only initialize once per load
      if (hasInitializedRef.current) return;

      // For clip mode
      if (isDataReady && clipData) {
        try {
          // First try to restore history from localStorage
          const historyRestored = false; // this is working, leave it for now
          // const historyRestored = clipId
          //   ? restoreHistoryFromStorage(clipId)
          //   : false;

          // If no history was restored, initialize with the current data
          if (!historyRestored) {
            // Use the existing subtitles config directly, don't overwrite it
            const subtitlesConfig = {
              ...clipData.subtitles,
            };

            onInitialConfigChange(
              {
                subtitles: {
                  items: captionsEnabled ? subtitlesConfig.items : [],
                  config: subtitlesConfig.config,
                  position: subtitlesConfig.position,
                },
                segments: clipData.segments,
              },
              clipId
            );
          }

          hasInitializedRef.current = true;
          // ClipConfig initialized with clip data successfully
        } catch (error) {
          // Error initializing clip config
        }
      }
    }, [
      isDataReady,
      videoSubtitles,
      clipData,
      onInitialConfigChange,
      getVideoSegments,
      captionsEnabled,
      clipId,
      restoreHistoryFromStorage,
    ]);
    return null;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        if (loading === false) {
          return; // Skip if we've already loaded data
        }

        setLoading(true);

        // Starting data fetch process...
        // Reset imperative state whenever we load a new video or clip
        resetState();
        // Reset initialization flag on data reload
        hasInitializedRef.current = false;

        if (projectId) {
          // Only fetch video if not already loaded or mismatched
          if (!video || video.id !== projectId) {
            // Fetching video with ID
            await dispatch(fetchVideo(projectId));
          }

          // Next, fetch the clip data if needed
          if (clipId && !subtitlesFetched) {
            // Fetching clip with ID (first and only time)
            setSubtitlesFetched(true); // Mark as fetched before the actual request

            try {
              await dispatch(fetchClip(clipId) as any);

              if (clipData) {
                // Calculate the duration from the segments
                const calculatedDuration = totalDurationInFrames(
                  clipData.segments,
                  video!.media_metadata.fps
                );

                // Setting initial duration
                // Set both current and original duration
                setCurrentDuration(calculatedDuration);
                setOriginalDuration(calculatedDuration); // Store original duration that won't change
                setCurrentFps(video!.media_metadata.fps);

                // Then set frame to the beginning
                setCurrentFrame(
                  Math.round(
                    clipData.segments[0].start * video!.media_metadata.fps
                  )
                );
              }
            } catch (error) {
              // Exception fetching clip
            }
          } else {
            // Clip already fetched, skipping fetch
          }
        }
      } catch (error) {
        // General error
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Cleanup function
    return () => {
      // Editor component unmounting, cleaning up
    };
  }, [projectId, clipId, dispatch, subtitleConfig]);

  const is1728 = useMediaQuery("(min-width: 1728px)");
  const is1280 = useMediaQuery("(min-width: 1280px) and (max-width: 1727px)");
  const is744 = useMediaQuery("(min-width: 744px)");
  const is440 = useMediaQuery("(max-width: 743px)");

  const { classes, cx } = useStyles({ leftVisible, rightVisible });

  // Replace the existing section width getter functions with memoized versions
  const leftSectionWidth = useMemo(() => {
    if (is1728) return 475;
    if (is1280) return 475;
    if (is744) return 475;
    if (is440) return Math.min(475, window.innerWidth);

    return Math.min(475, window.innerWidth); // Default
  }, [is1728, is1280, is744, is440]);

  const centerSectionWidth = useMemo(() => {
    if (is1728) return 746;
    if (is1280) return 554;
    if (is744) return 696;
    if (is440) return Math.min(394, window.innerWidth);
    return Math.min(394, window.innerWidth); // Default
  }, [is1728, is1280, is744, is440]);

  const rightSectionWidth = useMemo(() => {
    if (is1728) return 506;
    if (is1280) return 274;
    if (is744) return 452;
    if (is440) return Math.min(394, window.innerWidth);
    return Math.min(394, window.innerWidth); // Default
  }, [is1728, is1280, is744, is440]);

  // Reset visibility when screen size changes
  useEffect(() => {
    // On larger screens (744px and above), sections are always visible
    if (is744) {
      setLeftVisible(true);
      setRightVisible(true);
    } else {
      // On smaller screens, hide by default
      setLeftVisible(false);
      setRightVisible(false);
    }
  }, [is744]);

  // Add a data validation function to check if we have required data
  const hasValidData = useCallback(() => {
    if (!video || !video.file_path || !video.media_metadata) {
      // Missing video data
      return false;
    }

    // Only require clip data in clip mode
    if (!clipData || !clipData.segments || clipData.segments.length === 0) {
      // Missing clip data
      return false;
    }

    return true;
  }, [video, clipData]);

  // Single request ref tracker
  const subtitlesFetchedRef = useRef(false);

  // Indicate loading state if still fetching data or if data isn't valid
  const isDataReady = !loading && hasValidData();

  // Add useEffect to listen for mute changes from the player
  useEffect(() => {
    if (playerRef.current) {
      const onMuteChange = () => {
        const newMuted = playerRef.current?.isMuted() ?? false;
        // Update ref first
        playerStateRef.current.isMuted = newMuted;
        // Only update state if changed (for UI updates)
        if (newMuted !== isMuted) {
          setIsMuted(newMuted);
        }
      };

      playerRef.current.addEventListener("mutechange", onMuteChange);
      return () => {
        playerRef.current?.removeEventListener("mutechange", onMuteChange);
      };
    }
  }, [playerRef, isMuted]);

  // Add useEffect to listen for play state changes from the player
  useEffect(() => {
    if (playerRef.current) {
      const onPlayingChange = () => {
        const newIsPlaying = playerRef.current?.isPlaying() ?? false;
        // Update ref first
        playerStateRef.current.isPlaying = newIsPlaying;
        // Only update state if changed (for UI updates)
        if (newIsPlaying !== isPlaying) {
          setIsPlaying(newIsPlaying);
        }
      };

      playerRef.current.addEventListener("play", onPlayingChange);
      playerRef.current.addEventListener("pause", onPlayingChange);

      return () => {
        playerRef.current?.removeEventListener("play", onPlayingChange);
        playerRef.current?.removeEventListener("pause", onPlayingChange);
      };
    }
  }, [playerRef, isPlaying, currentPlayerFrame]);

  // Update frames duration with cleaner code
  useEffect(() => {
    if (clipData && video?.media_metadata?.fps) {
      // Calculate and set the current duration based on mode
      setCurrentDuration(getVideoFramesDuration());

      // Set FPS for both modes
      setCurrentFps(video.media_metadata.fps);
    }
  }, [clipData, video, getVideoFramesDuration]);

  // Handle aspect ratio change
  const handleAspectRatioChange = useCallback(
    (newAspectRatio: string | null) => {
      if (!newAspectRatio || newAspectRatio === aspectRatioRef.current) return;

      // Store previous value for potential rollback
      previousAspectRatioRef.current = aspectRatioRef.current;

      // Mark as manually set to prevent auto-resetting
      aspectRatioManuallySet.current = true;

      // Update the ref immediately (this doesn't trigger a re-render)
      aspectRatioRef.current = newAspectRatio;

      // Update the state asynchronously to minimize the impact of the rerender
      // This deferred update allows any edit operations to complete first
      setTimeout(() => {
        setAspectRatio(newAspectRatio);
      }, 0);
    },
    []
  );

  // Use the ref value for rendering decisions but keep state synced for components that need it
  useEffect(() => {
    // Sync state with ref if they get out of sync
    if (
      aspectRatio !== aspectRatioRef.current &&
      aspectRatioRef.current !== null
    ) {
      setAspectRatio(aspectRatioRef.current);
    }
  }, [aspectRatio]);

  // Make sure to update the ref when the state is set through other means
  useEffect(() => {
    if (aspectRatio !== null && aspectRatio !== aspectRatioRef.current) {
      aspectRatioRef.current = aspectRatio;
    }
  }, [aspectRatio]);

  // 1. Create a ref for EditorSettings
  const editorSettingsRef = React.useRef<{
    getCurrentCropBox: GetCurrentCropBoxFn;
  }>(null);

  // In the OptimizedPlayer useMemo and crop mode logic, use the crop box from editorSettingsRef
  const currentCropBox = editorSettingsRef.current?.getCurrentCropBox({
    aspectRatio: aspectRatioRef.current || "16:9", // Use ref value instead of state
    segmentIndex: currentPlayerFrame,
    sourceVideoMetadata: video?.media_metadata || { width: 0, height: 0 },
    segments: clipData?.segments,
  });

  // --- PLAYER BUFFERING/LOADED TRACKING VIA renderPoster ---
  const renderPoster = useCallback(
    ({ isBuffering }: { isBuffering: boolean }) => {
      if (isPlayerBufferingRef.current !== isBuffering) {
        isPlayerBufferingRef.current = isBuffering;
        recalculateLoaderVisibility();
      }
      // Mark player as ready the first time buffering is false
      if (!isBuffering && !isPlayerReadyRef.current) {
        isPlayerReadyRef.current = true;
        recalculateLoaderVisibility();
      }
      // Optionally, show a spinner overlay (not strictly needed, as LoadingOverlay is global)
      return null;
    },
    []
  );

  const OptimizedPlayer = useMemo(() => {
    if (!video) return null;
    if (!clipData || !clipData.segments) return null;
    if (!aspectRatioRef.current) {
      return (
        <Box
          style={{
            width: "100%",
            height: "400px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: "#1A1A1A",
          }}
        >
          <Text color="#fafafa99">Detecting aspect ratio...</Text>
        </Box>
      );
    }
    if (!editorSettingsRef.current) {
      return (
        <Box
          style={{
            width: "100%",
            height: "400px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: "#1A1A1A",
          }}
        >
          <Text color="#fafafa99">Preparing editor...</Text>
        </Box>
      );
    }
    let segments: any[] = [];
    if (clipData?.segments) {
      segments = clipData.segments.map((segment, idx) => {
        let segCropBox = editorSettingsRef.current?.getCurrentCropBox({
          aspectRatio: aspectRatioRef.current || "16:9",
          segmentIndex: idx,
          sourceVideoMetadata: video?.media_metadata || { width: 0, height: 0 },
          segments: clipData?.segments,
        });
        if (!isValidCropBox(segCropBox)) {
          segCropBox = segment.crop_box || {
            x1: 0,
            y1: 0,
            x2: video.media_metadata.width,
            y2: video.media_metadata.height,
          };
        }
        return {
          ...segment,
          crop_box: segCropBox,
        };
      });
    }
    if (
      !segments ||
      segments.length === 0 ||
      segments.some((s) => !isValidCropBox(s.crop_box))
    ) {
      return (
        <Box
          style={{
            width: "100%",
            height: "400px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            background: "#1A1A1A",
          }}
        >
          <Text color="#fafafa99">Preparing video player...</Text>
        </Box>
      );
    }
    const playerProps = {
      sourceVideoUrl: video.file_path,
      dimensions: {
        width: centerSectionWidth,
        height: 0,
      },
      sourceVideoMetadata: video.media_metadata,
      isBuffering: false,
      cropMode,
      useImperativeSegmentIndex: true,
    };
    const totalFrames = getVideoFramesDuration();
    return (
      <Player
        style={{
          width: "100%",
          background: "transparent",
          maxHeight: "100%",
        }}
        ref={playerRef}
        component={ClipPreview}
        inputProps={playerProps}
        durationInFrames={totalFrames}
        compositionWidth={centerSectionWidth}
        compositionHeight={Math.round(
          centerSectionWidth *
            (cropMode
              ? 9 / 16
              : aspectRatioRef.current === "9:16"
              ? 16 / 9
              : aspectRatioRef.current === "1:1"
              ? 1
              : 9 / 16)
        )}
        fps={video.media_metadata.fps}
        bufferStateDelayInMilliseconds={200}
        showPosterWhenBuffering
        showPosterWhenUnplayed
        showPosterWhenPaused
        renderPoster={renderPoster}
      />
    );
  }, [
    video,
    clipData?.segments,
    clipData?.subtitles,
    videoSubtitles,
    centerSectionWidth,
    defaultPresetConfig,
    getVideoFramesDuration,
    cropMode,
    currentCropBox,
    renderPoster,
  ]);

  useEffect(() => {
    if (video?.media_metadata && !aspectRatioManuallySet.current) {
      // For clip mode, check for crop box first
      if (clipData?.segments && clipData.segments.length > 0) {
        const segmentWithCropBox = clipData.segments.find(
          (segment) =>
            segment.crop_box !== null && segment.crop_box !== undefined
        );
        if (segmentWithCropBox?.crop_box) {
          const cropBox = segmentWithCropBox.crop_box;
          const cropBoxWidth = cropBox.x2 - cropBox.x1;
          const cropBoxHeight = cropBox.y2 - cropBox.y1;
          const detectedRatio = detectAspectRatio(cropBoxWidth, cropBoxHeight);
          // Setting aspect ratio from clip crop box

          // Update ref first
          aspectRatioRef.current = detectedRatio;
          // Then update state
          setAspectRatio(detectedRatio);

          return;
        }
      }
      // If no crop box or not in clip mode, use video dimensions
      const videoWidth = video.media_metadata.width;
      const videoHeight = video.media_metadata.height;
      const detectedRatio = detectAspectRatio(videoWidth, videoHeight);
      // Setting aspect ratio from video dimensions

      // Update ref first
      aspectRatioRef.current = detectedRatio;
      // Then update state
      setAspectRatio(detectedRatio);
      return;
    }
    // Fallback: if aspectRatio is still not set, set to '16:9' and log a warning
    if (!aspectRatioRef.current) {
      // Could not detect aspect ratio, falling back to 16:9

      // Update ref first
      aspectRatioRef.current = "16:9";
      // Then update state
      setAspectRatio("16:9");
    }
  }, [video, clipData]);

  // Restore the effect to exit crop mode on outside click
  useEffect(() => {
    if (!cropMode) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        centerSectionRef.current &&
        !centerSectionRef.current.contains(event.target as Node)
      ) {
        setCropMode(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [cropMode]);

  // Add this near the top of the Editor component, before any use of centerSectionRef
  const centerSectionRef = useRef<HTMLDivElement>(null);

  // Add a noop function for the onComplete callback
  const handleProcessingComplete = useCallback(() => {
    // No action needed when processing completes
    // The UI will be updated through Redux state changes
  }, []);

  // Calculate clip duration in seconds for simulation estimation
  const clipDurationSeconds = useMemo(() => {
    if (clipData && clipData.segments) {
      return getClipDurationInSeconds(clipData.segments);
    }
    return 0;
  }, [clipData]);

  // Estimate processing time: 1s video = 3.13s processing, add 20% buffer
  const estimatedProcessingTimeMs = useMemo(() => {
    if (
      typeof clipDurationSeconds === "number" &&
      !isNaN(clipDurationSeconds) &&
      clipDurationSeconds > 0
    ) {
      // New formula: duration * 3.13 * 1.2 (20% buffer)
      const renderTime = clipDurationSeconds * 3.13;
      const bufferedTime = renderTime * 1.2;

      return Math.ceil(bufferedTime * 1000);
    }
    // Fallback to 5 minutes
    return 5 * 60 * 1000;
  }, [clipDurationSeconds]);

  // Use the processing simulation hook with the clip ID
  const {
    percentage: processingPercentage,
    eta: processingEta,
    startSimulation: startProcessingSimulation,
    initializeFromLocalStorage: initializeProcessingFromLocalStorage,
  } = useNoRenderProcessingSimulation(
    clipId || "",
    handleProcessingComplete,
    estimatedProcessingTimeMs
  );

  // Initialize simulation when the component mounts and clipData is available
  useEffect(() => {
    if (clipData?.rendering_status === ProcessingStatus.PROCESSING && clipId) {
      // First check if there's already a simulation state
      const existingState = getProcessingSimulationState(clipId);

      if (existingState) {
        // We already have a simulation running, no need to initialize
        // Found existing processing simulation for clip
        return;
      }

      // Try to initialize from localStorage first
      const hasExistingSimulation = initializeProcessingFromLocalStorage();

      // If no existing simulation, start a new one
      if (!hasExistingSimulation && estimatedProcessingTimeMs > 0) {
        // Starting new processing simulation for clip
        startProcessingSimulation();
      }
    }
  }, [
    clipData,
    clipId,
    initializeProcessingFromLocalStorage,
    startProcessingSimulation,
    estimatedProcessingTimeMs,
  ]);

  // Create a separate notification component to isolate re-renders
  const ProcessingNotification = ({
    clipId,
    simulationTimeMs,
  }: {
    clipId: string | undefined;
    simulationTimeMs: number;
  }) => {
    // Use a separate instance of the processing hook to avoid re-renders of the parent
    const simulation = useNoRenderProcessingSimulation(
      clipId || "",
      () => {}, // No-op complete handler
      simulationTimeMs
    );

    const [eta, setEta] = useState(simulation.eta || "5 min");

    // Update the ETA periodically without affecting the parent component
    useEffect(() => {
      if (!clipId) return;

      // Set initial value
      setEta(simulation.eta || "5 min");

      const intervalId = setInterval(() => {
        // Get the current value directly from the simulation
        setEta(simulation.eta || "5 min");
      }, 5000); // Update notification every 5 seconds

      return () => clearInterval(intervalId);
    }, [clipId, simulation]);

    // Render nothing if no clipId or not in processing status
    if (!clipId) return null;

    return (
      <Box className={classes.processingLoaderContainer}>
        <VFNotification
          message={`Preparing download of clip (ETA ${eta})`}
          variant="success"
        />
      </Box>
    );
  };

  // Memoize the notification component to prevent re-renders
  const MemoizedProcessingNotification = React.memo(ProcessingNotification);

  // --- GLOBAL LOADER STATE REFS ---
  const isVideoLoadedRef = useRef(false);
  const isClipLoadedRef = useRef(false);
  const isPlayerReadyRef = useRef(false);
  const isPlayerBufferingRef = useRef(true); // Assume buffering until proven otherwise
  const shouldShowLoaderRef = useRef(true);
  const [_, forceLoaderUpdate] = useReducer((x) => x + 1, 0);
  const [loaderError, setLoaderError] = useState<string | null>(null);

  // --- LOADER VISIBILITY CALCULATION ---
  function recalculateLoaderVisibility() {
    const shouldShow =
      !isVideoLoadedRef.current ||
      !isClipLoadedRef.current ||
      !isPlayerReadyRef.current ||
      isPlayerBufferingRef.current;
    if (shouldShowLoaderRef.current !== shouldShow) {
      shouldShowLoaderRef.current = shouldShow;
      forceLoaderUpdate();
    }
  }

  // --- VIDEO/CLIP FETCH TRACKING ---
  useEffect(() => {
    // Video loaded if valid
    const videoLoaded = !!video && !!video.file_path && !!video.media_metadata;
    if (isVideoLoadedRef.current !== videoLoaded) {
      isVideoLoadedRef.current = videoLoaded;
      recalculateLoaderVisibility();
    }
    // Clip loaded if valid
    const clipLoaded =
      !!clipData && !!clipData.segments && clipData.segments.length > 0;
    if (isClipLoadedRef.current !== clipLoaded) {
      isClipLoadedRef.current = clipLoaded;
      recalculateLoaderVisibility();
    }
  }, [video, clipData]);

  // --- RESET LOADER STATE ON PROJECT/CLIP CHANGE ---
  useEffect(() => {
    isVideoLoadedRef.current = false;
    isClipLoadedRef.current = false;
    isPlayerReadyRef.current = false;
    isPlayerBufferingRef.current = true;
    shouldShowLoaderRef.current = true;
    setLoaderError(null);
    forceLoaderUpdate();
  }, [projectId, clipId]);

  // --- PLAYER HOVER AND CROP BUTTON MANAGEMENT ---
  const isPlayerHoveredRef = useRef(false);
  const cropButtonVisibilityRef = useRef<HTMLDivElement>(null);

  // Update crop button visibility without re-rendering the component
  const updateCropButtonVisibility = useCallback(() => {
    if (!cropButtonVisibilityRef.current || !video?.media_metadata) return;

    const shouldShowCropButton =
      isPlayerHoveredRef.current &&
      aspectRatioRef.current !==
        detectAspectRatio(
          video.media_metadata.width,
          video.media_metadata.height
        );

    // Update DOM directly instead of using state
    cropButtonVisibilityRef.current.style.display = shouldShowCropButton
      ? "block"
      : "none";
  }, [video?.media_metadata]);

  // Setup mutation observer to update crop button when necessary
  useEffect(() => {
    if (!video?.media_metadata) return;

    // Update visibility initially
    updateCropButtonVisibility();

    // Setup resize observer to update when dimensions change
    const resizeObserver = new ResizeObserver(() => {
      updateCropButtonVisibility();
    });

    if (centerSectionRef.current) {
      resizeObserver.observe(centerSectionRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [video?.media_metadata, updateCropButtonVisibility]);

  // --- NEW STATE FOR NOTIFICATION ---
  const [showNotification, setShowNotification] = useState(false);
  const notificationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // --- SHOW NOTIFICATION AFTER LOADER DISAPPEARS ---
  useEffect(() => {
    // Only show notification when data is ready AND loader is hidden
    if (isDataReady && !shouldShowLoaderRef.current) {
      setShowNotification(true);
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
      }
      notificationTimeoutRef.current = setTimeout(() => {
        setShowNotification(false);
      }, 10000);
    } else {
      setShowNotification(false);
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
        notificationTimeoutRef.current = null;
      }
    }
    // Cleanup on unmount
    return () => {
      if (notificationTimeoutRef.current) {
        clearTimeout(notificationTimeoutRef.current);
        notificationTimeoutRef.current = null;
      }
    };
  }, [isDataReady, _]); // Add dependency on _ to detect changes in shouldShowLoaderRef.current

  return (
    <Box className={classes.editorWrapper}>
      <ClipConfigProvider>
        <ClipConfigInitializer />
        <Header />
        {/* --- GLOBAL LOADER --- */}
        <LoadingOverlay visible={shouldShowLoaderRef.current} zIndex={1000} />
        {loaderError && (
          <Box
            style={{
              position: "absolute",
              top: 80,
              left: 0,
              right: 0,
              zIndex: 1100,
              textAlign: "center",
            }}
          >
            <Text color="red" weight={700}>
              {loaderError}
            </Text>
          </Box>
        )}
        {clipData?.rendering_status === ProcessingStatus.PROCESSING && (
          <MemoizedProcessingNotification
            clipId={clipId}
            simulationTimeMs={estimatedProcessingTimeMs}
          />
        )}

        <Box className={classes.editorContainer}>
          {!is744 && (
            <Button
              className={cx(classes.toggleButton, classes.leftToggleButton)}
              onClick={() => setLeftVisible(!leftVisible)}
              compact
            >
              {leftVisible ? "Hide settings" : "Show settings"}
            </Button>
          )}
          {!is744 && (
            <Button
              className={cx(classes.toggleButton, classes.rightToggleButton)}
              onClick={() => setRightVisible(!rightVisible)}
              compact
            >
              {rightVisible ? "Hide transcript" : "Show transcript"}
            </Button>
          )}
          <Box
            className={classes.leftSection}
            style={{
              width: `${leftSectionWidth}px`,
              display: is744 || leftVisible ? "block" : "none",
              flexShrink: 0,
            }}
          >
            <Box
              style={{
                paddingLeft: 16,
                marginBottom: 16,
              }}
            >
              <VFButton
                variant="primary"
                buttonDisplay="withIcon"
                iconName="arrow-left"
                fullWidth
                onClick={() => navigate(`/projects/${projectId}/clips`)}
                style={{
                  padding: "10px 16px",
                }}
              >
                Back to my clips
              </VFButton>
            </Box>
            <EditorSettings
              ref={editorSettingsRef}
              aspectRatio={aspectRatioRef.current} // Use ref value instead of state
              onAspectRatioChange={handleAspectRatioChange}
              originalSubtitles={clipData?.subtitles?.items || null}
              sourceVideoMetadata={
                video?.media_metadata || {
                  width: 0,
                  height: 0,
                  duration_seconds: 0,
                  file_size_bytes: 0,
                  fps: 0,
                }
              }
            />
          </Box>
          <Box
            className={classes.centerSection}
            style={{
              width: !is744 ? "100%" : `${centerSectionWidth}px`,
              maxWidth: "100%",
            }}
            ref={centerSectionRef}
          >
            {!loading && !isDataReady && (
              <Box
                style={{
                  height: "400px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  flexDirection: "column",
                  gap: "10px",
                }}
              >
                <Text>Missing required data to display content</Text>
                <Box>
                  {!video && (
                    <Text size="xs" color="red">
                      No video data available
                    </Text>
                  )}
                  {video && !video.file_path && (
                    <Text size="xs" color="red">
                      Missing video file path
                    </Text>
                  )}
                  {video && !video.media_metadata && (
                    <Text size="xs" color="red">
                      Missing video metadata
                    </Text>
                  )}
                  {!clipData && (
                    <Text size="xs" color="red">
                      No clip data available
                    </Text>
                  )}
                  {clipData &&
                    (!clipData.segments || clipData.segments.length === 0) && (
                      <Text size="xs" color="red">
                        No segments in clip data
                      </Text>
                    )}
                </Box>
              </Box>
            )}
            {isDataReady && (
              <Box
                style={{
                  position: "relative",
                  width: "100%",
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "center",
                  maxHeight: "calc(100vh - 240px)",
                }}
              >
                {/* --- HOVER CONTAINER FOR PLAYER AND CROP BUTTON --- */}
                <Box
                  style={{
                    width: "100%",
                    maxWidth: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    height: "100%",
                    position: "relative",
                  }}
                  onMouseEnter={() => {
                    isPlayerHoveredRef.current = true;
                    updateCropButtonVisibility();
                  }}
                  onMouseLeave={() => {
                    isPlayerHoveredRef.current = false;
                    updateCropButtonVisibility();
                  }}
                >
                  {/* --- CROP BUTTON CONTAINER --- */}
                  <Box
                    ref={cropButtonVisibilityRef}
                    style={{
                      position: "absolute",
                      zIndex: 1000,
                      display: "none", // Initially hidden, updated by ref
                    }}
                  >
                    <CropButton
                      cropMode={cropMode}
                      setCropMode={setCropMode}
                      clipData={clipData}
                      setClipData={setClipData}
                    />
                  </Box>

                  {/* --- NOTIFICATION: SHOW FOR 10s AFTER LOAD, FADE OUT --- */}
                  <Transition
                    mounted={showNotification}
                    transition="fade"
                    duration={400}
                    timingFunction="ease"
                  >
                    {(styles) => (
                      <VFNotification
                        message={`Click and drag video captions to re-position`}
                        variant="default"
                        style={{
                          position: "absolute",
                          top: 40,
                          left: 0,
                          zIndex: 1000,
                          ...styles,
                        }}
                        closable
                      />
                    )}
                  </Transition>
                  {/* --- REMOTION PLAYER --- */}
                  {OptimizedPlayer || (
                    <Box
                      style={{
                        width: "100%",
                        height: Math.round(
                          centerSectionWidth *
                            (aspectRatio === "9:16"
                              ? 16 / 9
                              : aspectRatio === "1:1"
                              ? 1 / 1
                              : 9 / 16)
                        ),
                        maxHeight: "calc(100vh - 240px)",
                        background: "#1A1A1A",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        borderRadius: "4px",
                      }}
                    >
                      <Text color="#fafafa99">Preparing video player...</Text>
                    </Box>
                  )}
                </Box>
              </Box>
            )}
          </Box>
          <Box
            className={classes.rightSection}
            style={{
              width: `${rightSectionWidth}px`,
              display: is744 || rightVisible ? "block" : "none",
            }}
          >
            {isDataReady && (
              <Transcript fps={video?.media_metadata?.fps || 30} />
            )}
          </Box>
        </Box>
        <Box className={classes.bottomSection}>
          {isDataReady && (
            <>
              <TimelineWidthProvider>
                <TimelineZoomContext>
                  <DisplayTime />
                  <TimelineControls
                    playerState={playerStateForProps}
                    setPlayerState={setPlayerStateForProps}
                    videoMetadata={clipData?.media_metadata || null}
                  />
                  <Timeline sourceVideoUrl={video!.file_path} />
                </TimelineZoomContext>
              </TimelineWidthProvider>
            </>
          )}
        </Box>
      </ClipConfigProvider>
    </Box>
  );
});

export default Editor;
