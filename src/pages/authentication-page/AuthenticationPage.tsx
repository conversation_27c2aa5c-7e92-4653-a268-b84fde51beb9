import { AuthenticationForm } from "../../components/authentication-form/AuthenticationForm";
import { FC, useEffect } from "react";
import "./AuthenticationPage.scss";
import { useNavigate } from "react-router-dom";
import { RootState } from "../../store";
import { useSelector, useDispatch } from "react-redux";
import { resetAuthFailure } from "../../features/auth/authSlice";

export const AuthenticationPage: FC = () => {
  const { isAuthenticated } = useSelector((state: RootState) => state.auth);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated]);

  // Reset any authentication failure state when accessing the login/register page
  useEffect(() => {
    dispatch(resetAuthFailure());
  }, [dispatch]);

  return (
    <div className="page-holder">
      <AuthenticationForm />
    </div>
  );
};
