import {
  fetchVideo,
  selectVideoById,
  updateVideoTitle,
} from "../../features/videos/videosSlice";
import {
  ClipLoadingStatus,
  createClip,
  fetchVideoClips,
  selectClipsByVideoId,
} from "../../features/clips/clipsSlice";
import { AppDispatch, RootState, store } from "../../store";
import { IVideoClip } from "../../types";
import {
  createStyles,
  LoadingOverlay,
  MantineTheme,
  Box,
  Text,
  Stack,
  Image,
} from "@mantine/core";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import { ClipCard } from "../../components/clip-card/ClipCard";
import { VFButton } from "../../components/ui/Button";
import { VFTextbox } from "../../components/ui/Textbox";
import { VFSelector } from "../../components/ui/Selector";
import { getClipDurationInSeconds } from "../../utils";
import React from "react";
import emptyClipsImage from "../../assets/images/empty-clips.png";
import noSearchResultsImage from "../../assets/images/clips-no-search-results.png";
import { useErrorHandler } from "@/hooks/useErrorHandler";

// Create a memoized version of ClipCard to prevent unnecessary re-renders
const MemoizedClipCard = React.memo(ClipCard, (prevProps, nextProps) => {
  // Only re-render if these specific props change
  return (
    prevProps.id === nextProps.id && prevProps.videoId === nextProps.videoId
  );
});

const useStyles = createStyles((theme: MantineTheme) => ({
  root: {
    minHeight: "100vh",
    backgroundColor: theme.other["surface-main-bg-color"],
  },
  contentContainer: {
    maxWidth: 1440,
    padding: "60px 128px 80px",
    margin: "0 auto",

    [`@media (max-width: 1727px)`]: {
      padding: "60px 128px 80px",
    },

    [`@media (max-width: 1279px)`]: {
      padding: "60px 64px 80px",
    },

    [`@media (max-width: 743px)`]: {
      padding: "60px 16px 64px",
    },
  },
  clipsContainer: {
    [`@media (min-width: 1728px)`]: {
      maxWidth: "1476px",
      margin: "0 auto",
    },
    [`@media (min-width: 1280px) and (max-width: 1727px)`]: {
      maxWidth: "952px",
      margin: "0 auto",
    },
    [`@media (min-width: 744px) and (max-width: 1279px)`]: {
      maxWidth: "616px",
      margin: "0 auto",
    },
    [`@media (max-width: 743px)`]: {
      maxWidth: "312px",
      margin: "0 auto",
    },
  },
  projectTitleContainer: {
    display: "flex",
    flexWrap: "wrap", // Add this to allow wrapping
    justifyContent: "space-between",
    gap: "16px", // Add gap for consistent spacing when wrapped
    marginBottom: 48,

    [`@media (max-width: ${theme.breakpoints.sm})`]: {
      // Adjust breakpoint as needed
      justifyContent: "flex-start", // Align items to start when wrapped
    },
  },
  projectTitleHolder: {
    display: "flex",
    alignItems: "center",
    gap: 5,
    flexGrow: 1, // Allow it to grow but not push button to new line
    minWidth: "280px", // Force wrap when container gets too narrow
  },
  projectTitle: {
    color: theme.other["surface-text-color"],
    margin: 0,
    minWidth: 0,
    flex: 1,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  actionsContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  filterAndSortHolder: {
    display: "flex",
    gap: 8,
    marginBottom: 12,
  },
  actionHolder: {
    position: "relative",
    display: "inline-block",
  },
  clipsCount: {
    color: theme.other["surface-subtext-color"],
  },
  clipsGrid: {
    display: "grid",
    gap: 24,
    width: "100%",
    [`@media (min-width: 1728px)`]: {
      gridTemplateColumns: "repeat(6, 226px)",
      justifyContent: "center",
    },
    [`@media (min-width: 1280px) and (max-width: 1727px)`]: {
      gridTemplateColumns: "repeat(4, 226px)",
    },
    [`@media (min-width: 744px) and (max-width: 1279px)`]: {
      gridTemplateColumns: "repeat(2, 294px)",
    },
    [`@media (max-width: 743px)`]: {
      gridTemplateColumns: "312px",
    },
  },
  emptyState: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 64,

    [`@media (max-width: 1100px)`]: {
      flexDirection: "column",
      gap: 32,
    },
  },
  emptyStateImage: {
    maxWidth: 400,
    height: "auto",
  },
  emptyStateContent: {
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
  },
  emptyStateHeading: {
    fontSize: 64,
    fontWeight: 600,
    color: theme.other["surface-text-color"],
    marginBottom: 24,
    lineHeight: 1.2,

    [`@media (max-width: 1100px)`]: {
      fontSize: 48,
      textAlign: "center",
    },

    [`@media (max-width: 743px)`]: {
      fontSize: 36,
    },
  },
  emptyStateSubtext: {
    fontSize: 28,
    fontWeight: 600,
    color: theme.other["surface-text-color"],
    maxWidth: 500,
    lineHeight: 1.2,

    [`@media (max-width: 1100px)`]: {
      textAlign: "center",
      fontSize: 24,
    },

    [`@media (max-width: 743px)`]: {
      fontSize: 18,
    },
  },
}));

export const ClipManagement: React.FC = () => {
  const { classes, cx } = useStyles();
  const { projectId } = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const clipsStatus = useSelector(
    (state: RootState) => state.clips.loadingStatus
  );
  const navigate = useNavigate();
  const initialFetchRef = useRef(false);
  const project = useSelector((state: RootState) => {
    return selectVideoById(state, projectId as string);
  });
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [newTitle, setNewTitle] = useState(project?.title || "");
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const [filterValue, setFilterValue] = useState<string | null>(null);
  const [isSortMenuOpen, setIsSortMenuOpen] = useState(false);
  const [sortValue, setSortValue] = useState("virality");
  const [isLoading, setIsLoading] = useState(false);
  const isCreatingVideoRef = useRef(false);
  const [visibleCount, setVisibleCount] = useState(12);
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  const BATCH_SIZE = 12;
  const { handleError } = useErrorHandler();

  const clips: IVideoClip[] = useSelector((state: RootState) => {
    return selectClipsByVideoId(state, projectId as string);
  });

  // Use indexes for filtering and sorting to prevent unnecessary re-renders
  const filteredAndSortedIndexes = useMemo(() => {
    if (!clips || clips.length === 0) return [];

    // Create an array of indexes
    const indexes = clips.map((_, index) => index);

    // First, filter clips that have segments and segments length
    const segmentsFilteredIndexes = indexes.filter((index) => {
      const clip = clips[index];
      return clip.segments && clip.segments.length > 0;
    });

    console.log(clips)
    // Then, apply user-selected filtering if needed
    const filteredIndexes = filterValue
      ? segmentsFilteredIndexes.filter((index) => {
        switch (filterValue) {
          case "favorite":
            return clips[index].is_favorite === true;
          default:
            return true;
        }
      })
      : segmentsFilteredIndexes;

    // Then, sort the filtered indexes based on the sortValue
    switch (sortValue) {
      case "virality":
        return filteredIndexes.sort((a, b) => {
          // First prioritize manually created clips (null overall_score)
          if (
            clips[a].overall_score === null &&
            clips[b].overall_score !== null
          )
            return -1;
          if (
            clips[a].overall_score !== null &&
            clips[b].overall_score === null
          )
            return 1;
          // Then apply the original sorting logic
          return (clips[b].overall_score || 0) - (clips[a].overall_score || 0);
        });

      case "length":
        return filteredIndexes.sort((a, b) => {
          // First prioritize manually created clips (null overall_score)
          if (
            clips[a].overall_score === null &&
            clips[b].overall_score !== null
          )
            return -1;
          if (
            clips[a].overall_score !== null &&
            clips[b].overall_score === null
          )
            return 1;
          // Then apply the original sorting logic
          const durationA = getClipDurationInSeconds(clips[a].segments);
          const durationB = getClipDurationInSeconds(clips[b].segments);
          return durationB - durationA;
        });

      case "date":
        return filteredIndexes.sort((a, b) => {
          // First prioritize manually created clips (null overall_score)
          if (
            clips[a].overall_score === null &&
            clips[b].overall_score !== null
          )
            return -1;
          if (
            clips[a].overall_score !== null &&
            clips[b].overall_score === null
          )
            return 1;
          // Then apply the original sorting logic
          const dateFieldA = clips[a].created_at || 0;
          const dateFieldB = clips[b].created_at || 0;

          const timeA =
            typeof dateFieldA === "string"
              ? new Date(dateFieldA).getTime()
              : Number(dateFieldA);
          const timeB =
            typeof dateFieldB === "string"
              ? new Date(dateFieldB).getTime()
              : Number(dateFieldB);

          return timeB - timeA;
        });

      default:
        return filteredIndexes.sort((a, b) => {
          // First prioritize manually created clips (null overall_score)
          if (
            clips[a].overall_score === null &&
            clips[b].overall_score !== null
          )
            return -1;
          if (
            clips[a].overall_score !== null &&
            clips[b].overall_score === null
          )
            return 1;
          // Then apply the original sorting logic
          return (clips[b].overall_score || 0) - (clips[a].overall_score || 0);
        });
    }
  }, [clips, sortValue, filterValue]); // Add filterValue to dependencies

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        await dispatch(fetchVideo(projectId as string) as any);

        // Fetch clips from API
        await dispatch(fetchVideoClips(projectId as string) as any);

        setIsLoading(false);
      } catch (error: any) {
        // Error fetching clips
        setIsLoading(false);
        handleError(error.error, error.statusCode);
      }
    };

    if (!initialFetchRef.current) {
      fetchData();
      initialFetchRef.current = true;
    }
  }, [dispatch, projectId]);

  const handleSortChange = useCallback((value: string | null) => {
    if (value !== null) {
      setSortValue(value);
    }
    setIsSortMenuOpen(false);
  }, []);

  const handleFilterChange = useCallback((filterValue: string | null) => {
    setFilterValue(filterValue);
    setIsFilterMenuOpen(false);
  }, []);

  const handleCreateClipsManually = async () => {
    if (isCreatingVideoRef.current || !projectId) return;

    try {
      isCreatingVideoRef.current = true;
      await dispatch(createClip({ videoId: projectId }));
    } catch (error) {
      // Error creating clip
    } finally {
      isCreatingVideoRef.current = false;
    }
  };

  const sortOptions = useMemo(() => {
    return [
      {
        label: "Virality score",
        value: "virality",
      },
      {
        label: "Length",
        value: "length",
      },
      {
        label: "Date created",
        value: "date",
      },
    ];
  }, []);

  const filterOptions = useMemo(() => {
    return [
      {
        label: "Favorite",
        value: "favorite",
      },
    ];
  }, []);

  // Reset visibleCount when the number of filtered clips changes
  useEffect(() => {
    setVisibleCount(BATCH_SIZE);
  }, [filteredAndSortedIndexes.length]);

  // Use a callback ref for the sentinel to always observe the latest DOM node
  const observerRef = useRef<IntersectionObserver | null>(null);
  const setSentinelRef = useCallback(
    (node: HTMLDivElement | null) => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (node && visibleCount < filteredAndSortedIndexes.length) {
        observerRef.current = new window.IntersectionObserver(
          (entries) => {
            if (entries[0].isIntersecting) {
              // Sentinel intersected, loading more clips
              setVisibleCount((prev) =>
                Math.min(prev + BATCH_SIZE, filteredAndSortedIndexes.length)
              );
            } else {
              // Sentinel NOT intersecting
            }
          },
          {
            root: null,
            rootMargin: "200px",
            threshold: 0.01,
          }
        );
        observerRef.current.observe(node);
      }
    },
    [visibleCount, filteredAndSortedIndexes.length]
  );

  const handleTitleChange = useCallback(async () => {
    if (isEditingTitle) {
      await dispatch(
        updateVideoTitle({ title: newTitle, videoId: projectId as string })
      );
    }

    setIsEditingTitle(!isEditingTitle);
  }, [dispatch, isEditingTitle, newTitle, projectId]);

  let content;

  // setCurrentDuration(45 * 30); //! todo: get this from remotion
  // setCurrentFps(30);

  if (clipsStatus === ClipLoadingStatus.LOADING || isLoading || !clips) {
    content = (
      <LoadingOverlay
        visible={true}
        loaderProps={{ size: "lg", variant: "dots" }}
      />
    );
  } else if (clipsStatus === ClipLoadingStatus.SUCCEEDED) {
    if (clips.length === 0) {
      // Empty clips state - no clips yet
      content = (
        <Box className={classes.emptyState}>
          <Image
            src={emptyClipsImage}
            alt="No clips yet"
            className={classes.emptyStateImage}
          />
          <Box className={classes.emptyStateContent}>
            <Text className={classes.emptyStateHeading}>No clips yet</Text>
            <Text className={classes.emptyStateSubtext}>
              You'll be notified when we're done processing your video.
            </Text>
          </Box>
        </Box>
      );
    } else {
      // When clips exist, always show the action bar, even if filtered results are empty
      content = (
        <div className={classes.clipsContainer}>
          <div className={classes.projectTitleContainer}>
            <div className={classes.projectTitleHolder}>
              {!isEditingTitle && (
                <h3 className={classes.projectTitle}>{newTitle}</h3>
              )}
              {isEditingTitle && (
                <VFTextbox
                  value={newTitle}
                  onChange={(newTitle: string) => setNewTitle(newTitle)}
                />
              )}
              <VFButton
                variant="secondary"
                buttonDisplay="onlyIcon"
                iconName={isEditingTitle ? "check" : "edit"}
                onClick={handleTitleChange}
              />
              <span className={cx(classes.clipsCount, "p-regular")}>
                {filteredAndSortedIndexes.length} of {clips.length} clips
              </span>
            </div>
            <VFButton
              variant="primary"
              size="default"
              loading={isCreatingVideoRef.current}
              onClick={handleCreateClipsManually}
            >
              Create clip manually
            </VFButton>
          </div>
          <div className={classes.actionsContainer}>
            <div className={classes.filterAndSortHolder}>
              <div className={classes.actionHolder}>
                <VFButton
                  variant="secondary"
                  buttonDisplay="withIcon"
                  iconName="filter"
                  size="small"
                  onClick={() => {
                    setIsFilterMenuOpen(!isFilterMenuOpen);
                    setIsSortMenuOpen(false);
                  }}
                >
                  Filter
                </VFButton>
                <VFSelector
                  position="left"
                  value={filterValue}
                  isOpen={isFilterMenuOpen}
                  options={filterOptions}
                  onChange={handleFilterChange}
                  deselectable
                />
              </div>
              <div className={classes.actionHolder}>
                <VFButton
                  variant="secondary"
                  buttonDisplay="withIcon"
                  iconName="sort"
                  size="small"
                  onClick={() => {
                    setIsSortMenuOpen(!isSortMenuOpen);
                    // close filter menu so they dont stay open at the same time
                    setIsFilterMenuOpen(false);
                  }}
                >
                  Sort
                </VFButton>
                <VFSelector
                  value={sortValue}
                  isOpen={isSortMenuOpen}
                  options={sortOptions}
                  onChange={handleSortChange}
                />
              </div>
            </div>
            {/* <VFButton
              variant="secondary"
              buttonDisplay="withIcon"
              iconName="download"
              size="small"
            >
              Download all
            </VFButton> */}
          </div>

          {filteredAndSortedIndexes.length === 0 ? (
            // No search results state - show within the clips container
            <Box
              sx={{ marginTop: 80, display: "flex", justifyContent: "center" }}
            >
              <Box className={classes.emptyState}>
                <Image
                  src={noSearchResultsImage}
                  alt="No results found"
                  className={classes.emptyStateImage}
                />
                <Box className={classes.emptyStateContent}>
                  <Text className={classes.emptyStateHeading}>
                    No results found
                  </Text>
                  <Text className={classes.emptyStateSubtext}>
                    Try adjusting your filter settings again.
                  </Text>
                </Box>
              </Box>
            </Box>
          ) : (
            <div className={classes.clipsGrid}>
              {filteredAndSortedIndexes.slice(0, visibleCount).map((index) => {
                const clip = clips[index];
                return (
                  <MemoizedClipCard
                    id={clip.id}
                    videoId={projectId as string}
                    key={clip.id || `clip-${clip.video_id}-${index}`}
                  />
                );
              })}
              {/* Sentinel for lazy loading */}
              {visibleCount < filteredAndSortedIndexes.length && (
                <div
                  ref={setSentinelRef}
                  style={{
                    height: 60,
                    width: "100%",
                    background: "#ffeeba",
                    border: "1px dashed #f0ad4e",
                    margin: 8,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <span style={{ color: "#856404" }}>
                    Loading more clips...
                  </span>
                </div>
              )}
              {/* Fallback Load More button */}
              {visibleCount < filteredAndSortedIndexes.length && (
                <div
                  style={{
                    width: "100%",
                    display: "flex",
                    justifyContent: "center",
                    margin: 16,
                  }}
                >
                  <VFButton
                    variant="secondary"
                    onClick={() => {
                      setVisibleCount((prev) =>
                        Math.min(
                          prev + BATCH_SIZE,
                          filteredAndSortedIndexes.length
                        )
                      );
                    }}
                  >
                    Load More
                  </VFButton>
                </div>
              )}
            </div>
          )}
        </div>
      );
    }
  }

  return (
    <div className={classes.root}>
      <div className={classes.contentContainer}>
        {/* <div className={classes.uploadVideoFormHolder}>
          RENAME PROJECT ELEMENT + DOWNLOAD ALL CLIPS BUTTON
        </div> */}
        {content}
      </div>
    </div>
  );
};
