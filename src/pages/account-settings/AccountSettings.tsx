import React, { useEffect, useMemo, useState } from "react";
import { createStyles } from "@mantine/core";
import { VFTabs } from "../../components/ui/Tabs";
import { TabsValue } from "@mantine/core";
import BoostCounter from "../../components/ui/BoostCounter";
import { PricingForm } from "../../components/ui/PricingForm";
import BrandPresets from "../../components/brand-presets/BrandPresets";
import { ProfileDetailsForm } from "../../components/ui/ProfileDetailsForm";
import { RootState } from "../../store";
import { useDispatch, useSelector } from "react-redux";
import { getSubscriptionData } from "../../features/auth/authActions";
import { VFNotification } from "../../components/ui/Notification";
import { useViewportSize } from "@mantine/hooks";

const useStyles = createStyles((theme) => ({
  container: {
    backgroundColor: theme.other["surface-main-bg-color"],
    minHeight: "100vh",
    display: "flex",
    flexDirection: "column",
    position: "relative",
  },
  content: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    padding: "68px 20px",
    flex: 1,
    width: "100%", // Full width

    // Adjust padding for mobile
    [`@media (max-width: 576px)`]: {
      padding: "48px 16px",
    },
  },
  title: {
    color: theme.other["surface-text-color"],
    marginBottom: "48px",
    textAlign: "center",

    // Adjust font size for mobile
    [`@media (max-width: 576px)`]: {
      fontSize: "24px",
      marginBottom: "32px",
    },
  },
  notificationContainer: {
    width: "100%",
    marginBottom: "48px",
    opacity: 1,
    transition: "opacity 0.5s ease",

    // Adjust margin for mobile
    [`@media (max-width: 576px)`]: {
      marginBottom: "32px",
    },
  },
  notificationFadeOut: {
    opacity: 0,
  },
  tabsContainer: {
    display: "flex",
    gap: "32px",
    width: "100%",
    maxWidth: "1000px",

    // Adjust layout for tablets and mobile
    [`@media (max-width: 992px)`]: {
      flexDirection: "column",
      maxWidth: "100%", // Full width on mobile
      gap: "24px",
    },

    // Further adjust for mobile
    [`@media (max-width: 576px)`]: {
      gap: "16px",
    },
  },
  tabsPanel: {
    width: "800px",
    paddingLeft: 64,

    // Adjust for tablets and mobile
    [`@media (max-width: 992px)`]: {
      width: "100%",
      paddingLeft: 0,
      paddingTop: 32,
      maxWidth: "100%", // Full width on mobile
    },

    // Further adjust for mobile
    [`@media (max-width: 576px)`]: {
      paddingTop: 24,
    },
  },
  subscriptionContent: {
    display: "flex",
    flexDirection: "column",
    gap: "40px",
    width: "100%",
  },
  // Style horizontal tabs to match button group in design
  horizontalTabsContainer: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: "32px",

    // Add specific styles to match the button group in Figma design
    "& .mantine-Tabs-tabsList": {
      width: "100%",
      maxWidth: "100%", // Full width
      border: `1px solid ${theme.other["card-border-color"]}`,
      borderRadius: "4px",
      overflow: "hidden",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
    },
    "& .mantine-Tabs-tab": {
      flex: 1,
      textAlign: "center",
      fontFamily: "SF Pro Display, sans-serif",
      fontWeight: 600,
      fontSize: "16px",
      lineHeight: "1.25em",
      padding: "8px 4px", // Further reduced padding
      height: "36px",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      borderRadius: 0,
      borderWidth: 0,
      minWidth: 0, // Prevent tabs from having minimum width
      whiteSpace: "nowrap", // Prevent text wrapping inside tabs
    },
    "& .mantine-Tabs-tab h4": {
      // Target the h4 inside tabs
      margin: 0,
      padding: 0,
    },
    "& .mantine-Tabs-tab[data-active]": {
      backgroundColor: theme.colors.dark[6],
      color: theme.white,
    },
    // Force horizontal orientation
    "& .mantine-Tabs-root": {
      flexDirection: "column",
      width: "100%",
    },
    // Make tab panels full width
    "& .mantine-Tabs-panel": {
      width: "100%",
    },
  },
  tabContent: {
    backgroundColor: theme.other["main-bg-color"],
    border: `1px solid ${theme.other["card-border-color"]}`,
    borderRadius: "4px",
    padding: "16px",
    width: "100%", // Full width
    maxWidth: "100%", // Full width

    // For mobile, adjust padding
    [`@media (max-width: 576px)`]: {
      padding: "16px 12px",
    },
  },
}));

// Create a context to share notification state between components
export const NotificationContext = React.createContext<{
  showNotification: (message: string) => void;
}>({
  showNotification: () => {},
});

// Define interface for PricingForm props
interface PricingFormProps {
  onChangePlan?: () => void;
  onEditDetails?: () => void;
  onUpdateCard?: () => void;
}

export const AccountSettings: React.FC = () => {
  const { classes, cx } = useStyles();
  const [activeTab, setActiveTab] = useState<TabsValue>("account");
  const { userData } = useSelector((state: RootState) => state.auth);
  const subscriptionData = useSelector(
    (state: RootState) => state.auth.subscriptionData
  );
  const { width } = useViewportSize();
  // Force horizontal orientation on mobile/tablet
  const isTabletOrMobile = width <= 992;
  const tabOrientation = isTabletOrMobile ? "horizontal" : "vertical";

  // Notification state
  const [notification, setNotification] = useState<string | null>(null);
  const [isFading, setIsFading] = useState(false);

  const dispatch = useDispatch();
  const hasFetched = React.useRef(false);

  // Handle notification fade out and removal with smooth transition
  useEffect(() => {
    if (notification) {
      const fadeTimer = setTimeout(() => {
        setIsFading(true);
      }, 4500); // Start fading out after 4.5 seconds

      const removeTimer = setTimeout(() => {
        setNotification(null);
        setIsFading(false);
      }, 5000); // Remove after 5 seconds (after fade completes)

      return () => {
        clearTimeout(fadeTimer);
        clearTimeout(removeTimer);
      };
    }
  }, [notification]);

  // Function to show notification
  const showNotification = (message: string) => {
    setIsFading(false);
    setNotification(message);
  };

  if (!userData) {
    return <div>Loading...</div>;
  }

  // Handler functions for PricingForm
  const handleChangePlan = () => {
    console.log("Change plan clicked");
  };

  const handleEditDetails = () => {
    console.log("Edit details clicked");
  };

  const handleUpdateCard = () => {
    console.log("Update card clicked");
  };

  // Handler for tab changes
  const handleTabChange = (value: TabsValue) => {
    setActiveTab(value);
  };

  useEffect(() => {
    if (!hasFetched.current) {
      if (!subscriptionData) {
        dispatch(getSubscriptionData() as any);
      }

      hasFetched.current = true;
    }
  }, [dispatch, subscriptionData]);

  return (
    <NotificationContext.Provider value={{ showNotification }}>
      <div className={classes.container}>
        <div className={classes.content}>
          <h2 className={classes.title}>Settings</h2>

          {notification && (
            <div
              className={cx(classes.notificationContainer, {
                [classes.notificationFadeOut]: isFading,
              })}
            >
              <VFNotification message={notification} variant="success" />
            </div>
          )}

          <div className={classes.tabsContainer}>
            <VFTabs
              value={activeTab}
              onTabChange={handleTabChange}
              orientation={tabOrientation}
              placement={isTabletOrMobile ? undefined : "left"}
              className={
                isTabletOrMobile ? classes.horizontalTabsContainer : undefined
              }
              style={{ width: "100%" }} // Force full width
            >
              <VFTabs.List position={isTabletOrMobile ? "center" : undefined}>
                <VFTabs.Tab value="account">ACCOUNT</VFTabs.Tab>
                <VFTabs.Tab value="subscription">SUBSCRIPTION</VFTabs.Tab>
                <VFTabs.Tab value="brand-presets">BRAND PRESETS</VFTabs.Tab>
              </VFTabs.List>

              <VFTabs.Panel value="account">
                <div
                  className={cx(
                    classes.tabsPanel,
                    isTabletOrMobile && classes.tabContent
                  )}
                >
                  <ProfileDetailsForm
                    initialUsername={userData?.full_name}
                    initialEmail={userData?.email}
                    onUpdateSuccess={() =>
                      showNotification("Settings successfully updated.")
                    }
                  />
                </div>
              </VFTabs.Panel>
              <VFTabs.Panel value="subscription">
                <div
                  className={cx(
                    classes.tabsPanel,
                    isTabletOrMobile && classes.tabContent
                  )}
                >
                  <div className={classes.subscriptionContent}>
                    <BoostCounter />
                    <PricingForm />
                  </div>
                </div>
              </VFTabs.Panel>
              <VFTabs.Panel value="brand-presets">
                <div
                  className={cx(
                    classes.tabsPanel,
                    isTabletOrMobile && classes.tabContent
                  )}
                >
                  <BrandPresets key={`brand-presets-${activeTab}`} />
                </div>
              </VFTabs.Panel>
            </VFTabs>
          </div>
        </div>
      </div>
    </NotificationContext.Provider>
  );
};

export default AccountSettings;
