export const extentedMantineTheme = {
  "other": {
    "btn-destructive-bg-color": "var(--btn-destructive-bg-color)",
    "btn-destructive-border-color": "var(--btn-destructive-border-color)",
    "btn-destructive-disabled-bg-color": "var(--btn-destructive-disabled-bg-color)",
    "btn-destructive-hover-bg-color": "var(--btn-destructive-hover-bg-color)",
    "btn-destructive-text-color": "var(--btn-destructive-text-color)",
    "btn-floating-contextmenu-bg-color": "var(--btn-floating-contextmenu-bg-color)",
    "btn-floating-contextmenu-border-color": "var(--btn-floating-contextmenu-border-color)",
    "btn-primary-bg-color": "var(--btn-primary-bg-color)",
    "btn-primary-border-color": "var(--btn-primary-border-color)",
    "btn-primary-disable-bg-color": "var(--btn-primary-disable-bg-color)",
    "btn-primary-hover-bg-color": "var(--btn-primary-hover-bg-color)",
    "btn-primary-text-color": "var(--btn-primary-text-color)",
    "btn-secondary-border-color": "var(--btn-secondary-border-color)",
    "btn-secondary-disabled-text-color": "var(--btn-secondary-disabled-text-color)",
    "btn-secondary-focus-border-color": "var(--btn-secondary-focus-border-color)",
    "btn-secondary-hover-bg-color": "var(--btn-secondary-hover-bg-color)",
    "btn-secondary-text-color": "var(--btn-secondary-text-color)",
    "captions-highlight-default": "var(--captions-highlight-default)",
    "captions-text-color": "var(--captions-text-color)",
    "captions-text-stroke-color": "var(--captions-text-stroke-color)",
    "card-border-color": "var(--card-border-color)",
    "card-text-color": "var(--card-text-color)",
    "checkbox-bg-color-false": "var(--checkbox-bg-color-false)",
    "checkbox-bg-color-true": "var(--checkbox-bg-color-true)",
    "checkbox-border-color": "var(--checkbox-border-color)",
    "checkbox-check-color": "var(--checkbox-check-color)",
    "checkbox-disabled-color": "var(--checkbox-disabled-color)",
    "checkbox-focus-color": "var(--checkbox-focus-color)",
    "checkbox-hover-color": "var(--checkbox-hover-color)",
    "checkbox-invalid-color": "var(--checkbox-invalid-color)",
    "checkbox-label-color": "var(--checkbox-label-color)",
    "color-picker-bg-color": "var(--color-picker-bg-color)",
    "color-picker-border-color": "var(--color-picker-border-color)",
    "dialog-bg-color": "var(--dialog-bg-color)",
    "dialog-border-color": "var(--dialog-border-color)",
    "dialog-text-color": "var(--dialog-text-color)",
    "expander-bg-color-default": "var(--expander-bg-color-default)",
    "expander-bg-color-hover": "var(--expander-bg-color-hover)",
    "expander-bottom-border-color": "var(--expander-bottom-border-color)",
    "expander-focus-border-color": "var(--expander-focus-border-color)",
    "expander-text-color-default": "var(--expander-text-color-default)",
    "expander-text-color-disabled": "var(--expander-text-color-disabled)",
    "label-default-border-color": "var(--label-default-border-color)",
    "label-default-border-error-color": "var(--label-default-border-error-color)",
    "label-default-progress-color": "var(--label-default-progress-color)",
    "label-default-text-color": "var(--label-default-text-color)",
    "label-default-text-error-color": "var(--label-default-text-error-color)",
    "label-small-default-bg-color": "var(--label-small-default-bg-color)",
    "label-small-default-text-color": "var(--label-small-default-text-color)",
    "label-small-high-virality-bg-color": "var(--label-small-high-virality-bg-color)",
    "label-small-low-virality-bg-color": "var(--label-small-low-virality-bg-color)",
    "label-small-virality-text-color": "var(--label-small-virality-text-color)",
    "menu-bg-color": "var(--menu-bg-color)",
    "menu-border-color": "var(--menu-border-color)",
    "menu-menu-item-disabled-text-color": "var(--menu-menu-item-disabled-text-color)",
    "menu-menu-item-error-color": "var(--menu-menu-item-error-color)",
    "menu-menu-item-focus-border-color": "var(--menu-menu-item-focus-border-color)",
    "menu-menu-item-hover-bg-color": "var(--menu-menu-item-hover-bg-color)",
    "menu-menu-item-subtitle-color": "var(--menu-menu-item-subtitle-color)",
    "menu-menu-item-text-color": "var(--menu-menu-item-text-color)",
    "notification-default-bg-color": "var(--notification-default-bg-color)",
    "notification-default-border-color": "var(--notification-default-border-color)",
    "notification-default-text-color": "var(--notification-default-text-color)",
    "notification-error-bg-color": "var(--notification-error-bg-color)",
    "notification-error-border-color": "var(--notification-error-border-color)",
    "notification-error-text-color": "var(--notification-error-text-color)",
    "notification-success-bg-color": "var(--notification-success-bg-color)",
    "notification-success-border-color": "var(--notification-success-border-color)",
    "notification-success-text-color": "var(--notification-success-text-color)",
    "progressbar-progress-color": "var(--progressbar-progress-color)",
    "progressbar-track-color": "var(--progressbar-track-color)",
    "selector-bg-color": "var(--selector-bg-color)",
    "selector-border-color": "var(--selector-border-color)",
    "selector-selector-item-disabled-text-color": "var(--selector-selector-item-disabled-text-color)",
    "selector-selector-item-focus-border-color": "var(--selector-selector-item-focus-border-color)",
    "selector-selector-item-hover-bg-color": "var(--selector-selector-item-hover-bg-color)",
    "selector-selector-item-selected-bg-color": "var(--selector-selector-item-selected-bg-color)",
    "selector-selector-item-selected-text-color": "var(--selector-selector-item-selected-text-color)",
    "selector-selector-item-subtitle-color": "var(--selector-selector-item-subtitle-color)",
    "selector-selector-item-text-color": "var(--selector-selector-item-text-color)",
    "surface-bg-color": "var(--surface-bg-color)",
    "surface-border-color": "var(--surface-border-color)",
    "surface-main-bg-color": "var(--surface-main-bg-color)",
    "surface-subtext-color": "var(--surface-subtext-color)",
    "surface-text-color": "var(--surface-text-color)",
    "switch-bg-color-off": "var(--switch-bg-color-off)",
    "switch-bg-color-on": "var(--switch-bg-color-on)",
    "switch-border-color-off": "var(--switch-border-color-off)",
    "switch-border-color-on": "var(--switch-border-color-on)",
    "switch-disabled-color": "var(--switch-disabled-color)",
    "switch-focus-border-color": "var(--switch-focus-border-color)",
    "switch-handle-color-off": "var(--switch-handle-color-off)",
    "switch-handle-color-on": "var(--switch-handle-color-on)",
    "switch-hover-color": "var(--switch-hover-color)",
    "table-border-color": "var(--table-border-color)",
    "table-header-text-color": "var(--table-header-text-color)",
    "table-text-color": "var(--table-text-color)",
    "tabs-default-bg-color-hover": "var(--tabs-default-bg-color-hover)",
    "tabs-default-text-color": "var(--tabs-default-text-color)",
    "tabs-focus-border-color": "var(--tabs-focus-border-color)",
    "tabs-selected-bg-color": "var(--tabs-selected-bg-color)",
    "tabs-selected-bg-color-hover": "var(--tabs-selected-bg-color-hover)",
    "tabs-selected-text-color": "var(--tabs-selected-text-color)",
    "textbox-bg-color": "var(--textbox-bg-color)",
    "textbox-border-color": "var(--textbox-border-color)",
    "textbox-disabled-text-color": "var(--textbox-disabled-text-color)",
    "textbox-focus-border-color": "var(--textbox-focus-border-color)",
    "textbox-hint-text-color": "var(--textbox-hint-text-color)",
    "textbox-hover-border-color": "var(--textbox-hover-border-color)",
    "textbox-invalid-color": "var(--textbox-invalid-color)",
    "textbox-placeholder-text-color": "var(--textbox-placeholder-text-color)",
    "textbox-text-color": "var(--textbox-text-color)",
    "textbox-valid-color": "var(--textbox-valid-color)",
    "timeline-active-video-marker-bg-color": "var(--timeline-active-video-marker-bg-color)",
    "timeline-audio-bg-color": "var(--timeline-audio-bg-color)",
    "timeline-audio-graph-color": "var(--timeline-audio-graph-color)",
    "timeline-current-marker-bg-color": "var(--timeline-current-marker-bg-color)",
    "timeline-video-inner-border": "var(--timeline-video-inner-border)",
    "timeline-video-marker-color": "var(--timeline-video-marker-color)",
    "timeline-video-outer-border": "var(--timeline-video-outer-border)",
    "tooltip-bg-color": "var(--tooltip-bg-color)",
    "tooltip-text-color": "var(--tooltip-text-color)",
    "uploader-bg-color": "var(--uploader-bg-color)",
    "uploader-border-color": "var(--uploader-border-color)",
    "uploader-error-color": "var(--uploader-error-color)",
    "uploader-subtext-color": "var(--uploader-subtext-color)",
    "uploader-text-color": "var(--uploader-text-color)",
    "video-preview-bg-color": "var(--video-preview-bg-color)",
    "video-preview-content-color": "var(--video-preview-content-color)"
  }
};