import {
  ISubtitle,
  ISubtitles,
  PresetConfig,
  IVideoSegment,
} from "./remotion/types";

export interface IvideoFile {
  video: File;
}

export interface IVideo {
  subtitles?: ISubtitle;
  created_at: string;
  compressed_file_path: string;
  file_path: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  processing_status: ProcessingStatus;
  title: string;
  updated_at: string;
  id: string;
}

export interface IVideoClip {
  created_at: string;
  file_path: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  rendering_status: ProcessingStatus;
  rendering_error: any;
  segments: IVideoSegment[];
  subtitles: ISubtitles;
  overall_score: number;
  is_favorite: boolean;
  title: string;
  updated_at: string;
  video_id: string;
  id: string;
  preset_id: string;
}

export interface IVideoMetadata {
  duration_seconds: number;
  file_size_bytes: number;
  height: number;
  width: number;
  fps: number;
}

export interface IResponseMeEndpoint {
  email: string;
  id: string;
}

export interface IVideoPayload {
  source_type: string;
  file?: any;
  url?: string;
}

export interface ILoginPayload {
  grant_type?: string;
  username: string;
  password: string;
  scope?: string;
  client_id?: string;
  client_secret?: string;
}

export interface IUserData {
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
  roles: string[];
  email_confirmed: boolean;
  stripe_customer_id: string;
  subscription_level: number;
  boost: number;
  full_name: string;
  marketing_consent: boolean;
  default_preset_id: string;
}
export interface IRegisterPayload {
  email: string;
  password: string;
  captcha_token: string;
  full_name: string;
  marketing_consent: boolean;
}
export interface IToolConfig {
  enabled: boolean;
  key: string;
  title: string;
  showFor: "project" | "edit" | "both";
  options?: IToolOption[] | null;
  tabs?: IToolTab[] | null;
}

export interface IToolOption {
  key: string;
  type: "color-picker" | "number" | "select" | "text" | "radio-group";
  items?: string[] | IRadioInput[] | null;
  label: string;
  defaultValue: string | number;
  minValue?: number;
  step?: number;
  value?: string | number | null;
  description?: string;
  placeholder: string;
  showFor: "project" | "edit" | "both";
}

export interface IToolTab {
  title: string;
  options: IToolOption[] | ICaption[];
  data?: any;
  showFor: "project" | "edit" | "both";
}

export interface ICreateClipPayload {
  segments: IVideoSegment[];
  title?: string;
  subtitles: ISubtitles | null;
}

export interface ICaptionsConfig {
  size: number;
  font: string;
  color: string;
  stroke_color: string;
  stroke_width: number;
  position: "top" | "center" | "bottom";
  words_per_line?: number; // not available when editing cut
}

export interface IEditCutPayload {
  captions: ICaption[];
  captions_config: {
    size?: number;
    font: string;
    color: string;
    stroke_color: string;
    stroke_width: number;
    position: string;
  };
}

export interface ICaption {
  start: number;
  end: number;
  text: string;
}

export interface IRadioInput {
  value: string;
  label: string;
}

export interface IClipSizeOption {
  icon: string;
  id: number;
  label: string;
  value: string;
  size: string;
  description?: string;
}

export enum ProcessingStatus {
  SUCCESS = "SUCCESS",
  ERROR = "ERROR",
  PROCESSING = "PROCESSING",
  DRAFT = "DRAFT",
}

export enum SSEEventTypes {
  VIDEO_PROCESSED = "video.processed",
  CLIP_PROCESSED = "clip.processed",
  ERROR = "error",
  SUBSCRIPTION_UPGRADED = "subscription.upgraded",
}

export interface ClipProcessedEventData {
  id: string;
  created_at: string;
  updated_at: string | null;
  title: string;
  file_path: string;
  rendering_status: string;
  video_id: string;
  media_metadata: IVideoMetadata;
  thumbnail_path: string;
  subtitles: ISubtitle[];
  segments: IVideoSegment[];
}

export interface TimelineZoomLevel {
  index: number;
  interval: number; // Interval between markers on the timeline
  spaceBetweenIntervals: number; // Space between intervals on the timeline in pixels
  default: boolean;
}

export enum ErrorResponseDetailType {
  OUT_OF_BOOST = "out_of_boost",
  OUT_OF_STORAGE = "out_of_storage",
}

export interface ErrorResponseDetail {
  msg: string;
  type: ErrorResponseDetailType;
}

export interface ErrorResponse {
  detail: ErrorResponseDetail[] | string;
  statusCode: number;
}
