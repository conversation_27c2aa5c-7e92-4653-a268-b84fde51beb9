{"name": "vidfast-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.10.5", "@mantine/core": "^5.9.5", "@mantine/dropzone": "^5.9.5", "@mantine/form": "^5.9.5", "@mantine/hooks": "^5.9.5", "@mantine/notifications": "^5.9.5", "@microsoft/fetch-event-source": "^2.0.1", "@reduxjs/toolkit": "^1.9.1", "@remotion/cli": "4.0.273", "@remotion/media-parser": "4.0.273", "@remotion/media-utils": "4.0.273", "@remotion/player": "4.0.273", "@remotion/transitions": "4.0.273", "@types/react-redux": "^7.1.24", "konva": "^9.3.1", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-edit-text": "^5.1.0", "react-ga4": "^2.1.0", "react-gtm-module": "^2.0.11", "react-hotjar": "^6.3.1", "react-konva": "^18.2.10", "react-loader-spinner": "^6.1.6", "react-microsoft-clarity": "^1.2.0", "react-redux": "^8.0.5", "react-resizable": "^3.0.5", "react-router-dom": "^6.6.0", "react-turnstile": "^1.1.4", "react-window": "^1.8.10", "remotion": "4.0.273", "sass": "^1.69.4", "video-react": "^0.16.0", "vite-plugin-pwa": "^1.0.0", "workbox-cacheable-response": "^7.3.0", "workbox-expiration": "^7.3.0", "workbox-range-requests": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0"}, "devDependencies": {"@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/react-gtm-module": "^2.0.4", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^3.0.0", "typescript": "^4.9.3", "vite": "^4.0.0"}}